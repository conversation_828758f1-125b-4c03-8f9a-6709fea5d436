import * as express from "express";
import config from "./config";
import { measures } from "@skywind-group/sw-utils";
import { pipeline } from "node:stream/promises";

const compression = require("compression");
const bodyParser = require("body-parser");
const methodOverride = require("method-override");
const cookieParser = require("cookie-parser");

export function create(setUp?: (app: express.Application) => void): express.Application {

    const app: express.Application = express();
    app.use(measures.measureProvider.instrumentFunction(cookieParser(), "express.cookieParser") as any);
    app.use(measures.measureProvider.instrumentFunction(compression({
        threshold: config.compressionThreshold
    }), "express.compression") as any);
    app.use(measures.measureProvider.instrumentFunction(bodyParser.urlencoded({
        extended: true, limit: config.bodyParserUrlLimit
    }), "express.urlencoded") as any);
    app.use(measures.measureProvider.instrumentFunction(bodyParser.json({
        limit: config.bodyParserJsonLimit
    }), "express.jsonBodyParser") as any);

    app.use(methodOverride());
    allowHTTPMethods(app);
    app.disable("x-powered-by");

    if (setUp) {
        setUp(app);
    }

    return app;
}

function isPrometheusMonitoring() {
    return measures.providerName === "prometheus";
}

function isMemoryMonitoring() {
    return measures.providerName === "memory";
}

export function setUpMetricHandlers(app: express.Application) {
    if (isPrometheusMonitoring()) {
        app.get("/metrics", async (req: express.Request, res: express.Response) => {
            measures.measureProvider.setTransaction("Prometheus metrics");
            res.setHeader("Content-Type", "text/plain");
            const metrics = await measures.measureProvider.getMeasuresStream();
            await pipeline(metrics, res);
        });
    } else if (isMemoryMonitoring()) {
        app.use("/v1/", require("./api/measures").default);
    }
}

export function allowHTTPMethods(app: express.Application) {
    const allowedMethods = new Set(config.allowedHTTPMethods);
    app.use((req: express.Request, res: express.Response, next: express.NextFunction) => {
        const method = req.method.toUpperCase();
        res.header("Access-Control-Allow-Methods", config.allowedHTTPMethods.join(","));
        if (!allowedMethods.has(method)) {
            res.status(405).send({
                statusCode: 405,
                message: `${method} not allowed.`,
                error: "Method Not Allowed"
            });
        }

        return next();
    });
}
