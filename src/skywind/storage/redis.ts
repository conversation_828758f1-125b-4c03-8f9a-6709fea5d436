import config from "../config";
import { Redis as RedisClient } from "ioredis";
import { lazy, redis } from "@skywind-group/sw-utils";
import * as fs from "fs";

export type RedisClientPool = redis.RedisPool<RedisClient>;
export type RedisClientFactory = redis.RedisClientFactory<RedisClient>;

let pool: RedisClientPool;
export function getRedisPool(): RedisClientPool {
    if (!pool) {
        pool = redis.createRedisPool<RedisClient>(config.redis);
    }
    return pool;
}

export const remoteTickerPool = lazy((): RedisClientPool => redis.createRedisPool<RedisClient>(config.remoteTicker.redis));
export const walletRedisPool = lazy((): RedisClientPool => redis.createRedisPool<RedisClient>(config.walletRedis));

export function get(): Promise<RedisClient> {
    return getRedisPool().get();
}

export function release(client: RedisClient): void {
    getRedisPool().release(client);
}

export async function waitForSync(client: RedisClient): Promise<void> {
    return getRedisPool().waitForSync(client);
}

export function create(): RedisClient {
    return (redis.getRedisFactory(config.redis) as RedisClientFactory).createClient();
}

export async function usingDb<T>(callback: (client: RedisClient) => Promise<T>): Promise<T> {
    const client = await get();
    try {
        return await callback(client);
    } finally {
        release(client);
    }
}

const scripts: { [path: string]: string } = {};

export async function loadScript(client: RedisClient, path: string): Promise<string> {
    const script = fs.readFileSync(path, "utf8");
    const scriptSha = await new Promise<string>((resolve, reject) => {
        client.script("LOAD", script, (err, sha) => {
            if (err) {
                reject(err);
            } else {
                resolve(sha as string);
            }
        });
    });
    scripts[path] = scriptSha;
    return scriptSha;
}

export async function execScript(client, path: string, keys, args): Promise<any> {
    let scriptSha = scripts[path];
    if (!scriptSha) {
        scriptSha = await loadScript(client, path);
    }
    return new Promise((resolve, reject) => {
        client.evalsha([scriptSha, keys.length, ...keys, ...args], (err, reply) => {
            if (err) {
                reject(err);
            } else {
                resolve(reply);
            }
        });
    });
}
