"use strict";

import { logging } from "@skywind-group/sw-utils";
import { splitEnvParameters } from "./utils/envParse";

let serverName: string = process.env.SERVER_NAME || "JPN";

function logQueries(sql: string, timing?: number) {
    logging.logger("sequelize").info(sql);
}

const config = {

    environment: process.env.NODE_ENV || "development",

    isProduction: (): boolean => {
        return config.environment === "production";
    },

    disableSwagger: process.env.DISABLE_SWAGGER === "true",

    region: process.env.REGION_CODE || "default",

    server: {
        timeout: +process.env.SERVER_TIMEOUT || 120000,
        keepAliveTimeout: +process.env.HTTP_SERVER_KEEP_ALIVE_TIMEOUT || 0,
        setName: (name: string) => {
            serverName = name;
        },
        getName: (): string => {
            return serverName;
        }
    },

    internalServer: {
        port: +process.env.INTERNAL_SERVER_PORT || 4004,
    },

    token: {
        secret: process.env.SECRET_KEY || "_secret_",
    },

    accessToken: {
        secret: process.env.ACCESS_TOKEN_SECRET || "k06u_Lq8-DfxLNacIh14tiWfOLLsyRz5v-DGw-hrarnp3tIKE1QZSdaJahH5fJgh",
    },

    redis: {
        host: process.env.REDIS_HOST || "redis",
        port: +process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD,
        minConnections: +process.env.REDIS_MIN_CONNECTIONS || 2,
        maxConnections: +process.env.REDIS_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.REDIS_MAX_IDLE_TIME_MS || 30000,
        retryMaxDelay: +process.env.REDIS_RETRY_MAX_DELAY_MS || 1000,
        connectionTimeout: +process.env.REDIS_CONNECTION_TIMEOUT || 5000,
        sentinels: JSON.parse(process.env.REDIS_SENTINELS || null),
        sentinelUsername: process.env.REDIS_SENTINEL_USERNAME,
        sentinelPassword: process.env.REDIS_SENTINEL_PASSWORD,
        clusterName: process.env.REDIS_CLUSTER_NAME || "redis-ha",
        replicationFactor: +process.env.REDIS_REPLICATION_FACTOR || 0,
        replicationTimeout: +process.env.REDIS_REPLICATION_TIMEOUT || 100,
        maxRetriesPerRequest: +process.env.REDIS_MAX_RETRIERS_PER_REQUEST || 0,
        showFriendlyErrorStack: process.env.REDIS_SHOW_FRIENDLY_ERROR_STACK === "true",
    },

    walletRedis: {
        host: process.env.WALLET_REDIS_HOST || "redis",
        port: +process.env.WALLET_REDIS_PORT || 6379,
        password: process.env.WALLET_REDIS_PASSWORD,
        minConnections: +process.env.WALLET_REDIS_MIN_CONNECTIONS || 2,
        maxConnections: +process.env.WALLET_REDIS_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.WALLET_REDIS_MAX_IDLE_TIME_MS || 30000,
        retryMaxDelay: +process.env.WALLET_REDIS_RETRY_MAX_DELAY_MS || 1000,
        connectionTimeout: +process.env.WALLET_REDIS_CONNECTION_TIMEOUT || 5000,
        sentinels: JSON.parse(process.env.WALLET_REDIS_SENTINELS || null),
        sentinelUsername: process.env.WALLET_REDIS_SENTINEL_USERNAME,
        sentinelPassword: process.env.WALLET_REDIS_SENTINEL_PASSWORD,
        clusterName: process.env.WALLET_REDIS_CLUSTER_NAME || "redis-ha",
        replicationFactor: +process.env.WALLET_REDIS_REPLICATION_FACTOR || 0,
        replicationTimeout: +process.env.WALLET_REDIS_REPLICATION_TIMEOUT || 100,
        maxRetriesPerRequest: +process.env.WALLET_REDIS_MAX_RETRIERS_PER_REQUEST || 0,
        showFriendlyErrorStack: process.env.WALLET_REDIS_SHOW_FRIENDLY_ERROR_STACK === "true",
    },

    trxIdRange: process.env.WALLET_TRX_ID_MIN || process.env.WALLET_TRX_ID_MAX ? {
        min: process.env.WALLET_TRX_ID_MIN,
        max: process.env.WALLET_TRX_ID_MAX
    } : undefined,

    logLevel: process.env.LOG_LEVEL || "info",

    graylog: {
        host: process.env.GRAYLOG_HOST || undefined,
        port: +process.env.GRAYLOG_PORT || undefined,
    },

    db: {
        database: process.env.PGDATABASE || "jackpot",
        user: process.env.PGUSER,
        password: process.env.PGPASSWORD,
        host: process.env.PGHOST || "db",
        port: +process.env.PGPORT || 5432,
        ssl: {
            isEnabled: process.env.PG_SECURE_CONNECTION === "true",
            ca: process.env.PG_CA_CERT || "./ca.pem",
        },
        maxConnections: +process.env.PG_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.PG_MAX_IDLE_TIME_MS || 30000,
        syncOnStart: process.env.SYNC_ON_START === "true",
    },

    walletArchiveDb: {
        database: process.env.JPN_ARCHIVE_PGDATABASE,
        user: process.env.JPN_ARCHIVE_PGUSER,
        password: process.env.JPN_ARCHIVE_PGPASSWORD,
        host: process.env.JPN_ARCHIVE_PGHOST,
        port: +process.env.JPN_ARCHIVE_PGPORT,
        ssl: {
            isEnabled: process.env.JPN_ARCHIVE_PG_SECURE_CONNECTION === "true",
            ca: process.env.PG_CA_CERT || "./ca.pem",
        },
        maxConnections: +process.env.JPN_ARCHIVE_PG_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.JPN_ARCHIVE_PG_MAX_IDLE_TIME_MS || 30000,
        schema: process.env.JPN_ARCHIVE_PGSCHEMA || "public",
    },

    queryLogging: process.env.POSTGRES_QUERY_LOGGING === "true" ? logQueries : () => {
        // do nothing
    },

    trxStoragePrefix: "jp",

    recentTrxStorage: {
        // hours. store last recently create transactions. Default is 3 hour
        TTL: +process.env.RECENT_TRX_TTL || 3,
        // cron to instantiate clean up job
        cleanUpCron: process.env.RECENT_TRX_CRON || "0 * * * *",
        // timestamp interval for a cleanup iteration (ms). Default is a minute
        iterationPeriod: +process.env.RECENT_TRX_CLEANUP_ITERATION_PERIOD || 60 * 1000,
        // pause between iteration
        iterationPause: +process.env.RECENT_TRX_CLEANUP_ITERATION_PAUSE || 50,
    },

    unloader: {
        // (minutes) max . Default is 15 minutes
        workerMaxOrphanTTL: +process.env.MAX_ORPHAN_TTL || 15,
        // cron to instantiate repairment of orphan worker lists
        repairWorkerCron: process.env.REPAIR_WORKER_CRON || "*/20 * * * *",
        // max batch size for unloader
        maxBatchSize: +process.env.UNLOADER_MAX_BATCH_SIZE || 100,
        // max interval to await batch
        maxSpinPopDuration: +process.env.MAX_SPIN_POP_DURATION || 500,
        // sleep interval in spinloop if we didn't get data
        spinPopSleepDuration: +process.env.SPIN_POP_SLEEP_DURATION || 100
    },

    historyUnloader: {
        // how often to update worker alive timestamp
        aliveSec: process.env.HISTORY_WORKER_ALIVE || 30,
        // ttl when worker considered to be stale
        aliveTTLSec: process.env.HISTORY_WORKER_ALIVE_TTL || 120,
        // schedule to repair stale workers
        repairCron: process.env.HISTORY_WORKER_REPAIR_CRON || "*/20 * * * *",
        // max batch size for unloader
        maxBatchSize: +process.env.UNLOADER_MAX_BATCH_SIZE || 1000,
    },

    newrelic: {
        enabled: process.env.NEWRELIC_ENABLED !== "false", // enabled by default
        envName: process.env.NEWRELIC_ENV_NAME || "development",
        appName: process.env.NEW_RELIC_APP_NAME || "JPN-SERVER-API",
        key: process.env.NEWRELIC_KEY || "b8cc24d3ec86dad4c9295a1f798f4f5e4571728a",
    },

    kafkaUnloader: {
        enabled: process.env.KAFKA_UNLOADER_ENABLED === "true",
        topicName: process.env.TOPIC_NAME || "jpn-trx-log",
        kafkaBrokerHostnames: process.env.KAFKA_BROKER_HOSTNAMES || "localhost:9092",
        requireAck: +process.env.KAFKA_REQUIRE_ACK || -1,
        ackTimeoutMs: +process.env.KAFKA_ACK_TIMEOUT || 1000,
        clientCreationTimeout: +process.env.KAFKA_CONNECT_TIMEOUT || 6000,
        requestTimeout: +process.env.KAFKA_REQUEST_TIMEOUT || 5000,
        partitionerType: +process.env.KAFKA_PARTITIONER_TYPE || 2,
        maxSendAttemptTimeout: +process.env.KAFKA_MAX_SEND_ATTEMPT_TIMEOUT || 10000,
    },

    maxWinRetry: +process.env.MAX_WIN_RETRY || 3,
    winResolveRetryTimeout: +process.env.WIN_RESOLVE_RETRY_TIMEOUT || 100,
    maxWinResolveRetry: +process.env.MAX_WIN_RESOLVE_RETRY || 50,

    bodyParserJsonLimit: process.env.BODY_PARSER_JSON_LIMIT || "5mb",
    bodyParserUrlLimit: process.env.BODY_PARSER_URL_LIMIT || "5mb",
    compressionThreshold: process.env.COMPRESSION_THRESHOLD || "1kb",

    keepAliveTimeout: +process.env.KEEP_ALIVE_TIMEOUT || 0,

    createJpTypesOnStart: process.env.CREATE_JP_TYPES_ON_START === "true" || false,

    tickerServer: {
        port: +(process.env.TICKER_SERVER_PORT || 5001)
    },
    corsWhitelist: {
        game: process.env.GAME_CORS_WHITELIST // comma-separated list of allowed origins for /game endpoint requests
    },

    internalServerToken: {
        expiresIn: +process.env.INTERNAL_SERVER_TOKEN_EXPIRES_IN || 300, // 5 minutes
        algorithm: process.env.INTERNAL_SERVER_TOKEN_ALGORITHM || "HS256",
        issuer: process.env.INTERNAL_SERVER_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.INTERNAL_SERVER_SECRET || "TU8N9oP4pPfrUMaRYkjwBsOyw0hgg39sPsTjONrgnN1ErJbn2"
    },

    remoteApiServer: {
        port: +process.env.REMOTE_API_SERVER_PORT || 5002,
        keepAlive: {
            maxFreeSockets: +(process.env.REMOTE_API_KEEP_ALIVE_FREE_SOCKET_COUNT || 100),
            freeSocketKeepAliveTimeout: +process.env.REMOTE_API_KEEP_ALIVE_TIMEOUT || 30000,
            socketActiveTTL: +process.env.REMOTE_API_SOCKET_ACTIVE_TTL || 60000,
        }
    },

    remoteTicker: {
        redis: {
            host: process.env.REMOTE_TICKER_REDIS_HOST || "redis",
            port: +process.env.REMOTE_TICKER_REDIS_PORT || 6379,
            password: process.env.REMOTE_TICKER_REDIS_PASSWORD,
            minConnections: +process.env.REMOTE_TICKER_REDIS_MIN_CONNECTIONS || 2,
            maxConnections: +process.env.REMOTE_TICKER_REDIS_MAX_CONNECTIONS || 10,
            maxIdleTime: +process.env.REMOTE_TICKER_REDIS_MAX_IDLE_TIME_MS || 30000,
            retryMaxDelay: +process.env.REMOTE_TICKER_REDIS_RETRY_MAX_DELAY_MS || 1000,
            connectionTimeout: +process.env.REMOTE_TICKER_REDIS_CONNECTION_TIMEOUT || 5000,
            sentinels: JSON.parse(process.env.REMOTE_TICKER_REDIS_SENTINELS || null),
            sentinelUsername: process.env.REMOTE_TICKER_REDIS_SENTINEL_USERNAME,
            sentinelPassword: process.env.REMOTE_TICKER_REDIS_SENTINEL_PASSWORD,
            clusterName: process.env.REMOTE_TICKER_REDIS_CLUSTER_NAME || "redis-ha",
            replicationFactor: +process.env.REMOTE_TICKER_REDIS_REPLICATION_FACTOR || 0,
            replicationTimeout: +process.env.REMOTE_TICKER_REDIS_REPLICATION_TIMEOUT || 100,
            maxRetriesPerRequest: +process.env.REMOTE_TICKER_REDIS_MAX_RETRIERS_PER_REQUEST || 0,
            showFriendlyErrorStack: process.env.REMOTE_TICKER_REDIS_SHOW_FRIENDLY_ERROR_STACK === "true",
        },

        forceRefreshTimeout: +process.env.REMOTE_TICKER_REFRESH_TIMEOUT || 5000,
        tickerPrefix: process.env.REMOTE_TICKER_PREFIX || "remote-ticker",
        tickerRetries: {
            sleep: +(process.env.REMOTE_TICKER_RETRIES_SLEEP_TIMEOUT || "50"),
            maxTimeout: +process.env.REMOTE_TICKER_RETRIES_MAX_TIMEOUT || 5000
        },
    },

    trxIdPool: {
        minLength: +process.env.TRX_ID_POOL_MIN_LENGTH || 10,
        maxLength: +process.env.TRX_ID_POOL_MAX_LENGTH || 1000,
        loadFactor: +process.env.TRX_ID_POOL_LOAD_FACTOR || 1.5,
        refreshTimeout: (+process.env.TRX_ID_POOL_REFRESH_TIMEOUT_SEC || 0) * 1000 // switched off by default
    },

    remoteRequestQueue: {
        queuePrefix: process.env.REMOTE_REQUEST_QUEUE_PREFIX || "remote-request",
        retransmitTimeout: (+process.env.REMOTE_REQUEST_QUEUE_RETRANSMIT_TIMEOUT_SEC || 10) * 1000,
        retransmitMaxTimeout: (+process.env.REMOTE_REQUEST_QUEUE_RETRANSMIT_MAX_TIMEOUT_SEC || 3600) * 1000
    },

    autoCreateTestJp: {
        prefix: process.env.AUTO_CREATE_TEST_JP_PREFIX || "",
        suffix: process.env.AUTO_CREATE_TEST_JP_SUFFIX || "_test"
    },

    loggingOutput: (process.env.LOGGING_OUTPUT_TYPE || process.env.GRAYLOG_HOST && "graylog" || "console") as any,

    toEURMultiplier: {
        enableCustomTicker: process.env.ENABLE_CUSTOM_TICKER === "true",
        version: +process.env.TO_EUR_MULTIPLIER_VERSION || 2,
        cacheInvalidateMin: +process.env.TO_EUR_MULTIPLIER_CACHNE_INVALIDATE_MIN || 30
    },

    gameProviderAdapterUri: {
        PP: process.env.GAMEPROVIDER_ADAPTER_PATEPLAY || "http://localhost:3250/"
    },
    enableSuperagentLib: process.env.ENABLE_SUPERAGENT_LIB === "true",
    allowedHTTPMethods: splitEnvParameters(
        process.env.ALLOWED_HTTP_METHODS,
        ["GET", "POST", "OPTIONS", "DELETE", "PATCH", "PUT", "HEAD"]
    ),
};

export default config;
