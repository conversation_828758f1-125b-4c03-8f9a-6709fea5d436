import { ITrxDataConsumer, ITrxData } from "@skywind-group/sw-wallet";
import * as Sequelize from "sequelize";

export abstract class BaseConsumer implements ITrxDataConsumer {

    public abstract process(dbTransaction: Sequelize.Transaction, bulk: ITrxData[]): Promise<void>;

    protected parseJackpotWalletKey(walletKey: string): { jackpotId: string, currency: string } {
        const items = walletKey.split(":");
        return { jackpotId: items[1], currency: items[2] };
    }

    protected toMajorUnits(amount: number, precision: number): number {
        return amount / precision;
    }
}
