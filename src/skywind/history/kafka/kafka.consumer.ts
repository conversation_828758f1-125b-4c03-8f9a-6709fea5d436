import { ITrxData } from "@skywind-group/sw-wallet";
import * as Sequelize from "sequelize";
import { BaseConsumer } from "../base.consumer";
import { kafka } from "@skywind-group/sw-utils";

export class KafkaConsumer extends BaseConsumer {
    constructor(private kafkaWriter: kafka.KafkaWriter) {
        super();
    }

    public async process(dbTransaction: Sequelize.Transaction, bulk: ITrxData[]): Promise<void> {
        return this.kafkaWriter.sendMessages(bulk.map(i => JSON.stringify(i)));
    }
}
