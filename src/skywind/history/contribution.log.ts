import { InferAttributes, InferCreationAttributes, Model, ModelStatic, DataTypes, fn } from "sequelize";
import db from "../services/db.service";
import { DBTransaction } from "@skywind-group/sw-wallet";
import { JackpotOperationStatus } from "@skywind-group/sw-jpn-core";

const TABLE_NAME_REGEXP = new RegExp("\\$tableName", "g");

const UPSERT_CONTRIBUTION_LOG_WITH_DELETE = `
WITH cte_item AS (
  SELECT
    trx_id,
    trx_date::timestamp with time zone,
    external_id,
    remote_trx_id,
    remote_trx_region,
    jackpot_id,
    pool,
    currency,
    seed::NUMERIC,
    progressive::NUMERIC,
    player_seed::NUMERIC,
    player_progressive::NUMERIC,
    brand_id,
    region,
    game_id,
    player_code,
    player_currency,
    contribution_amount::NUMERIC,
    currency_rate::NUMERIC,
    game_data::JSONB,
    game_code,
    round_id::BIGINT,
    inserted_at::DATE,
    total_seed::NUMERIC,
    total_progressive::NUMERIC,
    status::enum_jackpot_operation_status
  FROM
    (
      VALUES
        (
          :trxId, :trxDate, :externalId, :remoteTrxId, :remoteTrxRegion,
          :jackpotId, :pool, :currency, :seed, :progressive,
          :playerSeed, :playerProgressive,
          :brandId, :region, :gameId, :playerCode, :playerCurrency,
          :contributionAmount, :currencyRate, :gameData, :gameCode, :roundId, :insertedAt,
          :totalSeed, :totalProgressive, :status
        )
    ) AS t (
      trx_id, trx_date, external_id, remote_trx_id, remote_trx_region,
      jackpot_id, pool, currency, seed, progressive,
      player_seed, player_progressive,
      brand_id, region, game_id, player_code, player_currency,
      contribution_amount, currency_rate, game_data, game_code, round_id, inserted_at,
      total_seed, total_progressive, status
    )
),
cte_del AS (
  DELETE FROM
    $tableName AS t USING cte_item AS n
  WHERE
    t.trx_id = n.trx_id AND t.jackpot_id = n.jackpot_id AND t.pool = n.pool
  RETURNING t.*
),
cte_ins AS (
  INSERT INTO $tableName (
    id, trx_id, trx_date, external_id, remote_trx_id, remote_trx_region,
    jackpot_id, pool, currency, seed, progressive,
    player_seed, player_progressive,
    brand_id, region, game_id, player_code, player_currency,
    contribution_amount, currency_rate, game_data, game_code, round_id, inserted_at,
    total_seed, total_progressive, status
  )
  SELECT
    COALESCE(d.id, nextval((select pg_get_serial_sequence('$tableName', 'id'))::regclass)),
    i.trx_id,
    CASE WHEN d.status IS NULL OR i.status != 'pending' THEN i.trx_date ELSE d.trx_date END,
    COALESCE(d.external_id, i.external_id),
    i.remote_trx_id,
    i.remote_trx_region,
    i.jackpot_id,
    i.pool,
    i.currency,
    i.seed,
    i.progressive,
    i.player_seed,
    i.player_progressive,
    i.brand_id,
    i.region,
    i.game_id,
    i.player_code,
    i.player_currency,
    i.contribution_amount,
    i.currency_rate,
    i.game_data,
    i.game_code,
    i.round_id,
    COALESCE(d.inserted_at, NOW()),
    i.total_seed,
    i.total_progressive,
    CASE WHEN d.status IS NULL OR i.status != 'pending' THEN i.status ELSE d.status END
  FROM
    cte_item AS i
  LEFT JOIN cte_del AS d ON d.trx_id = i.trx_id AND d.pool = i.pool
  RETURNING round_id
)
SELECT round_id FROM cte_ins;
`;

export interface ContributionLog {
    trxId: string;
    trxDate: Date;
    externalId: string;
    remoteTrxId?: string;
    remoteTrxRegion?: string;
    jackpotId: string;
    pool: string;
    currency: string;
    seed: number;
    progressive: number;
    playerSeed: number;
    playerProgressive: number;
    brandId: number;
    region: string;
    gameId: string;
    playerCode: string;
    playerCurrency: string;
    contributionAmount: number;
    currencyRate: number;
    gameData: any;
    gameCode: string;
    roundId: string;
    insertedAt?: Date;
    totalSeed: number;
    totalProgressive: number;
    status?: JackpotOperationStatus;
}

export interface ContributionLogDBInstance extends Model<
    InferAttributes<ContributionLogDBInstance>,
    InferCreationAttributes<ContributionLogDBInstance>>, ContributionLog {
}
export type IContributionLogModel = ModelStatic<ContributionLogDBInstance>;

export class ContributionLogDb {

    private model: IContributionLogModel;
    private readonly upsertWithDeleteQuery: string;

    constructor(tableName: string, skipColumns: string[] = []) {
        const attributes = {
            trxId: { type: DataTypes.CHAR(28), field: "trx_id", allowNull: false },
            trxDate: { type: "timestamp without time zone", field: "trx_date", allowNull: false },
            externalId: { type: DataTypes.STRING, field: "external_id" },
            remoteTrxId: { type: DataTypes.CHAR(28), field: "remote_trx_id", allowNull: true },
            remoteTrxRegion: { type: DataTypes.STRING, field: "remote_trx_region", allowNull: true },
            jackpotId: { type: DataTypes.STRING, field: "jackpot_id", allowNull: false },
            pool: { type: DataTypes.STRING, field: "pool", allowNull: false },
            currency: { type: DataTypes.CHAR(3), field: "currency", allowNull: false },
            seed: { type: DataTypes.DECIMAL, field: "seed", allowNull: false },
            progressive: { type: DataTypes.DECIMAL, field: "progressive", allowNull: false },
            playerSeed: { type: DataTypes.DECIMAL, field: "player_seed" },
            playerProgressive: { type: DataTypes.DECIMAL, field: "player_progressive" },
            brandId: { type: DataTypes.INTEGER, field: "brand_id", allowNull: false },
            region: { type: DataTypes.STRING, field: "region" },
            gameId: { type: DataTypes.STRING, field: "game_id", allowNull: false },
            playerCode: { type: DataTypes.STRING, field: "player_code", allowNull: false },
            playerCurrency: { type: DataTypes.CHAR(3), field: "player_currency", allowNull: false },
            contributionAmount: { type: DataTypes.DECIMAL, field: "contribution_amount", allowNull: false },
            currencyRate: { type: DataTypes.DECIMAL, field: "currency_rate", allowNull: false },
            gameData: { type: DataTypes.JSONB, field: "game_data", allowNull: false },
            gameCode: { type: DataTypes.STRING, field: "game_code", allowNull: false },
            roundId: { type: DataTypes.BIGINT, field: "round_id" },
            insertedAt: { field: "inserted_at", type: DataTypes.DATE, allowNull: false,
            defaultValue: fn("NOW") },
            totalSeed: { type: DataTypes.DECIMAL, field: "total_seed", allowNull: false },
            totalProgressive: { type: DataTypes.DECIMAL, field: "total_progressive", allowNull: false },
            status: { type: "enum_jackpot_operation_status", field: "status", allowNull: true }
        };

        for (const column of skipColumns) {
            delete attributes[column];
        }
        this.model = db.define<ContributionLogDBInstance, ContributionLog>(
            tableName,
            attributes,
            {
                freezeTableName: true,
                timestamps: false,
                indexes: [
                    { name: `idx_${tableName}_unique`, unique: true, fields: ["trx_id", "jackpot_id", "pool"] },
                    {
                        name: `idx_${tableName}_remote_trx_id`,
                        fields: ["remote_trx_id"]
                    }
                ],
            }
        );
        this.upsertWithDeleteQuery = UPSERT_CONTRIBUTION_LOG_WITH_DELETE.replace(TABLE_NAME_REGEXP, tableName);
    }

    public async sync(): Promise<any> {
        return this.model.sync();
    }

    public async save(log: ContributionLog, dbTransaction?: DBTransaction): Promise<any> {
        if (log.status) {
            log.externalId = log.externalId || null;
            log.remoteTrxId = log.remoteTrxId || null;
            log.remoteTrxRegion = log.remoteTrxRegion || null;
            log.insertedAt = log.insertedAt || null;
            log.status = log.status || null;
            log.gameData = log.gameData ? JSON.stringify(log.gameData) : null;
            log.playerSeed = log.playerSeed || null;
            log.playerProgressive = log.playerProgressive || null;

            const rs = await db.query(
                this.upsertWithDeleteQuery,
                { replacements: log as any, transaction: dbTransaction }
            );
            return !!rs.length;
        } else {
            return this.model.create(log, { transaction: dbTransaction });
        }
    }

    public async saveBatch(logs: ContributionLog[], dbTransaction?: DBTransaction): Promise<any> {
        const logsWithoutStatuses = logs.filter(log => !log.status);
        if (logsWithoutStatuses.length) {
            await this.model.bulkCreate(logsWithoutStatuses, { transaction: dbTransaction });
        }
        const logsWithStatuses = logs.filter(log => log.status);
        for (const log of logsWithStatuses) {
            await this.save(log, dbTransaction);
        }
    }

    public async find(trxId: string, pool: string): Promise<ContributionLog> {
        const data = await this.model.findOne({
            where: {
                trxId: trxId,
                pool: pool,
            },
        });
        return data ? data.toJSON() : undefined;
    }

    public async findAll(): Promise<ContributionLog[]> {
        const data = await this.model.findAll();
        return data.map(v => v.toJSON());
    }
}

export const contributionLogDb: ContributionLogDb = new ContributionLogDb("jp_contribution_log");
export const remoteContributionLogDb: ContributionLogDb = new ContributionLogDb("remote_jp_contribution_log", ["status"]);
