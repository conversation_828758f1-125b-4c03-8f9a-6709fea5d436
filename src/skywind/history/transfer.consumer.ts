import { ITrxData } from "@skywind-group/sw-wallet";
import * as Sequelize from "sequelize";
import { BaseConsumer } from "./base.consumer";
import {
    BaseWalletWinParams,
    InternalTransfer,
    LOCAL_WALLET_OPERATION_NAME,
    REMOTE_WALLET_OPERATION_NAME,
    WalletWinPayout
} from "../modules/walletParams";
import { remoteTransferLogDb, TransferLog, transferLogDb } from "./transfer.log";

export class TransferConsumer extends BaseConsumer {

    public async process(dbTransaction: Sequelize.Transaction, bulk: ITrxData[]): Promise<void> {
        const logs = [];
        const remoteLogs = [];
        for (const trx of bulk) {
            if (this.hasWinTransfer(trx)) {
                const params: BaseWalletWinParams = trx.operation.params;
                const storage = params.fromRemote ? remoteLogs : logs;
                storage.push(...this.parseWinTransfer(trx, trx.operation.params.wins));
            } else if (this.isPoolTransfer(trx)) {
                logs.push(...this.parsePoolTransfer(trx));
            } else if (this.isInternalTransfer(trx)) {
                logs.push(...this.parseWinTransfer(trx, trx.operation.params.transfers));
            }
        }
        await transferLogDb.saveBatch(logs, dbTransaction);
        await remoteTransferLogDb.saveBatch(remoteLogs, dbTransaction);
    }

    private hasWinTransfer(trx: ITrxData): boolean {
        return trx.operation && trx.data.length && trx.operation.params
            && (trx.operation.operationName === LOCAL_WALLET_OPERATION_NAME.release
                || trx.operation.operationName === REMOTE_WALLET_OPERATION_NAME.release)
            && !!(trx.operation.params as BaseWalletWinParams).wins.find((win) => win.transferPool !== undefined);
    }

    private parseWinTransfer(trx: ITrxData, transfers: WalletWinPayout[] | InternalTransfer[]): TransferLog[] {
        const logs = [];
        const params: BaseWalletWinParams = trx.operation.params;
        const wallet = this.parseJackpotWalletKey(trx.data[0].walletKey);
        for (const win of transfers) {
            if (win.transferPool === undefined) {
                continue;
            }
            const log: TransferLog = {
                trxId: trx.id,
                trxDate: trx.committedAt,
                externalId: trx.operation.externalTrxId,
                remoteTrxId: params.remoteTrxId,
                remoteTrxRegion: params.remoteTrxRegion,
                jackpotId: wallet.jackpotId,
                currency: wallet.currency,
                fromPool: win.pool,
                toPool: win.transferPool,
                seed: win.seed || 0,
                progressive: win.progressive || 0,
                fromPoolSeed: win.totalSeed || 0,
                fromPoolProgressive: win.totalProgressive || 0,
                toPoolSeed: win.transferPoolSeed || 0,
                toPoolProgressive: win.transferPoolProgressive || 0,
                gameId: params.gameId,
            };
            logs.push(log);
        }
        return logs;
    }

    private isPoolTransfer(trx: ITrxData): boolean {
        return trx.operation && trx.operation.operationName === "pool_transfer" && trx.operation.params;
    }

    private parsePoolTransfer(trx: ITrxData): TransferLog[] {
        const wallet = this.parseJackpotWalletKey(trx.data[0].walletKey);
        const transferLog: TransferLog = {
            trxId: trx.id,
            trxDate: trx.committedAt,
            externalId: trx.operation.externalTrxId,
            remoteTrxId: null,
            remoteTrxRegion: null,
            jackpotId: wallet.jackpotId,
            currency: wallet.currency,
            seed: 0,
            progressive: 0,
            fromPool: undefined,
            fromPoolSeed: 0,
            fromPoolProgressive: 0,
            toPool: undefined,
            toPoolSeed: 0,
            toPoolProgressive: 0,
            gameId: trx.operation.params.gameId,
        };
        const precision = trx.operation.params.precision;
        for (const change of trx.data) {
            if (change.trxType === "take") {
                transferLog.fromPool = change.account;
                if (change.property === "seed") {
                    transferLog.fromPoolSeed = this.toMajorUnits(change.prevValue, precision);
                    transferLog.seed = Math.abs(this.toMajorUnits(change.amount, precision));
                } else if (change.property === "progressive") {
                    transferLog.fromPoolProgressive = this.toMajorUnits(change.prevValue, precision);
                    transferLog.progressive = Math.abs(this.toMajorUnits(change.amount, precision));
                }
            } else if (change.trxType === "give") {
                transferLog.toPool = change.account;
                if (change.property === "seed") {
                    transferLog.toPoolSeed = this.toMajorUnits(change.prevValue, precision);
                } else if (change.property === "progressive") {
                    transferLog.toPoolProgressive = this.toMajorUnits(change.prevValue, precision);
                }
            }
        }
        if (!transferLog.fromPool || !transferLog.toPool) {
            return [];
        }

        let loanLog: TransferLog;
        const loanChanges = trx.data.filter((change) => change.trxType === "take_loan");
        if (loanChanges.length) {
            const loanFromPool = loanChanges[0].account;
            loanLog = loanFromPool === transferLog.fromPool ? transferLog : {
                ...transferLog,
                fromPool: loanFromPool,
                fromPoolSeed: 0,
                fromPoolProgressive: 0,
                seed: 0,
                progressive: 0
            };
            for (const change of loanChanges) {
                if (change.property === "seed") {
                    loanLog.fromPoolSeed = this.toMajorUnits(change.prevValue, precision);
                    loanLog.seed += Math.abs(this.toMajorUnits(change.amount, precision));
                } else if (change.property === "progressive") {
                    loanLog.fromPoolProgressive = this.toMajorUnits(change.prevValue, precision);
                    loanLog.progressive += Math.abs(this.toMajorUnits(change.amount, precision));
                }
            }
        }

        return loanLog && loanLog !== transferLog ? [transferLog, loanLog] : [transferLog];
    }

    private isInternalTransfer(trx: ITrxData): boolean {
        return trx.operation && trx.data.length && trx.operation.params
            && trx.operation.operationName === LOCAL_WALLET_OPERATION_NAME.internalTransfer
            && trx.operation.params.transfers && trx.operation.params.transfers.length;
    }
}
