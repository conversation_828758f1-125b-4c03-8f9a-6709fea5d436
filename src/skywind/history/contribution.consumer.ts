import { ITrxData } from "@skywind-group/sw-wallet";
import * as Sequelize from "sequelize";
import { BaseConsumer } from "./base.consumer";
import { ContributionLog, contributionLogDb, remoteContributionLogDb } from "./contribution.log";
import { Contribution } from "@skywind-group/sw-jpn-core";
import {
    BaseWalletContributionParams,
    ContributionPayout,
    LOCAL_WALLET_OPERATION_NAME,
    META_INF_ACCOUNT,
    REMOTE_WALLET_OPERATION_NAME,
    RemoteWalletContributionParams
} from "../modules/walletParams";

export class ContributionConsumer extends BaseConsumer {

    public async process(dbTransaction: Sequelize.Transaction, bulk: ITrxData[]): Promise<void> {
        const logs = [];
        const remoteLogs = [];
        const allowedLocalJPOperations = [
            LOCAL_WALLET_OPERATION_NAME.contribute,
            LOCAL_WALLET_OPERATION_NAME.contributeConfirmation,
            LOCAL_WALLET_OPERATION_NAME.contributeRollback
        ] as string[];
        for (const trx of bulk) {
            if (trx.operation && trx.data.length && trx.operation.params && trx.operation.params.brandId) {
                const params: BaseWalletContributionParams = trx.operation.params;
                const storage = params.fromRemote ? remoteLogs : logs;
                if (allowedLocalJPOperations.includes(trx.operation.operationName)) {
                    storage.push(...this.parseContributionLogs(trx));
                } else if (trx.operation.operationName === REMOTE_WALLET_OPERATION_NAME.contribute) {
                    storage.push(...this.parseRemoteContributionLogs(trx));
                }
            }
        }
        await contributionLogDb.saveBatch(logs, dbTransaction);
        await remoteContributionLogDb.saveBatch(remoteLogs, dbTransaction);
    }

    private parseContributionLogs(trx: ITrxData): ContributionLog[] {
        const logs: ContributionLog[] = [];
        const pools: { [pool: string]: ContributionLog } = {};
        const precision = trx.operation.params.precision;
        if (!precision) {
            // should never happen :)
            throw new Error("Invalid contribution log data - precision is missing");
        }
        for (const data of trx.data) {
            const pool = data.account;
            if (pool !== META_INF_ACCOUNT) {
                let log = pools[pool];
                if (!log) {
                    log = this.newContributionLog(trx, data.walletKey);
                    log.pool = pool;
                    logs.push(log);
                    pools[pool] = log;
                }
                if (data.property === "seed") {
                    log.seed = this.toMajorUnits(data.amount, precision);
                } else if (data.property === "progressive") {
                    log.progressive = this.toMajorUnits(data.amount, precision);
                }
            }
        }

        const playerContributions = trx?.operation?.params?.playerContributions as Contribution[];
        if (playerContributions) {
            for (const c of playerContributions) {
                 const log = pools[c.pool];
                if (log) {
                    log.playerSeed = c.seed;
                    log.playerProgressive = c.progressive;
                }
            }
        }

        const contributions = trx?.operation?.params?.contributions as ContributionPayout[];
        if (contributions) {
            for (const c of contributions) {
                const log = pools[c.pool];
                if (log) {
                    log.totalSeed = c.totalSeed || 0;
                    log.totalProgressive = c.totalProgressive || 0;
                    log.status = c.status;
                    if (!log.seed) {
                        log.seed = c.seed || 0;
                    }
                    if (!log.progressive) {
                        log.progressive = c.progressive || 0;
                    }
                }
            }
        }
        return logs;
    }

    private parseRemoteContributionLogs(trx: ITrxData): ContributionLog[] {
        const params: RemoteWalletContributionParams = trx.operation.params;
        if (!params.contributions) {
            return [];
        }

        const walletKey = trx.data[0].walletKey;
        const logs: ContributionLog[] = [];
        for (const contribution of params.contributions) {
            const log = this.newContributionLog(trx, walletKey);
            log.pool = contribution.pool;
            log.seed = contribution.seed || 0;
            log.progressive = contribution.progressive || 0;
            log.totalSeed = contribution.totalSeed || 0;
            log.totalProgressive = contribution.totalProgressive || 0;

            const playerContribution = params.playerContributions && params.playerContributions
                .find((v) => (v.pool === contribution.pool));
            if (playerContribution) {
                log.playerSeed = playerContribution.seed || 0;
                log.playerProgressive = playerContribution.progressive || 0;
            }
            logs.push(log);
        }
        return logs;
    }

    private newContributionLog(trx: ITrxData, walletKey: string): ContributionLog {
        const params: BaseWalletContributionParams = trx.operation.params;
        const wallet = this.parseJackpotWalletKey(walletKey);
        return {
            trxId: trx.id,
            trxDate: trx.committedAt,
            jackpotId: wallet.jackpotId,
            currency: wallet.currency,
            externalId: trx.operation.externalTrxId,
            remoteTrxId: params.remoteTrxId,
            remoteTrxRegion: params.remoteTrxRegion,
            brandId: params.brandId,
            region: params.region,
            gameId: params.gameId,
            playerCode: params.playerCode,
            playerCurrency: params.playerCurrency,
            contributionAmount: params.contributionAmount,
            currencyRate: params.currencyRate,
            gameData: params.gameData,
            gameCode: params.gameCode,
            roundId: params.roundId,
            pool: undefined,        // will be defined later
            seed: 0,                // will be defined later
            progressive: 0,        // will be defined later
            playerSeed: 0,         // will be defined later
            playerProgressive: 0,  // will be defined later
            totalSeed: 0,          // will be defined later
            totalProgressive: 0,   // will be defined later
        };
    }
}
