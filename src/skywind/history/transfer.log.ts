import { InferAttributes, InferCreationAttributes, Model, ModelStatic, DataTypes } from "sequelize";
import db from "../services/db.service";
import { DBTransaction } from "@skywind-group/sw-wallet";

export interface TransferLog {
    trxId: string;
    trxDate: Date;
    externalId: string;
    remoteTrxId?: string;
    remoteTrxRegion?: string;
    jackpotId: string;
    gameId: string;
    fromPool: string;
    toPool: string;
    currency: string;
    seed: number;
    progressive: number;
    fromPoolSeed: number;
    fromPoolProgressive: number;
    toPoolSeed: number;
    toPoolProgressive: number;
}

export interface TransferLogDBInstance extends Model<
    InferAttributes<TransferLogDBInstance>,
    InferCreationAttributes<TransferLogDBInstance>>, TransferLog {
}
export type ITransferLogModel = ModelStatic<TransferLogDBInstance>;
export class TransferLogDb {

    private model: ITransferLogModel;

    constructor(tableName: string) {
        const attributes = {
            trxId: { type: DataTypes.CHAR(28), field: "trx_id", allowNull: false },
            trxDate: { type: "timestamp without time zone", field: "trx_date", allowNull: false },
            externalId: { type: DataTypes.STRING, field: "external_id" },
            remoteTrxId: { type: DataTypes.CHAR(28), field: "remote_trx_id", allowNull: true },
            remoteTrxRegion: { type: DataTypes.STRING, field: "remote_trx_region", allowNull: true },
            jackpotId: { type: DataTypes.STRING, field: "jackpot_id", allowNull: false },
            gameId: { type: DataTypes.STRING, field: "game_id", allowNull: false },
            fromPool: { type: DataTypes.STRING, field: "from_pool", allowNull: false },
            toPool: { type: DataTypes.STRING, field: "to_pool" },
            currency: { type: DataTypes.CHAR(3), field: "currency", allowNull: false },
            seed: { type: DataTypes.DECIMAL, field: "seed", allowNull: false },
            progressive: { type: DataTypes.DECIMAL, field: "progressive", allowNull: false },
            fromPoolSeed: { type: DataTypes.DECIMAL, field: "from_pool_seed", allowNull: false },
            fromPoolProgressive: { type: DataTypes.DECIMAL, field: "from_pool_progressive", allowNull: false },
            toPoolSeed: { type: DataTypes.DECIMAL, field: "to_pool_seed" },
            toPoolProgressive: { type: DataTypes.DECIMAL, field: "to_pool_progressive" },
        };
        this.model = db.define<TransferLogDBInstance, TransferLog>(
            tableName,
            attributes,
            {
                freezeTableName: true,
                timestamps: false,
                indexes: [
                    {
                        name: `idx_${tableName}_unique`,
                        unique: true,
                        fields: ["trx_id", "jackpot_id", "from_pool", "to_pool"]
                    },
                    {
                        name: `idx_${tableName}_remote_trx_id`,
                        fields: ["remote_trx_id"]
                    }
                ],
            }
        );
    }

    public async sync(): Promise<any> {
        return this.model.sync();
    }

    public async save(log: TransferLog, dbTransaction?: DBTransaction): Promise<any> {
        return this.model.create(log, { transaction: dbTransaction });
    }

    public async saveBatch(logs: TransferLog[], dbTransaction?: DBTransaction): Promise<any> {
        return this.model.bulkCreate(logs, { transaction: dbTransaction });
    }

    public async find(trxId: string, pool: string): Promise<TransferLog> {
        // TODO: maybe there is a bug here
        const data = await this.model.findOne({
            where: {
                trxId: trxId,
                pool: pool,
            } as any,
        });
        return data ? data.toJSON() : undefined;
    }

    public async findAll(): Promise<TransferLog[]> {
        const data = await this.model.findAll();
        return data.map(v => v.toJSON());
    }
}

export const transferLogDb: TransferLogDb = new TransferLogDb("jp_transfer_log");
export const remoteTransferLogDb: TransferLogDb = new TransferLogDb("remote_jp_transfer_log");
