import { InferAttributes, InferCreationAttributes, Model, ModelStatic, DataTypes, fn } from "sequelize";
import db from "../services/db.service";
import { DBTransaction } from "@skywind-group/sw-wallet";
import { JackpotOperationStatus } from "@skywind-group/sw-jpn-core";

const TABLE_NAME_REGEXP = new RegExp("\\$tableName", "g");

const UPSERT_WIN_LOG_WITH_DELETE = `
WITH cte_item AS (
  SELECT
    trx_id,
    trx_date::timestamp with time zone,
    external_id,
    remote_trx_id,
    remote_trx_region,
    jackpot_id,
    pool,
    event_id::INTEGER,
    currency,
    initial_seed::NUMERIC,
    seed::NUMERIC,
    progressive::NUMERIC,
    total_seed::NUMERIC,
    total_progressive::NUMERIC,
    seed_since_last_win::NUMERIC,
    progressive_since_last_win::NUMERIC,
    brand_id,
    region,
    game_id,
    player_code,
    player_currency,
    win_amount::NUMERIC,
    currency_rate::NUMERIC,
    round_id::BIGINT,
    game_code,
    bet_amount::NUMERIC,
    status::enum_jackpot_operation_status,
    inserted_at::DATE,
    info::JSONB
  FROM
    (
      VALUES
        (
          :trxId, :trxDate, :externalId, :remoteTrxId, :remoteTrxRegion,
          :jackpotId, :pool, :eventId, :currency, :initialSeed, :seed, :progressive,
          :totalSeed, :totalProgressive, :seedSinceLastWin, :progressiveSinceLastWin,
          :brandId, :region, :gameId, :playerCode, :playerCurrency,
          :winAmount, :currencyRate, :roundId, :gameCode, :betAmount, :status, :insertedAt, :info
        )
    ) AS t (
      trx_id, trx_date, external_id, remote_trx_id, remote_trx_region,
      jackpot_id, pool, event_id, currency, initial_seed, seed, progressive,
      total_seed, total_progressive, seed_since_last_win, progressive_since_last_win,
      brand_id, region, game_id, player_code, player_currency,
      win_amount, currency_rate, round_id, game_code, bet_amount, status, inserted_at, info
    )
),
cte_del AS (
  DELETE FROM
    $tableName AS t USING cte_item AS n
  WHERE
    t.trx_id = n.trx_id AND t.jackpot_id = n.jackpot_id AND t.pool = n.pool
  RETURNING t.*
),
cte_ins AS (
  INSERT INTO $tableName (
    id, trx_id, trx_date, external_id, remote_trx_id, remote_trx_region,
    jackpot_id, pool, event_id, currency, initial_seed, seed, progressive,
    total_seed, total_progressive, seed_since_last_win, progressive_since_last_win,
    brand_id, region, game_id, player_code, player_currency,
    win_amount, currency_rate, round_id, game_code, bet_amount, status, inserted_at, info
  )
  SELECT
    COALESCE(d.id, nextval((select pg_get_serial_sequence('$tableName', 'id'))::regclass)),
    i.trx_id,
    CASE WHEN d.status IS NULL OR i.status != 'pending' THEN i.trx_date ELSE d.trx_date END,
    COALESCE(d.external_id, i.external_id),
    i.remote_trx_id,
    i.remote_trx_region,
    i.jackpot_id,
    i.pool,
    i.event_id,
    i.currency,
    i.initial_seed,
    i.seed,
    i.progressive,
    i.total_seed,
    i.total_progressive,
    i.seed_since_last_win,
    i.progressive_since_last_win,
    i.brand_id,
    i.region,
    i.game_id,
    i.player_code,
    i.player_currency,
    i.win_amount,
    i.currency_rate,
    i.round_id,
    i.game_code,
    COALESCE(d.bet_amount, i.bet_amount),
    CASE WHEN d.status IS NULL OR i.status != 'pending' THEN i.status ELSE d.status END,
    COALESCE(d.inserted_at, NOW()),
    i.info
  FROM
    cte_item AS i
  LEFT JOIN cte_del AS d ON d.trx_id = i.trx_id AND d.pool = i.pool
  RETURNING round_id
)
SELECT round_id FROM cte_ins;
`;

export interface WinLog {
    trxId: string;
    trxDate: Date;
    externalId: string;
    remoteTrxId?: string;
    remoteTrxRegion?: string;
    jackpotId: string;
    pool: string;
    eventId: number;
    currency: string;
    initialSeed: number;
    seed: number;
    progressive: number;
    totalSeed: number;
    totalProgressive: number;
    seedSinceLastWin: number;
    progressiveSinceLastWin: number;
    brandId: number;
    region: string;
    gameId: string;
    playerCode: string;
    playerCurrency: string;
    winAmount: number;
    currencyRate: number;
    roundId: string;
    gameCode: string;
    betAmount?: number;
    status?: JackpotOperationStatus;
    insertedAt?: Date;
    info?: any;
}

export interface WinLogDBInstance extends Model<
    InferAttributes<WinLogDBInstance>,
    InferCreationAttributes<WinLogDBInstance>>, WinLog {
}
export type IWinLogModel = ModelStatic<WinLogDBInstance>;
export class WinLogDb {

    private model: IWinLogModel;
    private readonly upsertWithDeleteQuery: string;

    constructor(tableName: string, skipColumns: string[] = []) {
        const attributes = {
            trxId: { type: DataTypes.CHAR(28), field: "trx_id", allowNull: false },
            trxDate: { type: "timestamp without time zone", field: "trx_date", allowNull: false },
            externalId: { type: DataTypes.STRING, field: "external_id" },
            remoteTrxId: { type: DataTypes.CHAR(28), field: "remote_trx_id", allowNull: true },
            remoteTrxRegion: { type: DataTypes.STRING, field: "remote_trx_region", allowNull: true },
            jackpotId: { type: DataTypes.STRING, field: "jackpot_id", allowNull: false },
            pool: { type: DataTypes.STRING, field: "pool", allowNull: false },
            eventId: { type: DataTypes.INTEGER, field: "event_id", allowNull: false, defaultValue: 0 },
            currency: { type: DataTypes.CHAR(3), field: "currency", allowNull: false },
            initialSeed: { type: DataTypes.DECIMAL, field: "initial_seed", allowNull: false },
            seed: { type: DataTypes.DECIMAL, field: "seed", allowNull: false },
            progressive: { type: DataTypes.DECIMAL, field: "progressive", allowNull: false },
            totalSeed: { type: DataTypes.DECIMAL, field: "total_seed", allowNull: false },
            totalProgressive: { type: DataTypes.DECIMAL, field: "total_progressive", allowNull: false },
            seedSinceLastWin: { type: DataTypes.DECIMAL, field: "seed_since_last_win" },
            progressiveSinceLastWin: { type: DataTypes.DECIMAL, field: "progressive_since_last_win" },
            brandId: { type: DataTypes.INTEGER, field: "brand_id", allowNull: false },
            region: { type: DataTypes.STRING, field: "region" },
            gameId: { type: DataTypes.STRING, field: "game_id", allowNull: false },
            playerCode: { type: DataTypes.STRING, field: "player_code", allowNull: false },
            playerCurrency: { type: DataTypes.CHAR(3), field: "player_currency", allowNull: false },
            winAmount: { type: DataTypes.DECIMAL, field: "win_amount", allowNull: false },
            currencyRate: { type: DataTypes.DECIMAL, field: "currency_rate", allowNull: false },
            roundId: { type: DataTypes.BIGINT, field: "round_id" },
            gameCode: { type: DataTypes.STRING, field: "game_code", allowNull: false },
            betAmount: { type: DataTypes.DECIMAL, field: "bet_amount", allowNull: true },
            status: { type: "enum_jackpot_operation_status", field: "status", allowNull: true },
            insertedAt: { field: "inserted_at", type: DataTypes.DATE, allowNull: false,
            defaultValue: fn("NOW") },
            info: { type: DataTypes.JSONB, field: "info", allowNull: true }
        };
        for (const column of skipColumns) {
            delete attributes[column];
        }
        this.model = db.define<WinLogDBInstance, WinLog>(
            tableName,
            attributes,
            {
                freezeTableName: true,
                timestamps: false,
                indexes: [
                    {
                        name: `idx_${tableName}_trx_jp_id_pool_event_id_unique`,
                        unique: true,
                        fields: ["trx_id", "jackpot_id", "pool", "event_id"]
                    },
                    {
                        name: `idx_${tableName}_remote_trx_id`,
                        fields: ["remote_trx_id"]
                    }
                ],
            }
        );
        this.upsertWithDeleteQuery = UPSERT_WIN_LOG_WITH_DELETE.replace(TABLE_NAME_REGEXP, tableName);
    }

    public async sync(): Promise<any> {
        return this.model.sync();
    }

    public async save(log: WinLog, dbTransaction?: DBTransaction): Promise<any> {
        if (log.status) {
            log.externalId = log.externalId || null;
            log.remoteTrxId = log.remoteTrxId || null;
            log.remoteTrxRegion = log.remoteTrxRegion || null;
            log.insertedAt = log.insertedAt || null;
            log.betAmount = log.betAmount || null;
            log.status = log.status || null;
            log.info = log.info && JSON.stringify(log.info) || null;

            const rs = await db.query(
                this.upsertWithDeleteQuery,
                {
                    replacements: log as any,
                    transaction: dbTransaction
                }
            );
            return !!rs.length;
        } else {
            return this.model.create(log, { transaction: dbTransaction });
        }
    }

    public async saveBatch(logs: WinLog[], dbTransaction?: DBTransaction): Promise<any> {
        const logsWithoutStatuses = logs.filter(log => !log.status);
        if (logsWithoutStatuses.length) {
            await this.model.bulkCreate(logsWithoutStatuses, { transaction: dbTransaction });
        }
        const logsWithStatuses = logs.filter(log => log.status);
        for (const log of logsWithStatuses) {
            await this.save(log, dbTransaction);
        }
    }

    public async find(trxId: string, pool: string): Promise<WinLog> {
        const data = await this.model.findOne({
            where: {
                trxId: trxId,
                pool: pool,
            },
        });
        return data ? data.toJSON() : undefined;
    }

    public async findAll(): Promise<WinLog[]> {
        const data = await this.model.findAll();
        return data.map(v => v.toJSON());
    }
}

export const winLogDb: WinLogDb = new WinLogDb("jp_win_log");
export const remoteWinLogDb: WinLogDb = new WinLogDb("remote_jp_win_log", ["status"]);
