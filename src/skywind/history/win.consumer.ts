import { ITrxData } from "@skywind-group/sw-wallet";
import * as Sequelize from "sequelize";
import { BaseConsumer } from "./base.consumer";
import { remoteWinLogDb, WinLog, winLogDb } from "./win.log";
import {
    BaseWalletWinParams,
    LOCAL_WALLET_OPERATION_NAME,
    REMOTE_WALLET_OPERATION_NAME
} from "../modules/walletParams";

export class WinConsumer extends BaseConsumer {

    public async process(dbTransaction: Sequelize.Transaction, bulk: ITrxData[]): Promise<void> {
        const allowedOperations = [
            LOCAL_WALLET_OPERATION_NAME.release,
            REMOTE_WALLET_OPERATION_NAME.release,
            LOCAL_WALLET_OPERATION_NAME.releaseConfirmation,
            LOCAL_WALLET_OPERATION_NAME.releaseRollback
        ] as string[];
        const logs = [];
        const remoteLogs = [];
        for (const trx of bulk) {
            if (trx.operation && trx.data.length && trx.operation.params && trx.operation.params.brandId
                && allowedOperations.includes(trx.operation.operationName)) {
                const params: BaseWalletWinParams = trx.operation.params;
                const storage = params.fromRemote ? remoteLogs : logs;
                storage.push(...this.parseWinLog(trx));
            }
        }
        await winLogDb.saveBatch(logs, dbTransaction);
        await remoteWinLogDb.saveBatch(remoteLogs, dbTransaction);
    }

    private parseWinLog(trx: ITrxData): WinLog[] {
        const logs = [];
        const params: BaseWalletWinParams = trx.operation.params;
        const wallet = this.parseJackpotWalletKey(trx.data[0].walletKey);
        for (let i = 0; i < params.wins.length; i += 1) {
            const win = params.wins[i];
            if (win.winAmount === undefined) {
                continue;
            }
            const log: WinLog = {
                trxId: trx.id,
                trxDate: trx.committedAt,
                externalId: trx.operation.externalTrxId,
                remoteTrxId: params.remoteTrxId,
                remoteTrxRegion: params.remoteTrxRegion,
                jackpotId: wallet.jackpotId,
                currency: wallet.currency,
                pool: win.pool,
                eventId: i,
                initialSeed: win.initialSeed || 0,
                seed: win.seed || 0,
                progressive: win.progressive || 0,
                totalSeed: win.totalSeed || 0,
                totalProgressive: win.totalProgressive || 0,
                seedSinceLastWin: win.seedSinceLastWin,
                progressiveSinceLastWin: win.progressiveSinceLastWin,
                brandId: params.brandId,
                region: params.region,
                gameId: params.gameId,
                playerCode: params.playerCode,
                playerCurrency: params.playerCurrency,
                winAmount: win.winAmount,
                currencyRate: win.currencyRate,
                roundId: params.roundId,
                gameCode: params.gameCode,
                betAmount: params.betAmount,
                status: win.status,
                info: win.info
            };
            logs.push(log);
        }
        return logs;
    }
}
