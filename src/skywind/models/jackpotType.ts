import { InferAttributes, InferCreationAttributes, Model, ModelStatic, DataTypes } from "sequelize";
import { JackpotType } from "../api/model";
import { JackpotDefinition } from "@skywind-group/sw-jpn-core";
import db from "../services/db.service";

export interface JackpotTypeDBInstance extends Model<
    InferAttributes<JackpotTypeDBInstance>,
    InferCreationAttributes<JackpotTypeDBInstance>
>, JackpotType {
    internalId: number;
    toInfo(): JackpotType;
}
export type IJackpotTypeModel = ModelStatic<JackpotTypeDBInstance>;
const schema = {
    internalId: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true, field: "id" },
    name: { type: DataTypes.STRING, unique: true, field: "name", allowNull: false },
    baseType: { type: DataTypes.STRING, field: "base_type", allowNull: false },
    jpGameId: { type: DataTypes.STRING, field: "jp_game_id", allowNull: false },
    definition: { type: DataTypes.JSONB, field: "definition", allowNull: false },
    configurable: { type: DataTypes.BOOLEAN, field: "configurable", allowNull: false, defaultValue: false },
    overridable: { type: DataTypes.BOOLEAN, field: "overridable", allowNull: false, defaultValue: false },
    canBeDisabled: { type: DataTypes.BOOLEAN, field: "can_be_disabled", allowNull: false, defaultValue: false },
    supportsWinCap: { type: DataTypes.BOOLEAN, field: "supports_win_cap", allowNull: false, defaultValue: false },
    createdAt: { type: "timestamp without time zone", field: "created_at", allowNull: true },
    updatedAt: { type: "timestamp without time zone", field: "updated_at", allowNull: true },
};

export function JackpotTypeModel() {
    const model: IJackpotTypeModel = db.define<JackpotTypeDBInstance, JackpotType>(
        "JackpotType",
        schema,
        { tableName: "jp_type" }
    );
    (model as any).prototype.toInfo = function() {
        return {
            name: this.name,
            baseType: this.baseType,
            jpGameId: this.jpGameId,
            definition: this.definition,
            configurable: this.configurable,
            overridable: this.overridable,
            canBeDisabled: this.canBeDisabled,
            supportsWinCap: this.supportsWinCap
        };
    };
    return model;
}

export class DBJackpotType {
    public internalId: number;
    public name: string;
    public baseType: string;
    public jpGameId: string;
    public definition: JackpotDefinition;
    public configurable: boolean;
    public overridable: boolean;
    public canBeDisabled: boolean;
    public supportsWinCap: boolean;
    public createdAt: Date;
    public updatedAt: Date;

    constructor(info: JackpotType) {
        this.name = info.name;
        this.baseType = info.baseType;
        this.jpGameId = info.jpGameId;
        this.definition = info.definition;
        this.configurable = info.configurable;
        this.overridable = info.overridable;
        this.canBeDisabled = info.canBeDisabled;
        this.supportsWinCap = info.supportsWinCap;
    }
}
