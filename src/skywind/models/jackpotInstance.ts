import { InferAttributes, InferCreationAttributes, Model, ModelStatic, DataTypes } from "sequelize";
import {
    JackpotDisableMode,
    JackpotInstance,
    JackpotInternalInstance,
} from "../api/model";
import { JackpotInstanceService } from "../services/jackpot.instance";
import { JackpotTypeDBInstance, JackpotTypeModel } from "./jackpotType";
import { DEFAULT_PRECISION } from "../modules/jackpotWallet";
import db from "../services/db.service";
import { JackpotRegionDBInstance, JackpotRegionModel } from "./jackpotRegion";
import { JackpotDefinition } from "@skywind-group/sw-jpn-core";

const jackpotTypeModel = JackpotTypeModel();
const jackpotRegionModel = JackpotRegionModel();
export interface JackpotModelInstance {
    id?: number;
    internalId: number;
    pid: string;
    typeId: number;
    regionId?: number;
    type: JackpotTypeDBInstance;
    definition: JackpotDefinition;
    migratedAt: Date;
    createdAt: Date;
    updatedAt: Date;
    region: JackpotRegionDBInstance;
    isTest: boolean;
    disableMode: number;
    isDisabled: boolean;
    isGlobal: boolean;
    isOwned: boolean;
    isLocal: boolean;
    jackpotConfigurationLevel: number;
    entityId: number;
    jurisdictionCode: string;
    info: any;
}
export interface JackpotInstanceDBInstance extends Model<
    InferAttributes<JackpotInstanceDBInstance>,
    InferCreationAttributes<JackpotInstanceDBInstance>
>, JackpotModelInstance {
    getType(): Promise<JackpotTypeDBInstance>;
    getRegion(): Promise<JackpotRegionDBInstance>;
    toInfo(withInternalId?: boolean): Promise<JackpotInternalInstance>;
}
export type IJackpotInstanceModel = ModelStatic<JackpotInstanceDBInstance>;

const schema: any = {
    internalId: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true, field: "id" },
    pid: { type: DataTypes.STRING, unique: true, field: "pid", allowNull: false },
    typeId: { type: DataTypes.INTEGER, field: "type_id", allowNull: false },
    regionId: { type: DataTypes.INTEGER, field: "region_id", allowNull: true },
    definition: { type: DataTypes.JSONB, field: "definition" },
    migratedAt: { type: "timestamp without time zone", field: "migrated_at", allowNull: true },
    createdAt: { type: "timestamp without time zone", field: "created_at", allowNull: true },
    updatedAt: { type: "timestamp without time zone", field: "updated_at", allowNull: true },
    isTest: { field: "is_test", type: DataTypes.BOOLEAN },
    disableMode: { type: DataTypes.INTEGER, field: "disable_mode" },
    isDisabled:{ type: DataTypes.BOOLEAN, field: "is_disabled" },
    isGlobal: { type: DataTypes.BOOLEAN, field: "is_global" },
    isOwned: { type: DataTypes.BOOLEAN, field: "is_owned" },
    isLocal: { type: DataTypes.BOOLEAN, field: "is_local" },
    jackpotConfigurationLevel: { type: DataTypes.INTEGER, field: "jackpot_configuration_level" },
    entityId: { type: DataTypes.INTEGER, field: "entity_id" },
    jurisdictionCode: { type: DataTypes.STRING, field: "jurisdiction_code" },
    info: { type: DataTypes.JSONB, field: "info" },
};

const model: IJackpotInstanceModel = db.define<JackpotInstanceDBInstance, JackpotModelInstance>(
    "JackpotInstance",
    schema,
    {
        tableName: "jp_instance",
        timestamps: true,
        indexes: [{ fields: ["type_id"] }],
        paranoid: true,
        underscored: true
    }
);

model.belongsTo(jackpotTypeModel, { foreignKey: "typeId", as: "type" });
model.belongsTo(jackpotRegionModel, { foreignKey: "regionId", onDelete: "NO ACTION", as: "region" });

export function JackpotInstanceModel() {
    (model as any).prototype.toInfo = async function(withInternalId?: boolean): Promise<JackpotInternalInstance> {
        const type: JackpotTypeDBInstance = await this.getType();
        const info: JackpotInternalInstance = {
            id: this.pid,
            type: type.name,
            baseType: type.baseType,
            jpGameId: type.jpGameId,
            definition: JackpotInstanceService.buildJackpotDefinition(type.definition, this.definition),
            disableMode: this.disableMode,
            isDisabled: this.isDisabled,
            isTest: this.isTest,
            isGlobal: this.isGlobal,
            isOwned: this.isOwned,
            isLocal: this.isLocal,
            createdAt: this.createdAt,
            jackpotConfigurationLevel: this.jackpotConfigurationLevel,
            entityId: this.entityId,
            jurisdictionCode: this.jurisdictionCode,
            info: this.info,
        } as JackpotInternalInstance;
        const region: JackpotRegionDBInstance = this.regionId ? await this.getRegion() : undefined;
        if (region) {
            info.regionCode = region.code;
        }
        if (withInternalId) {
            info.internalId = this.internalId;
            info.region = region && region.toInfo();
            info.precision = DEFAULT_PRECISION;
            info.migratedAt = this.migratedAt;
        }
        return info;
    };
    return model;
}

export class DBJackpotInstance {
    public internalId: number;
    public pid: string;
    public typeId: number;
    public regionId: number;
    public type: JackpotTypeDBInstance;
    public definition: JackpotDefinition;
    public migratedAt: Date;
    public createdAt: Date;
    public updatedAt: Date;
    public region: JackpotRegionDBInstance;
    public isTest: boolean;
    public disableMode: number;
    public isDisabled: boolean = false;
    public isGlobal: boolean = false;
    public isOwned: boolean = false;
    public isLocal: boolean = false;
    public jackpotConfigurationLevel: number;
    public entityId: number;
    public jurisdictionCode: string;
    public info: any;

    constructor(info: JackpotInstance, typeId: number, regionId?: number) {
        this.pid = info.id;
        this.typeId = typeId;
        this.definition = info.definition;
        this.regionId = regionId;
        this.disableMode = info.disableMode || JackpotDisableMode.IMMEDIATE;
        this.isTest = info.isTest;
        this.isGlobal = info.isGlobal || !!regionId;
        this.isOwned = info.isOwned || false;
        this.isLocal = info.isLocal || false;
        this.jackpotConfigurationLevel = info.jackpotConfigurationLevel;
        this.entityId = info.entityId;
        this.jurisdictionCode = info.jurisdictionCode;
        this.info = info.info;
    }
}
