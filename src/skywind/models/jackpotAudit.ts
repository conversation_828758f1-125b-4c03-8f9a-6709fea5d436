import { InferAttributes, InferCreationAttributes, Model, ModelStatic, DataTypes } from "sequelize";
import { <PERSON><PERSON><PERSON>udit, JackpotAuditCreateData, JackpotAuditInitiator, JackpotAuditType } from "../api/model";
import db from "../services/db.service";

export interface JackpotAuditDBInstance extends Model<
    InferAttributes<JackpotAuditDBInstance>,
    InferCreationAttributes<JackpotAuditDBInstance>
>, JackpotAudit {
    toInfo(): JackpotAudit;
}
export type IJackpotAuditModel = ModelStatic<JackpotAuditDBInstance>;
const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true, field: "id" },
    jackpotId: { type: DataTypes.STRING, field: "jackpot_id", allowNull: false },
    type: { type: DataTypes.STRING, field: "type", allowNull: false },
    history: { type: DataTypes.JSONB, field: "history", allowNull: false },
    ts: { type: "timestamp without time zone", field: "ts", allowNull: false },
    initiatorType: {
        type: DataTypes.ENUM(...Object.values(JackpotAuditInitiator)),
        allowNull: false,
        field: "initiator_type"
    },
    initiatorName: { type: DataTypes.STRING, field: "initiator_name", allowNull: true },
    ip: { type: DataTypes.INET, field: "ip", allowNull: true },
    userAgent: { type: DataTypes.STRING, field: "user_agent", allowNull: true },
};

export function JackpotAuditModel() {
    const model: IJackpotAuditModel = db.define<JackpotAuditDBInstance, JackpotAudit>(
        "JackpotAudit",
        schema,
        {
            tableName: "jp_audit",
            timestamps: false,
            underscored: true,
            indexes: [{ name: "idx_jp_audit_ts", fields: ["ts"] }]
        }
    );
    (model as any).prototype.toInfo = function() {
        return {
            jackpotId: this.jackpotId,
            type: this.type,
            history: this.history,
            ts: this.ts,
            initiatorType: this.initiatorType,
            initiatorName: this.initiatorName,
            ip: this.ip,
            userAgent: this.userAgent
        };
    };
    return model;
}

export class DBJackpotAudit {
    public jackpotId: string;
    public type: JackpotAuditType;
    public history: any;
    public ts: Date;
    public initiatorType;
    public initiatorName: string;
    public ip: string;
    public userAgent: string;

    constructor(info: JackpotAuditCreateData) {
        this.jackpotId = info.jackpotId;
        this.type = info.type;
        this.history = info.history;
        this.ts = info.ts;
        this.initiatorType = info.initiatorType;
        this.initiatorName = info.initiatorName;
        this.ip = info.ip;
        this.userAgent = info.userAgent;
    }
}
