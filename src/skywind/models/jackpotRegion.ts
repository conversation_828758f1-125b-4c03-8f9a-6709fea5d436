import { InferAttributes, InferCreationAttributes, Model, ModelStatic, DataTypes } from "sequelize";
import { SecureContextOptions } from "tls";
import { JackpotRegion } from "../api/model";
import db from "../services/db.service";

export interface JackpotRegionDBInstance extends Model<
    InferAttributes<JackpotRegionDBInstance>,
    InferCreationAttributes<JackpotRegionDBInstance>
>, JackpotRegion {
    id: number;
    toInfo(): JackpotRegion;
}

export type IJackpotRegionModel = ModelStatic<JackpotRegionDBInstance>;
const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true, field: "id" },
    code: { type: DataTypes.STRING, unique: true, field: "code", allowNull: false },
    url: { type: DataTypes.STRING, field: "url", allowNull: false },
    secureOptions: { type: DataTypes.JSONB, field: "secure_options" },
};
const model: IJackpotRegionModel = db.define<JackpotRegionDBInstance, JackpotRegion>(
    "JackpotRegion",
    schema,
    { tableName: "jp_region", timestamps: true, underscored: true }
);
export function JackpotRegionModel() {
    model.prototype.toInfo = function() {
        return {
            code: this.code,
            url: this.url,
            secureOptions: this.secureOptions
        };
    };
    return model;
}

export class DBJackpotRegion {
    public id: number;
    public code: string;
    public url: string;
    public secureOptions: SecureContextOptions;

    constructor(info: JackpotRegion) {
        this.code = info.code;
        this.url = info.url;
        this.secureOptions = info.secureOptions;
    }
}
