import * as redis from "../storage/redis";

export class Lock {

    private key: string;
    private locked: number;

    constructor(key: string) {
        this.key = "lock:" + key;
    }

    public async lock(expire: number): Promise<void> {
        let lockAcquired = false;
        const ts = Date.now() + expire;
        const client = await redis.get();
        try {
            const result = await client.setnx(this.key, ts);
            await redis.waitForSync(client);
            if (result === 1) {
                lockAcquired = true;
            } else {
                const lockTs = await client.get(this.key);
                if (lockTs && Number(lockTs) < Date.now()) {
                    const expiredLockTs = await client.getset(this.key, ts);
                    await redis.waitForSync(client);
                    if (expiredLockTs === lockTs) {
                        lockAcquired = true;
                    }
                }
            }
        } finally {
            redis.release(client);
        }

        if (!lockAcquired) {
            return new Promise<void>((resolve, reject) => {
                setTimeout(() => {
                    this.lock(expire).then(resolve).catch(reject);
                }, 100);
            });
        }

        this.locked = ts;
    }

    public async unlock(): Promise<void> {
        if (!this.locked) {
            throw new Error("Lock not acquired");
        }
        const client = await redis.get();
        try {
            const ts = await client.get(this.key);
            if (ts && Number(ts) !== this.locked) {
                throw new Error("Failed to unlock - concurrent use");
            }
            await client.del(this.key);
            await redis.waitForSync(client);
            this.locked = undefined;
        } finally {
            redis.release(client);
        }
    }
}
