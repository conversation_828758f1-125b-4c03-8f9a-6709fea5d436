export function round(value: number, precision: number, rng: () => number = Math.random): number {

    if (precision < 0 || precision > 8 || precision === undefined) {
        return value;
    }

    const exp = 10 ** precision;
    const normalized = value * exp;

    const rounded = stochasticRounding(normalized, rng);

    return rounded / exp;
}

// https://en.wikipedia.org/wiki/Rounding#Stochastic_rounding
function stochasticRounding(value: number, rng: () => number): number {
    const floor = Math.floor(value);
    const prob = value - floor;

    const rnd = rng();
    if (rnd < prob) {
        return floor + 1;
    } else {
        return floor;
    }
}
