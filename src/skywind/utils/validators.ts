import * as net from "net";

export enum IpFamily {
    NONE,
    IP_V6,
    IP_V4
}

export class Validators {
    public static validateIP(ip: string): IpFamily {
        if (!ip && typeof ip !== "string") {
            return IpFamily.NONE;
        }

        const ipType = net.isIP(ip);
        switch (ipType) {
            case 0:
                return IpFamily.NONE;
            case 4:
                return IpFamily.IP_V4;
            case 6:
                return IpFamily.IP_V6;
            default:
                return IpFamily.NONE;
        }
    }

    public static isOptionalTs(ts: number): boolean {
        if (ts === undefined || ts === null) {
            return true;
        }
        return Number.isFinite(ts);
    }
}
