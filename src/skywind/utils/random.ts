import { RandomGenerator, CSRandomGenerator } from "@skywind-group/sw-random-cs";
import { lazy } from "@skywind-group/sw-utils";

const CheatingRandomGenerator = lazy(() => require("@skywind-group/sw-random-cs-cheating").CheatingRandomGenerator);

const cheatsConfig = require("../../../resources/cheatsConfig.json");

const globalRandomGenerator = new CSRandomGenerator();

export function createRandomGenerator(req: any): RandomGenerator {
    if (req.cheats && cheatsConfig.allowSetPositionsByClient) {
        if (!req.rngCheats) {
            req.rngCheats = req.cheats.slice(0);
        }
        return new (CheatingRandomGenerator.get())(req.rngCheats);
    }
    if (req.cheats) {
        req.cheats = undefined;
    }
    return globalRandomGenerator;
}

export function getGlobalRandomGenerator() {
    return globalRandomGenerator;
}
