import {
    Request,
    Response,
    NextFunction,
} from "express";
import * as Errors from "../errors";
import * as JWT from "jsonwebtoken";
import config from "../config";
import { token as jwt } from "@skywind-group/sw-utils";

const SCOPE_ID: string = "Permissions";
export const ACCESS_TOKEN: string = "X-Access-Token";

type PermissionsList = string[];

export interface UserPermissions {
    username: string;
    grantedPermissions: PermissionsList;
}

export interface AccessTokenData extends UserPermissions {
    userId: number;
    entityId: number;
    sessionId?: string;
    isSuperAdmin?: boolean;
}

function extractPermissions(request: any): string[] {
    if (!request.swagger || !request.swagger.operation || !request.swagger.operation.security) {
        return undefined;
    }

    return getSecurityPermissions(request.swagger.operation.security);
}

function getSecurityPermissions(security): string[] {
    let securityPermissions: string[];
    for (let i = 0, length = security.length; i < length; i++) {
        if (security[i].hasOwnProperty(SCOPE_ID)) {
            securityPermissions = security[i][SCOPE_ID];
            break;
        }
    }
    return securityPermissions;
}

function isPermitted(request: any, userPermissions: UserPermissions): boolean {
    const operationPermissions = extractPermissions(request);
    if (!operationPermissions) {
        return false;
    }
    if (!userPermissions.grantedPermissions) {
        return false;
    }

    for (const p of operationPermissions) {
        if (userPermissions.grantedPermissions.indexOf(p) >= 0) {
            return true;
        }
    }

    return false;
}

function parseToken(token: string): UserPermissions {
    try {
        return JWT.verify(token, config.accessToken.secret) as any;
    } catch (err) {
        if (err instanceof JWT.JsonWebTokenError) {
            throw new Errors.TokenNotValid();
        } else if (err instanceof JWT.TokenExpiredError) {
            throw new Errors.TokenExpired();
        } else {
            throw err;
        }
    }
}

export function authenticate(req: Request, res: Response, next: NextFunction) {
    const token: string = req.get(ACCESS_TOKEN);
    if (!token) {
        return next(new Errors.TokenIsMissing());
    }
    try {
        const permissions: UserPermissions = parseToken(token);
        if (!isPermitted(req, permissions)) {
            return next(new Errors.OperationForbidden());
        }
        if (!req.query.initiator) {
            req.query.initiator = permissions.username;
        }
        next();
    } catch (ex) {
        next(ex);
    }
}

export async function verifyInternalToken(token: string): Promise<any> {
    try {
        return await jwt.verify(token, config.internalServerToken);
    } catch (err) {
        if (err instanceof jwt.TokenVerifyException) {
            return Promise.reject(new Errors.InternalServerTokenError());
        }

        if (err instanceof jwt.TokenExpiredException) {
            return Promise.reject(new Errors.InternalServerTokenExpiredError());
        }
        return Promise.reject(err);
    }
}

export function generateInternalToken(data: any): Promise<string> {
    return jwt.generate(data, config.internalServerToken);
}
