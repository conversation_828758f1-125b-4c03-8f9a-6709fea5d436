import { measures, token } from "@skywind-group/sw-utils";
import { AccessTokenData } from "./security";
import { AuthRequest, BaseRequest } from "@skywind-group/sw-jpn-core";

export class ContextVariables {
    private static readonly EntityId = "entity-id";
    private static readonly UserId = "user-id";
    private static readonly PlayerCode = "player-code";
    private static readonly SessionId = "session-id";
    private static readonly BrandId = "brand-id";
    private static readonly GameCode = "game-code";
    private static readonly RoundId = "round-id";

    public static setAuth(accessToken: string, req: BaseRequest) {
        if (accessToken) {
            const tokenData = token.parse<AccessTokenData | AuthRequest>(accessToken);
            const userTokenData: AccessTokenData = tokenData as AccessTokenData;
            const playerTokenData: AuthRequest = tokenData as AuthRequest;
            if (userTokenData?.userId) {
                ContextVariables.setUserAuth(userTokenData);
            }
            if (playerTokenData?.playerCode) {
                ContextVariables.setPlayerAuth(playerTokenData, req);
            }
        }
    }

    public static setUserAuth(accessToken: AccessTokenData) {
        measures.measureProvider.setContextVariable(ContextVariables.EntityId, accessToken.entityId, true);
        measures.measureProvider.setContextVariable(ContextVariables.UserId, accessToken.userId, true);
        measures.measureProvider.setContextVariable(ContextVariables.SessionId, accessToken.sessionId, true);
    }

    public static setPlayerAuth(playerToken: AuthRequest, req: BaseRequest) {
        measures.measureProvider.setContextVariable(ContextVariables.GameCode, playerToken.gameCode, true);
        measures.measureProvider.setContextVariable(ContextVariables.PlayerCode, playerToken.playerCode, true);
        measures.measureProvider.setContextVariable(ContextVariables.BrandId, playerToken.brandId, true);
        measures.measureProvider.setContextVariable(ContextVariables.RoundId, req?.roundId, true);
    }
}
