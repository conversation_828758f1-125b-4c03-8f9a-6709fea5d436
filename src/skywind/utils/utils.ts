import fs = require("fs");
import { Request } from "express";
import { PathLike } from "fs";
import { UnableToLoadFileError, ValidationError } from "../errors";

export function findIp(req: Request): string {
    let ip: string = req?.query?.ip as string;
    const xForwardedForHeader = req.headers["x-forwarded-for"] as string;
    if (!ip && xForwardedForHeader) {
        const forwardedFor = xForwardedForHeader.split(",");
        ip = forwardedFor[0].replace(" ", "");
    }
    return ip || req.ip;
}

export function requestLogData(req: Request) {
    const ip = findIp(req);
    const userAgent = req.headers["user-agent"] || "N/A";
    const referrer = req.header("Referer") || "N/A";

    return {
        method: req.method,
        url: req.originalUrl,
        query: req.query,
        body : req.body,
        ip,
        userAgent,
        referrer
    };
}

/**
 * Returns list of files in the specified directory
 *
 * @param {"fs".PathLike} dirPath target directory
 * @returns {Promise<string[]>} files list
 */
export async function getFilesList(dirPath: PathLike): Promise<string[]> {
    return new Promise<string[]>((resolve, reject) => {
        fs.readdir(dirPath, (err, items) => {
            if (err) {
                reject(err);
            } else {
                resolve(items);
            }
        });
    });
}

/**
 * Loads json file and return js object or throws error on fail.
 * @param {"fs".PathLike} filePath target file
 * @returns {Promise<any>} js objects
 */
export async function readJsonFile(filePath: PathLike): Promise<any> {
    return new Promise<any>((resolve, reject) => {
        fs.readFile(filePath, "utf-8", (err, data) => {
            if (err) {
                reject(new UnableToLoadFileError(filePath.toString(), err));
            } else {
                try {
                    const result = JSON.parse(data);
                    resolve(result);
                } catch (err) {
                    reject(err);
                }
            }
        });
    });
}

export function toMinorUnits(value: number | string, precision: number): number {
    return Math.round(Number(value) * precision);
}

export function toMajorUnits(value: number | string, precision: number): number {
    return Number(Number(value) / precision);
}

// Before using please check implementation and unit-tests. Method has limitations
export function safeExchange(value: number, rate: number): number {
    if (value < 0 || rate < 0) {
        throw new ValidationError("Value and rate should be positive");
    }
    const DEFAULT_PRECISION = 10 ** 8;
    const roundedResult = Math.round(value * rate * DEFAULT_PRECISION);
    if (roundedResult < Number.MAX_SAFE_INTEGER) {
        return roundedResult / DEFAULT_PRECISION;
    }
    const precisionPowerDelta = Math.ceil(Math.log10(roundedResult / Number.MAX_SAFE_INTEGER));
    const newPrecision = DEFAULT_PRECISION / 10 ** precisionPowerDelta;
    if (newPrecision < 10 || precisionPowerDelta === 0) {
        // values are to high - we can ignore floating point error
        return value * rate;
    }
    return Math.round(value * rate * newPrecision) / newPrecision;
}
