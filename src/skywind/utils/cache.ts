import * as redis from "../storage/redis";
import { Redis as RedisClient } from "ioredis";

interface InvalidateMessage<ID> {
    id: ID;
}

export class Cache<ID, T> {

    private cache: Map<ID, T> = new Map<ID, T>();
    private channel: string;
    private readonly sub: RedisClient;
    private readonly pub: RedisClient;

    constructor(name: string, private search: Function) {
        this.channel = "cache:" + name;
        this.sub = redis.create();
        this.pub = redis.create();
        this.listenInvalidates();
    }

    public async find(id: ID, ...args): Promise<T> {
        if (!this.cache.has(id)) {
            const value: T = await this.search.call(this.search, id, ...args);
            if (value) {
                this.cache.set(id, value);
            }
        }
        return this.cache.get(id);
    }

    public invalidate(id: ID) {
        this.notifyInvalidated(id);
    }

    public invalidateAll() {
        this.notifyInvalidated(null);
    }

    private listenInvalidates() {
        this.sub.subscribe(this.channel);
        this.sub.on("message", (channel, message) => {
            if (this.channel === channel) {
                const msg: InvalidateMessage<ID> = JSON.parse(message);
                if (msg.id) {
                    this.cache.delete(msg.id);
                } else {
                    this.cache.clear();
                }
            }
        });
    }

    private notifyInvalidated(id: ID) {
        this.pub.publish(this.channel, JSON.stringify({ id: id }));
    }
}
