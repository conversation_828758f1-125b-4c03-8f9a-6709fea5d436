import config from "../config";
import logger from "./logger";
import { measures } from "@skywind-group/sw-utils";

const log = logger("trxid:pool");

export type TrxIdGenerator = (count: number) => Promise<string[]>;

/**
 * Pool of transaction ids. Generates transaction ids in advance adaptively to current getter pace.
 */
export class TrxIdPool {

    private pool: string[] = [];
    private minPoolLength: number = config.trxIdPool.minLength;

    private generatorPromise: Promise<void>;
    private promisedToGenerate: number = 0;
    private refreshTimer: any;

    constructor(private generator: TrxIdGenerator) {}

    public async getTransactionId(): Promise<string> {
        if (!this.pool.length) {
            await this.generateTransactionIds();
        }

        const trxId: string = this.pool.shift();

        if (!trxId) {
            return this.getTransactionId();
        }

        if (this.pool.length <= this.minPoolLength) {
            this.generateTransactionIds().catch((err) => {
                log.error(err, "Failed to generate transaction ids");
                measures.measureProvider.saveError(err);
            });
        }

        return trxId;
    }

    private async generateTransactionIds(): Promise<void> {
        if (this.generatorPromise) {
            this.promisedToGenerate += 1;
            return this.generatorPromise;
        }

        this.generatorPromise = (async (): Promise<void> => {
            try {
                const poolLengthBefore = this.pool.length;

                const count = Math.min(Math.round(this.minPoolLength * config.trxIdPool.loadFactor),
                    config.trxIdPool.maxLength);
                const ids = await this.generator(count);

                const poolLengthAfter = this.pool.length;

                this.minPoolLength = Math.max(poolLengthBefore - poolLengthAfter + this.promisedToGenerate,
                    config.trxIdPool.minLength);
                this.pool.push(...ids);

                this.refreshStaleTransactionIds();
            } finally {
                this.generatorPromise = undefined;
                this.promisedToGenerate = 0;
            }
        })();

        return this.generatorPromise;
    }

    private refreshStaleTransactionIds() {
        if (!config.trxIdPool.refreshTimeout) {
            // refresh is switched off
            return;
        }
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
        }
        this.refreshTimer = setTimeout(() => {
            this.pool = [];
            this.minPoolLength = config.trxIdPool.minLength;
        }, config.trxIdPool.refreshTimeout);
    }
}
