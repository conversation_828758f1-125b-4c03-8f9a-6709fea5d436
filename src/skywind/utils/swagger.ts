import fs = require("fs");
import { lazy } from "@skywind-group/sw-utils";
import { Request, Response, NextFunction } from "express";

export function loadJson(file: string): any {
    const spec = fs.readFileSync(file, "utf8");
    return JSON.parse(spec);
}

export const apiSwagger = lazy(() => loadJson("swagger.json"));

export const apiSwaggerTicker = lazy(() => loadJson("swagger-ticker.json"));

export const apiSwaggerRemoteApi = lazy(() => loadJson("swagger-remote-api.json"));

export const swaggerCspMiddleware = (req: Request, res: Response, next: NextFunction) => {
    res.setHeader(
        "Content-Security-Policy",
        "default-src 'self'; " +
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
        "font-src 'self' https://fonts.gstatic.com; " +
        "img-src 'self' data: https://online.swagger.io https://validator.swagger.io; " +
        "script-src 'self' 'unsafe-inline'; " +
        "connect-src 'self' https://online.swagger.io;"
    );
    next();
};
