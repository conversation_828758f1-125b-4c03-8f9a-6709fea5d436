export class JackpotId {
    // gameprovidercode;jackpotid;sitecode_widgetcode_currency
    // e.g PP;jp1;sc1_wg1_EUR
    private static readonly GAME_PROVIDER_JACKPOT_REGEX = new RegExp("(\\w+);(.*?);(.*)");

    public static filterAndGroupExternal(jackpotIds: string[]): Map<string, string[]> {
        if (!jackpotIds) {
            return new Map<string, string[]>();
        }
        return jackpotIds.reduce((map, id) => {
            const match = id.match(JackpotId.GAME_PROVIDER_JACKPOT_REGEX);
            if (match === null) return map;
            const gameProvider = match[1];
            if (!map.has(gameProvider)) {
                map.set(gameProvider, []);
            }
            map.get(gameProvider).push(id);
            return map;
        }, new Map<string, string[]>());
    }

    public static filterInternal(jackpotIds: string[]): string[] {
        if (!jackpotIds) {
            return [];
        }
        return jackpotIds.filter(jackpotId => !jackpotId.match(JackpotId.GAME_PROVIDER_JACKPOT_REGEX));
    }
}
