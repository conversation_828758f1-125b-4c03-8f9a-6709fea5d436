import { default as logger } from "./utils/logger";
import { Application } from "express";
import * as http from "http";
import { create as createApplication } from "./express";
import { defineRoutes } from "./api/remoteApiRouters";
import { lazy } from "@skywind-group/sw-utils";
import config from "./config";

export const application = lazy(() => defineRoutes(createApplication()));

export async function startServer(port = 5002): Promise<http.Server> {
    return startApplicationServer(application.get(), "Remote JPN", port);
}

export async function startApplicationServer(app: Application,
                                             name: string,
                                             port: number): Promise<http.Server> {
    const log = logger("remote-api");

    return new Promise<http.Server>((resolve) => {
        const server: http.Server = http.createServer(app);
        server.listen(port, null, () => {
            log.info(`${name} Remote API listening on: ${port}`);
            server.timeout = config.server.timeout;
            server.keepAliveTimeout = config.server.keepAliveTimeout;
            resolve(server);
        });
    });
}
