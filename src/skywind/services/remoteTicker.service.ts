import { Ticker } from "@skywind-group/sw-jpn-core";
import { RedisClientPool } from "../storage/redis";
import config from "../config";
import { redis } from "@skywind-group/sw-utils";
import { JackpotLookup } from "../../definition";
import logger from "../utils/logger";
import * as path from "path";
import { RemoteJackpotApiCache } from "./remoteJackpotApi";

interface RemoteTickerHolder {
    ts: string;
    seqId: string;
    isDisabled?: string;

    [poolProperty: string]: string;
}

const log = logger("remote:jackpot-ticker");

export interface RemoteTicker {
    getTicker(jackpotId: string): Promise<Ticker>;
    updateTicker(remoteTicker: Ticker): Promise<Ticker>;
}

export class RemoteTickerService implements RemoteTicker {
    private updateTickerProc = new redis.RedisProc(log,
        path.resolve(__dirname, "../../../resources/lua/update_remote_ticker.lua"));
    private forceTickerRefresh: Map<string, Promise<Ticker>> = new Map();

    constructor(private readonly redisPool: RedisClientPool,
                private lookup: JackpotLookup) {
    }

    public async getTicker(jackpotId: string): Promise<Ticker> {
        const client = await this.redisPool.get();
        try {
            const holder: RemoteTickerHolder = await client.hgetall(this.getKey(jackpotId)) as RemoteTickerHolder;
            if (!holder.ts) {
                return this.requestRemoteTicker(jackpotId).then((ticker) => this.updateTicker(ticker));
            } else if (Date.now() - (+holder.ts) >= config.remoteTicker.forceRefreshTimeout) {
                this.requestRemoteTicker(jackpotId)
                    .then((ticker) => this.updateTicker(ticker))
                    .catch((err) => log.warn(err, { jackpotId }, "Cannot get ticker for  jackpotID"));
            }

            return await this.deserialize(jackpotId, holder);
        } finally {
            this.redisPool.release(client);
        }
    }

    /**
     * Try to update ticker and return the last value.
     */
    public async updateTicker(remoteTicker: Ticker): Promise<Ticker> {
        const client = await this.redisPool.get();
        try {
            const values = await this.updateTickerProc.exec(client,
                [this.getKey(remoteTicker.id)], this.serialize(remoteTicker));
            if (values == null) {
                return remoteTicker;
            } else {
                return await this.deserialize(remoteTicker.id, this.toObject(values));
            }
        } finally {
            this.redisPool.release(client);
        }
    }

    private async deserialize(jackpotId: string, obj: RemoteTickerHolder): Promise<Ticker> {
        const instance = await this.lookup.findInternal(jackpotId);
        const result: Ticker = {
            id: instance.id,
            currency: instance.definition.currency,
            pools: {},
            seqId: +obj.seqId || 0
        };

        result.isDisabled = !!(+obj.isDisabled || 0);

        for (const item of instance.definition.list) {
            const progressive = +obj[`${item.id}:progressive`] || 0;
            const seed = +obj[`${item.id}:seed`] || 0;
            result.pools[item.id] = {
                seed: seed,
                progressive: progressive
            };
        }

        return result;
    }

    private toObject<T>(values: string[]): T {
        const result = {};
        for (let i = 0; i < values.length; i += 2) {
            result[values[i]] = values[i + 1];
        }
        return result as T;
    }

    private serialize(obj: Ticker): string[] {
        const result: any[] = [
            Date.now(),
            obj.seqId
        ];
        result.push("isDisabled", obj.isDisabled ? 1 : 0);

        for (const poolId of Object.keys(obj.pools)) {
            const pool = obj.pools[poolId];
            result.push(`${poolId}:progressive`);
            result.push(pool.progressive);
            result.push(`${poolId}:seed`);
            result.push(pool.seed);
        }

        return result;
    }

    private async requestRemoteTicker(jackpotId: string): Promise<Ticker> {
        let result: Promise<Ticker> = this.forceTickerRefresh.get(jackpotId);
        if (!result) {
            const instance = await this.lookup.findInternal(jackpotId);
            const remoteApi = RemoteJackpotApiCache.getRemoteJackpotApi(instance.region);
            result = remoteApi.getTicker(jackpotId)
                .then((response) => {
                    this.forceTickerRefresh.delete(jackpotId);
                    return response;
                }).catch((err) => {
                    this.forceTickerRefresh.delete(jackpotId);
                    return Promise.reject(err);
                });
            this.forceTickerRefresh.set(jackpotId, result);
        }

        return result;
    }

    private getKey(jackpotId: string) {
        return `${config.remoteTicker.tickerPrefix}:${jackpotId}`;
    }
}
