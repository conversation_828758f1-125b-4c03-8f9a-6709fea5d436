import {
    BaseRequest,
    Contribution,
    ContributionRequest,
    DeferredContributionResult,
    Jack<PERSON>,
    JackpotDefinition,
    <PERSON><PERSON>Game,
    JackpotGameResult,
    JackpotPoolsInformation,
    JackpotResult,
    JackpotStartMiniGameResult,
    JackpotWinResult,
    MiniGameRequest,
    PoolManipulation,
    PoolManipulationType,
    Ticker,
    TickerInformation,
    WinJackpotRequest,
    InternalTransferPoolManipulation,
    TickerAmount
} from "@skywind-group/sw-jpn-core";
import { JackpotModule } from "../modules/jackpotModule";
import { currencyService, getToEURMultiplier } from "./currency.service";
import {
    CURRENT_JACKPOT_CONTEXT_VERSION,
    JackpotContext,
    JackpotContextService,
    JackpotPlayerWin,
    JackpotStatus,
    JackpotWin,
    JackpotWinTransfer,
    JackpotWinType
} from "../../definition";
import * as ContextVersions from "./jackpot.context.version";
import * as Errors from "../errors";
import { round } from "../utils/rounding";
import { safeExchange, toMajorUnits, toMinorUnits } from "../utils/utils";
import config from "../config";
import {
    LocalWalletContributionParams,
    LocalWalletWinParams,
} from "../modules/walletParams";

export function createPlayerContributions(contributions: Contribution[],
                                          exchangeRate?: number,
                                          precision?: number): Contribution[] {
    return contributions.map((c) => {
        return {
            pool: c.pool,
            seed: exchangeRate && c.seed ? round(c.seed / exchangeRate, precision) : c.seed,
            progressive: exchangeRate && c.progressive ? round(c.progressive / exchangeRate, precision) : c.progressive
        };
    });
}

export interface TickerOptions {
    withPots?: boolean;
    skipError?: boolean;
    withVirtualSeed?: boolean;
}

/**
 * Stateful object to interact with Jackpot game logic and process game results.
 */
export interface JackpotGameFlow extends Jackpot {

    jpModule: JackpotModule;
    game: JackpotGame;
    request: BaseRequest;
    transactionId: string;

    /**
     * Calculate contribution amount and check game win
     */
    contribute(): Promise<void>;

    /**
     * Check jackpot game win
     */
    checkWin(): Promise<void>;

    /**
     * Play jackpot mini game
     */
    winMiniGame(): Promise<void>;

    /**
     * Trigger jackpot win
     */
    winJackpot(): Promise<void>;

    /**
     * Validate jackpot win eligible
     */
    validateCheckWin(): Promise<void>;

    /**
     * True if collected contributions
     */
    hasContributions(): boolean;

    /**
     * Returns collected contributions
     */
    getContributions(): Contribution[];

    /**
     * Returns collected contributions in player currency
     * and with incoming precision
     */
    getPlayerContributions(): Contribution[];

    /**
     * True if won jackpot mini game
     */
    hasWonMiniGame(): boolean;

    /**
     * Returns mini game result
     */
    getMiniGame(): JackpotStartMiniGameResult;

    /**
     * True if won jackpot
     */
    hasWonJackpot(): boolean;

    /**
     * Returns collected jackpot wins
     */
    getWins(): JackpotWin[];

    /**
     * Calculates jackpot win amounts according to current game results
     */
    calculateWins(): Promise<JackpotWin[]>;

    /**
     * Returns collected game result
     */
    getGameResult(): JackpotResult;

    /**
     * Calculates total contribution amount collected from all the contributions in Player currency
     */
    getPlayerTotalContribution(): number;

    /**
     * Summarize game results and commit available jackpot contributions and wins
     */
    finalize(): Promise<void>;

    /**
     * Continue contribution for deferred contribution.
     */
    continueDeferredContribute(result: DeferredContributionResult): Promise<void>;

    /**
     * Force update of cached ticker
     */
    refreshTicker(): Promise<void>;

    hasWonJackpotFromMiniGame(): boolean;

    /**
     * Return jackpot ticker in player currency
     */
    getPlayerTicker(opts: TickerOptions): Promise<TickerInformation>;

    getPoolManipulations(): PoolManipulation[];

    getPlayerBetAmount(): number;

    /**
     * Return jackpot ticker in player currency for lobby (game can override default ticker logic)
     */
    getTickerForLobby(opts: TickerOptions): Promise<TickerInformation>;

    getContributionParams(): LocalWalletContributionParams;

    getWinParams(): LocalWalletWinParams;

}

const START_MINI_GAME_TYPES = ["start-mini-game", "start-instant-jp-mini-game"];

/**
 * Jackpot game flow implementation
 */
export class JackpotGameFlowImpl implements JackpotGameFlow {

    private currentTicker: Ticker;

    protected contributions: Contribution[];
    protected playerContributions: Contribution[];
    protected gameResult: JackpotResult;
    protected wins: JackpotWin[];
    protected context: JackpotContext;
    protected wonFromMiniGame: boolean = false;

    protected contributionParams: LocalWalletContributionParams;
    protected winParams: LocalWalletWinParams;

    public transactionId: string;
    private poolManipulations: PoolManipulation[];

    constructor(public request: BaseRequest, public jpModule: JackpotModule, public game: JackpotGame,
                private contextService: JackpotContextService) {
        this.transactionId = request.transactionId;
    }

    public getPlayerBetAmount(): number {
        if (this.context && this.context.betAmount) {
            return this.context.betAmount;
        }

        if (this.request.type === "contribution") {
            return this.request.amount;
        } else if (["win-jackpot", "mini-game", "check-win", "checkWin"].includes(this.request.type)) {
            return this.request.betAmount;
        }
    }

    public get id(): string {
        return this.jpModule.instance.id;
    }

    public get definition(): JackpotDefinition {
        return this.jpModule.instance.definition;
    }

    public async getTicker(): Promise<Ticker> {
        if (!this.currentTicker) {
            this.currentTicker = await this.jpModule.getTicker();
        }
        return this.currentTicker;
    }

    public async contribute(): Promise<void> {
        const request: ContributionRequest = this.request as ContributionRequest;
        // do the actual contribution

        this.currentTicker = await this.getTicker();

        // TODO analyze impact of floating point error here
        const contributionAmount: number = request.amount * this.jpModule.exchangeRate;
        const precision = this.getContributionPrecision();
        const gameContributions = this.game.getContributions(contributionAmount, request, this.currentTicker.pools);
        this.contributions = this.createContributions(gameContributions, this.jpModule.instance.precision);
        this.playerContributions = createPlayerContributions(gameContributions, this.jpModule.exchangeRate, precision);

        // update ticker value with expected contributions
        for (const contribution of this.contributions) {
            const pool = this.currentTicker.pools[contribution.pool];
            pool.seed += contribution.seed || 0;
            pool.progressive += contribution.progressive || 0;
            pool.amount += contribution.progressive || 0;
        }

        if (request.amount) {
            if (this.game.getAdditionalPoolManipulations) {
                this.poolManipulations = this.game.getAdditionalPoolManipulations(this.currentTicker.pools);
            }

            this.gameResult = await this.game.checkWin(request);
        }
    }

    public async checkWin(): Promise<void> {
        this.gameResult = await this.game.checkWin(this.request);
    }

    public async winMiniGame(): Promise<void> {
        this.gameResult = await this.game.winMiniGame(this.request as MiniGameRequest);
        this.wonFromMiniGame = true;
    }

    public async winJackpot(): Promise<void> {
        this.gameResult = await this.game.winJackpot(this.request as WinJackpotRequest);
    }

    public async validateCheckWin(): Promise<void> {
        if (this.gameResult) {
            this.gameResult = await this.game.validateCheckWin(this.gameResult);
        }
    }

    public hasContributions(): boolean {
        return this.contributions && this.contributions.length > 0;
    }

    public getContributions(): Contribution[] {
        return this.contributions;
    }

    public getContributionParams(): LocalWalletContributionParams {
        return this.contributionParams;
    }

    public getPlayerContributions(): Contribution[] {
        return this.playerContributions;
    }

    public hasWonMiniGame(): boolean {
        if (this.gameResult) {
            const jpGameResult: JackpotGameResult = this.gameResult[0] ? this.gameResult[0] :
                                                    this.gameResult as JackpotGameResult;
            // process start-mini-game or start-instant-jp-mini-game event (should be only one)
            return START_MINI_GAME_TYPES.includes(jpGameResult.type);
        }

        return false;
    }

    public getMiniGame(): JackpotStartMiniGameResult {
        return this.gameResult[0] ? this.gameResult[0] : this.gameResult as JackpotStartMiniGameResult;
    }

    public hasWonJackpot(): boolean {
        if (this.gameResult) {
            const jpGameResult: JackpotGameResult = this.gameResult[0] ? this.gameResult[0] :
                                                    this.gameResult as JackpotGameResult;
            return jpGameResult.type === "win";
        }

        return false;
    }

    public async refreshTicker(): Promise<void> {
        this.currentTicker = await this.jpModule.getTicker(true);
    }

    public async calculateWins(): Promise<JackpotWin[]> {
        if (!this.hasWonJackpot()) {
            return;
        }

        const ticker = await this.getTicker();
        const payouts = await this.game.getWinPayouts(this.gameResult as JackpotWinResult, ticker.pools);

        const wins: JackpotWin[] = [];

        for (const payout of payouts) {

            let win: JackpotWin;

            if (payout.transferPool !== undefined) {
                win = {
                    type: JackpotWinType.TRANSFER,
                    ...payout
                } as JackpotWinTransfer;
            } else {

                const exchangedWinAmount = currencyService.exchangeWithRate(payout.amount,
                    1 / this.jpModule.exchangeRate, this.jpModule.playerInfo.currency);

                win = {
                    type: JackpotWinType.PLAYER,
                    ...payout,
                    playerAmount: exchangedWinAmount,
                    exchangeRate: this.jpModule.exchangeRate,
                } as JackpotPlayerWin;
            }

            wins.push(win);
        }

        return wins;
    }

    public getWins(): JackpotWin[] {
        return this.wins;
    }

    public getWinParams(): LocalWalletWinParams {
        return this.winParams;
    }

    public getGameResult(): JackpotResult {
        return this.gameResult;
    }

    public getPlayerTotalContribution(): number {
        if (!this.playerContributions) {
            return 0;
        }

        let totalContribution: number = 0;
        for (const contribution of this.playerContributions) {
            totalContribution += contribution.seed || 0;
            totalContribution += contribution.progressive || 0;
        }

        return totalContribution;
    }

    public async finalize(): Promise<void> {
        this.validateGameResult();
        this.validatePoolManipulations();

        await this.updateContext();

        try {
            const result = await this.jpModule.processGameFlow(this);

            this.contributions = result.contributions;
            this.playerContributions = result.playerContributions;
            this.gameResult = result.gameResult;
            this.wins = result.wins;

            this.contributionParams = result.contributionParams;
            this.winParams = result.winParams;

        } catch (err) {
            if (err instanceof Errors.ConcurrentJackpotWin) {
                // clean up context with failed concurrent win info because win payout has to be recalculated
                await this.contextService.remove({
                    jackpotId: this.jpModule.instance.id,
                    playerInfo: this.jpModule.playerInfo,
                    transactionId: this.request.transactionId
                } as JackpotContext);
            }
            return Promise.reject(err);
        }

        await this.updateContext(true);
    }

    public getPoolManipulations(): PoolManipulation[] {
        return this.poolManipulations;
    }

    private async updateContext(finalized?: boolean): Promise<void> {
        if (this.hasWonMiniGame()) {
            this.context = {
                jackpotId: this.jpModule.instance.id,
                transactionId: this.request.transactionId,
                externalId: this.request.externalId,
                playerInfo: this.jpModule.playerInfo,
                status: JackpotStatus.MINI_GAME,
                miniGame: {},
                roundId: this.request.roundId,
                result: this.getGameResult(),
                version: CURRENT_JACKPOT_CONTEXT_VERSION,
                betAmount: this.getPlayerBetAmount()
            };

            await this.contextService.update(this.context);
        } else if (this.hasWonJackpot()) {
            this.context = {
                jackpotId: this.jpModule.instance.id,
                transactionId: this.request.transactionId,
                externalId: this.request.externalId,
                playerInfo: this.jpModule.playerInfo,
                roundId: this.request.roundId,
                status: finalized ? JackpotStatus.PAID : JackpotStatus.WON,
                result: this.getGameResult(),
                win: this.getWins(),
                version: CURRENT_JACKPOT_CONTEXT_VERSION,
                betAmount: this.getPlayerBetAmount(),
                contributionParams: this.getContributionParams(),
                winParams: this.getWinParams()
            };

            await this.contextService.update(this.context);
        } else if (this.context) {
            await this.contextService.remove({
                jackpotId: this.jpModule.instance.id,
                playerInfo: this.jpModule.playerInfo,
                transactionId: this.request.transactionId
            } as JackpotContext);
        }
    }

    private validateGameResult() {
        if (!this.gameResult) {
            return;
        }

        const results = Array.isArray(this.gameResult) ? this.gameResult : [this.gameResult];

        if (results.length === 1 && START_MINI_GAME_TYPES.includes(results[0].type)) {
            return;
        }

        if (results.find((win) => (win.type !== "win"))) {
            throw new Errors.InvalidJackpotResult();
        }
    }

    private getContributionPrecision(): number {
        return this.request?.contributionPrecision as number;
    }

    private createContributions(contributions: Contribution[], precision: number): Contribution[] {
        return contributions.map((c) => {
            const contribution: Contribution = {
                pool: c.pool,
                seed: toMajorUnits(toMinorUnits(c.seed, precision), precision),
                progressive: toMajorUnits(toMinorUnits(c.progressive, precision), precision)
            };
            if (c.properties) {
                contribution.properties = c.properties;
            }
            return contribution;
        });
    }

    public async continueDeferredContribute(result: DeferredContributionResult): Promise<void> {
       this.contributions = result.contributions;
       this.playerContributions = result.playerContributions;
       this.gameResult = result.gameResult;
    }

    public hasWonJackpotFromMiniGame(): boolean {
        return this.hasWonJackpot() && this.wonFromMiniGame;
    }

    public async getPlayerTicker(opts: TickerOptions): Promise<TickerInformation> {
        const ticker = await this.jpModule.getTicker();
        const exchangeRate = this.jpModule.exchangeRate;
        const targetCurrency = this.jpModule.playerInfo.currency;

        const tickerAmounts = this.game.getTickerAmount(ticker.pools);

        const pools = this.preparePoolsInformation(tickerAmounts, exchangeRate, targetCurrency, opts);

        const result: TickerInformation = {
            jackpotId: ticker.id,
            jackpotType: this.jpModule.instance.type,
            jackpotBaseType: this.jpModule.instance.baseType,
            pools
        };
        if (ticker.isDisabled) {
            result.isDisabled = ticker.isDisabled;
        }

        return result;
    }

    public async getTickerForLobby(opts: TickerOptions): Promise<TickerInformation> {
        if (config.toEURMultiplier.enableCustomTicker && this.game.getLobbyTicker) {
            const ticker = await this.jpModule.getTicker();
            const exchangeRate = this.jpModule.exchangeRate;
            const targetCurrency = this.jpModule.playerInfo.currency;
            const toEurMultiplier = await getToEURMultiplier(targetCurrency);
            if (toEurMultiplier) {
                const lobbyTicker: TickerAmount = this.game.getLobbyTicker(ticker.pools, toEurMultiplier);
                const pools = this.preparePoolsInformation(lobbyTicker, exchangeRate, targetCurrency, opts);
                const result: TickerInformation = {
                    jackpotId: ticker.id,
                    jackpotType: this.jpModule.instance.type,
                    jackpotBaseType: this.jpModule.instance.baseType,
                    pools,
                };
                if (ticker.isDisabled) {
                    result.isDisabled = ticker.isDisabled;
                }

                return result;
            }
        }
        return this.getPlayerTicker(opts);
    }

    private preparePoolsInformation(tickerAmounts: TickerAmount, exchangeRate: number, targetCurrency: string, opts: TickerOptions): JackpotPoolsInformation {
        const pools: JackpotPoolsInformation = {};
        for (const pool of Object.keys(tickerAmounts)) {
            const poolDef = this.jpModule.instance.definition.list.find((item) => item.id === pool);

            const tickerAmount = tickerAmounts[pool];

            let amount = currencyService.exchangeWithRate(tickerAmount.amount, 1 / exchangeRate, targetCurrency);
            if (poolDef.virtualSeed && opts.withVirtualSeed) {
                const virtualSeedAmount = poolDef.virtualSeed[targetCurrency] ||
                    currencyService.exchangeWithRate(poolDef.virtualSeed.amount, 1 / exchangeRate, targetCurrency);
                amount += virtualSeedAmount;
            }

            if (this.jpModule.instance.definition.winCapping) {
                const winCapping = currencyService.exchangeWithRate(
                    this.jpModule.instance.definition.winCapping, 1 / exchangeRate, targetCurrency);
                if (amount > winCapping) {
                    amount = winCapping;
                }
            }

            pools[pool] = {
                amount: +amount.toPrecision(9)
            };
            if (opts.withPots) {
                pools[pool].seed = tickerAmount.seed;
                pools[pool].progressive = tickerAmount.progressive;
            }
            if (poolDef.info) {
                pools[pool].info = this.exchangeEL(poolDef.info, 1 / exchangeRate, targetCurrency);
            }
        }
        return pools;
    }

    /**
     * Converts values in the object with exchange to target currency. Uses expression language, e.g.
     * { value: "exchange(10)" } with exchange rate 2 will be converted to { value: 20 }
     */
    private exchangeEL(obj: any, exchangeRate: number, targetCurrency: string): any {
        const result = {};
        for (const key of Object.keys(obj)) {
            let value = obj[key];
            if (typeof value === "string") {
                const toExchange = value.match(/^exchange\((\d+(.\d+)?)\)$/);
                if (toExchange && toExchange[1]) {
                   value = safeExchange(Number(toExchange[1]), exchangeRate);
                }
            } else if (typeof value === "object") {
                value = this.exchangeEL(value, exchangeRate, targetCurrency);
            }
            result[key] = value;
        }
        return result;
    }

    private validatePoolManipulations() {
        if (!this.poolManipulations) {
            return;
        }

        if (!Array.isArray(this.poolManipulations)) {
            throw new Errors.UnsupportedPoolManipulation();
        }
        this.poolManipulations.forEach(m => {
            const supportedManipulations: string[] = Object.keys(PoolManipulationType).map(k => PoolManipulationType[k]);
            if (!supportedManipulations.includes(m.type)) {
                throw new Errors.UnsupportedPoolManipulation();
            }
            if (m.type === PoolManipulationType.InternalTransferFromSeed) {
                const transfer: InternalTransferPoolManipulation = m as InternalTransferPoolManipulation;
                if (!(transfer.payload.restValue >= 0)) {
                    throw new Errors.UnsupportedPoolManipulation();
                }
            }
        });
    }

}

/**
 * Jackpot game flow restored from context
 */
export class RestoredJackpotGameFlowImpl extends JackpotGameFlowImpl {

    constructor(request: BaseRequest, jpModule: JackpotModule, game: JackpotGame, context: JackpotContext,
                contextService: JackpotContextService) {
        super(request, jpModule, game, contextService);

        this.context = context;

        if (!request.externalId) {
            request.externalId = context.externalId;
        }
        this.gameResult = context.result;
        if (context.win) {
            this.wins = Array.isArray(context.win) ? context.win : [context.win];
            this.jpModule.exchangeRate = (context.win[0] || context.win).exchangeRate;
        }
        if (context.winParams) {
            this.winParams = context.winParams;
        }
        if (context.contributionParams) {
            this.contributionParams = context.contributionParams;
        }
    }

    public async checkWin(): Promise<void> {
        // do nothing because game result is taken from context
    }

    public async winJackpot(): Promise<void> {
        // do nothing because game result is taken from context
    }

    public getPoolManipulations(): PoolManipulation[] {
        return undefined;
    }
}

export async function createJackpotGameFlow(request: BaseRequest,
                                            jpModule: JackpotModule,
                                            game: JackpotGame,
                                            contextService: JackpotContextService,
                                            ignoreContext: boolean = false): Promise<JackpotGameFlow> {
    let flow: JackpotGameFlow = new JackpotGameFlowImpl(request, jpModule, game, contextService);
    game.jackpot = flow;

    let context: JackpotContext = await contextService.find(jpModule.instance.id,
        jpModule.playerInfo, request.transactionId);
    context = await ContextVersions.toCurrentVersion(context, jpModule, game);

    if (context) {
        flow = new RestoredJackpotGameFlowImpl(request, jpModule, game, context, contextService);
        game.jackpot = flow;
    }

    return flow;
}
