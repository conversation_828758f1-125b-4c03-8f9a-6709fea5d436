import {
    AuditInfo,
    <PERSON>pot<PERSON>udit,
    JackpotAuditCreateData,
    JackpotAuditInitiator,
    JackpotAuditType,
    JackpotInstance,
} from "../api/model";
import logger from "../utils/logger";
import { WhereOptions } from "sequelize";
import { DBJackpotAudit, IJackpotAuditModel, JackpotAuditModel } from "../models/jackpotAudit";

const log = logger("jackpot:audit");

export const DEFAULT_AUDIT_LIST_OFFSET = 0;
export const DEFAULT_AUDIT_LIST_LIMIT = 100;

export function auditJackpot(auditType: JackpotAuditType) {
    return (target, key, descriptor) => {
        const originalMethod = descriptor.value;

        descriptor.value = async function(...argsOfDecoratedFunc: any[]) {
            const serviceInstance = this;

            const result = await originalMethod.apply(serviceInstance, argsOfDecoratedFunc);

            await new JackpotAuditService().auditJackpot(result, auditType, serviceInstance.audit);

            return result;
        };

        return descriptor;
    };
}

export class JackpotAuditService {

    public static model: IJackpotAuditModel = JackpotAuditModel();

    public async create(data: JackpotAuditCreateData): Promise<void> {
        try {
            await JackpotAuditService.model.create(new DBJackpotAudit(data));
        } catch (err) {
            log.error(err, "Failed to store audit", data);
        }
    }

    public async auditJackpot(instance: JackpotInstance,
                              auditType: JackpotAuditType,
                              info: AuditInfo = { initiatorType: JackpotAuditInitiator.SYSTEM }): Promise<void> {
        return this.create({
            jackpotId: info.id || instance.id,
            type: auditType,
            history: instance || {},
            ts: new Date(),
            ...info
        });
    }

    public async findAll(query: WhereOptions<JackpotAudit> = {} as any,
                         offset = DEFAULT_AUDIT_LIST_OFFSET,
                         limit = DEFAULT_AUDIT_LIST_LIMIT): Promise<JackpotAudit[]> {
        const regions = await JackpotAuditService.model.findAll({
            where: query,
            order: [["ts", "DESC"]],
            offset,
            limit
        });
        return regions.map((audit) => audit.toInfo());
    }
}

export default new JackpotAuditService();
