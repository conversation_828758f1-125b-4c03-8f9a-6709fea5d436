import { Cache } from "../utils/cache";
import { JackpotInternalInstance } from "../api/model";

let cache: Cache<string, JackpotInternalInstance>;

export function init(search): Cache<string, JackpotInternalInstance> {
    if (!cache) {
        cache = new Cache<string, JackpotInternalInstance>("jp-instances", search);
    }
    return cache;
}

export function get(): Cache<string, JackpotInternalInstance> {
    return cache;
}
