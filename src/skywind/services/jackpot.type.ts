import * as Sequelize from "sequelize";
import { JackpotDefinition } from "@skywind-group/sw-jpn-core";
import { AuditInfo, JackpotType } from "../api/model";
import * as GameService from "./game.service";
import * as Errors from "../errors";
import * as _ from "lodash";
import logger from "../utils/logger";
import { lookupJackpotType } from "./game.service";
import { Op, FindOptions } from "sequelize";
import { JackpotInstanceService } from "./jackpot.instance";
import {
    DBJackpotType,
    IJackpotTypeModel,
    JackpotTypeDBInstance,
    JackpotTypeModel,
} from "../models/jackpotType";

const log = logger("jackpot:type");

export class JackpotTypeService {

    public static model: IJackpotTypeModel = JackpotTypeModel();

    constructor(private audit?: AuditInfo) {
    }

    public async createTypeFromModule(gameId: string,
                                      configurable: boolean = false,
                                      overridable: boolean = false,
                                      canBeDisabled: boolean = false): Promise<JackpotType> {
        const jpTypeFromJpGames = await lookupJackpotType(gameId);
        if (!jpTypeFromJpGames) {
            return Promise.reject(new Errors.JackpotTypeNotFound(gameId));
        }

        try {
            jpTypeFromJpGames.configurable = configurable;
            jpTypeFromJpGames.overridable = overridable;
            jpTypeFromJpGames.canBeDisabled = canBeDisabled;
            return this.create(jpTypeFromJpGames);
        } catch (err) {
            if (err instanceof Sequelize.UniqueConstraintError) {
                return Promise.reject(new Errors.JackpotTypeAlreadyExist());
            } else {
                return Promise.reject(err);
            }
        }
    }

    public async updateTypeFromModule(name: string): Promise<JackpotType> {
        const type: JackpotTypeDBInstance = await JackpotTypeService.model
            .findOne({ where: { name: name } });
        if (!type) {
            return Promise.reject(new Errors.JackpotTypeNotFound(name));
        }
        const typeFromModule = await lookupJackpotType(type.jpGameId);
        if (!typeFromModule) {
            return Promise.reject(new Errors.JackpotTypeNotFound(name));
        }

        if (!this.jackpotDefinitionValid(typeFromModule.definition,
            { supportsWinCap: typeFromModule.supportsWinCap })) {
            return Promise.reject(new Errors.ValidationError("Jackpot definition is not valid"));
        }
        if (type.definition.currency !== typeFromModule.definition.currency) {
            return Promise.reject(new Errors.ValidationError("Cannot update jackpot currency"));
        }
        type.definition = typeFromModule.definition;
        type.baseType = typeFromModule.baseType;
        log.info("Update jackpot type", type.toInfo());
        await type.save();
        await new JackpotInstanceService(this.audit).updateJackpotType(type);
        return type.toInfo();
    }

    public async validateTypes(): Promise<void> {
        const reasons: string[] = [];

        const jpGamesTypes: JackpotType[] = await GameService.lookupJackpotTypes();

        for (const jpGamesType of jpGamesTypes) {
            try {
                const storedType = await this.find(jpGamesType.name);
                _.omit(storedType, "configurable", "overridable", "canBeDisabled");

                if (!_.isEqual(storedType, jpGamesType)) {
                    reasons.push(`Stored jackpot type is not equal to default config ` +
                        `${JSON.stringify(storedType)} vs default: ${JSON.stringify(jpGamesType)}`);
                }
            } catch (err) {
                if (err instanceof Errors.JackpotTypeNotFound) {
                    reasons.push(`Can't find ${jpGamesType.name} type in database`);
                } else {
                    reasons.push(`Unknown error: ${err}`);
                }
            }
        }

        if (reasons.length > 0) {
            return Promise.reject(new Errors.JpTypeDistinguishError(reasons));
        }
    }

    public async createAllDefined(): Promise<void> {
        const types: JackpotType[] = await GameService.lookupJackpotTypes();
        const toCreate: Promise<void>[] = types.map(async (type) => {
            let storedType;
            try {
                storedType = await this.find(type.name);
                _.omit(storedType, "configurable", "overridable", "canBeDisabled");
            } catch (err) {
                if (!(err instanceof Errors.JackpotTypeNotFound)) {
                    throw err;
                }
            }
            try {
                if (!storedType) {
                    await this.create(type);
                    log.info("Jackpot type created", type);
                } else if (!_.isEqual(storedType, type)) {
                    log.warn("Stored jackpot type is not equal to default config",
                        JSON.stringify(storedType),
                        "vs default:",
                        JSON.stringify(type));
                }
            } catch (err) {
                if (!(err instanceof Sequelize.UniqueConstraintError)) {
                    log.error(err, "Failed to create jackpot type", JSON.stringify(type));
                }
            }
        });
        await Promise.all(toCreate);
    }

    public async create(info: JackpotType): Promise<JackpotType> {
        if (!this.jackpotDefinitionValid(info.definition, { supportsWinCap: info.supportsWinCap })) {
            return Promise.reject(new Errors.ValidationError("Jackpot definition is not valid"));
        }
        if (!GameService.exists(info.jpGameId)) {
            return Promise.reject(new Errors.JackpotGameNotFound(info.jpGameId));
        }
        log.info("Create jackpot type", info);
        try {
            const type: JackpotTypeDBInstance = await JackpotTypeService.model.create(new DBJackpotType(info));
            return type.toInfo();
        } catch (err) {
            if (err instanceof Sequelize.UniqueConstraintError) {
                throw new Errors.JackpotTypeAlreadyExist();
            } else {
                throw err;
            }
        }
    }

    public async findAll(names?: string[]): Promise<JackpotType[]> {
        let queryOptions: FindOptions<JackpotType>;
        if (Array.isArray(names)) {
            queryOptions = {
                where: {
                    name: {
                        [Op.in]: names
                    }
                }
            };
        }

        const types: JackpotTypeDBInstance[] = await JackpotTypeService.model.findAll(queryOptions);
        return types.map(type => type.toInfo());
    }

    public async find(name: string): Promise<JackpotType> {
        const type: JackpotTypeDBInstance = await JackpotTypeService.model.findOne({ where: { name: name } });
        if (!type) {
            return Promise.reject(new Errors.JackpotTypeNotFound(name));
        }
        return type.toInfo();
    }

    public async findId(name: string): Promise<[number, JackpotType]> {
        const type: JackpotTypeDBInstance = await JackpotTypeService.model.findOne({ where: { name: name } });
        if (!type) {
            return Promise.reject(new Errors.JackpotTypeNotFound(name));
        }
        return [type.internalId, type.toInfo()];
    }

    public async update(name: string, info: JackpotType): Promise<JackpotType> {
        const type: JackpotTypeDBInstance = await JackpotTypeService.model
            .findOne({ where: { name: name } });
        if (!type) {
            return Promise.reject(new Errors.JackpotTypeNotFound(name));
        }
        if (!type.configurable && (info.jpGameId || info.definition)) {
            return Promise.reject(new Errors.ValidationError("Jackpot type is not configurable"));
        }
        if (info.jpGameId) {
            if (!GameService.exists(info.jpGameId)) {
                return Promise.reject(new Errors.JackpotGameNotFound(info.jpGameId));
            }
            type.jpGameId = info.jpGameId;
        }

        if (info.supportsWinCap !== undefined) {
            type.supportsWinCap = info.supportsWinCap;

            if (!type.supportsWinCap) {
                type.definition.winCapping = undefined;
            }
        }

        if (info.definition) {
            if (!this.jackpotDefinitionValid(info.definition, { supportsWinCap: type.supportsWinCap })) {
                return Promise.reject(new Errors.ValidationError("Jackpot definition is not valid"));
            }
            if (type.definition.currency !== info.definition.currency) {
                return Promise.reject(new Errors.ValidationError("Cannot update jackpot currency"));
            }
            type.definition = info.definition;
        }
        if (info.configurable !== undefined) {
            type.configurable = !!info.configurable;
        }
        if (info.overridable !== undefined) {
            const overrides = await new JackpotInstanceService(this.audit).findTypeOverride(type.internalId);
            if (!info.overridable && overrides.length) {
                return Promise.reject(new Errors.ValidationError(
                    `Definition has overrides in jackpots ${overrides.map((instance) => instance.id)}`));
            }
            type.overridable = !!info.overridable;
        }
        if (info.canBeDisabled !== undefined) {
            type.canBeDisabled = !!info.canBeDisabled;
        }
        if (info.baseType !== undefined) {
            type.baseType = info.baseType;
        }
        log.info("Update jackpot type", info);
        await type.save();
        await new JackpotInstanceService(this.audit).updateJackpotType(type);
        return type.toInfo();
    }

    public async remove(name: string): Promise<void> {
        log.info("Remove jackpot type", name);
        try {
            const count = await JackpotTypeService.model.destroy({ where: { name: name } });
            if (!count) {
                return Promise.reject(new Errors.JackpotTypeNotFound(name));
            }
        } catch (err) {
            if (err instanceof Sequelize.ForeignKeyConstraintError) {
                return Promise.reject(new Errors.ValidationError("Has jackpot instances"));
            }
            return Promise.reject(err);
        }
    }

    private jackpotDefinitionValid(definition: JackpotDefinition, options: { supportsWinCap?: boolean } = {}): boolean {
        const fieldsDefined = definition && definition.currency &&
            definition.list && Array.isArray(definition.list) && definition.list.length > 0;
        if (!fieldsDefined) {
            return fieldsDefined;
        }

        if (definition.winCapping &&
            (!options.supportsWinCap || typeof definition.winCapping !== "number" || definition.winCapping <= 0)) {
            return false;
        }

        const validItems = definition.list.filter(item => item.id &&
            ((item.seed && item.seed.amount >= 0) || !item.seed) &&
            ((item.virtualSeed && item.virtualSeed.amount >= 0) || !item.virtualSeed));
        return validItems.length === definition.list.length;
    }
}

export default new JackpotTypeService();
