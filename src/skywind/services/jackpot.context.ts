import * as redis from "../storage/redis";
import { PlayerInformation } from "@skywind-group/sw-jpn-core";
import { measures } from "@skywind-group/sw-utils";
import { JackpotContext, JackpotContextService } from "../../definition";
const measure = measures.measure;

export class JackpotContextDbService implements JackpotContextService {

    @measure({ name: "JackpotContextService.find", isAsync: true, debugOnly: true })
    public async find(jpId: string, playerInfo: PlayerInformation, transactionId: string): Promise<JackpotContext> {
        const client = await redis.get();
        try {
            const contextKey = this.contextKey(jpId, playerInfo, transactionId);
            const data = await client.get(contextKey);
            return data ? JSON.parse(data) : undefined;
        } finally {
            redis.release(client);
        }
    }

    @measure({ name: "JackpotContextService.update", isAsync: true, debugOnly: true })
    public async update(context: JackpotContext): Promise<void> {
        const client = await redis.get();
        try {
            const contextKey = this.contextKey(context.jackpotId, context.playerInfo, context.transactionId);
            const contextDb = JSON.stringify(context);
            await client.set(contextKey, contextDb);
            await redis.waitForSync(client);
        } finally {
            redis.release(client);
        }
    }

    @measure({ name: "JackpotContextService.remove", isAsync: true, debugOnly: true })
    public async remove(context: JackpotContext): Promise<void> {
        const client = await redis.get();
        try {
            const contextKey = this.contextKey(context.jackpotId, context.playerInfo, context.transactionId);
            await client.del(contextKey);
            await redis.waitForSync(client);
        } finally {
            redis.release(client);
        }
    }

    private contextKey(jpId: string, player: PlayerInformation, transactionId: string): string {
        return `jackpot:context:${jpId}:${player.brandId}:${player.playerCode}:${transactionId}`;
    }
}

const service: JackpotContextService = new JackpotContextDbService();
export default service;
