import { JackpotLookup, WalletService } from "../../definition";
import {
    BaseRequest,
    PlayerInformation,
    Ticker,
    JackpotGame,
    Jackpot,
    DeferredContributionResult,
    PoolManipulation
} from "@skywind-group/sw-jpn-core";
import { JackpotWallet } from "../modules/jackpotWallet";
import { createExchangeService, currencyService } from "./currency.service";
import { JackpotInstance, JackpotInternalInstance } from "../api/model";
import {
    BaseWalletParams,
    ContributionPayout,
    convertContributionsToContributionsPayout,
    WalletWinPayout
} from "../modules/walletParams";
import { JackpotGameFlowResult, JackpotModule } from "../modules/jackpotModule";
import { createRandomGenerator } from "../utils/random";
import * as GameService from "./game.service";
import logger from "../utils/logger";
import { JackpotGameFlowImpl } from "./jackpotGameFlow";
import { LocalJackpotModule } from "../modules/localJackpotModule";
import { JackpotInstanceNotFound, ValidationError } from "../errors";

const log = logger("jackpot:service:remote");

export interface TransactionIdBatch {
    transactionIds: string[];
}

export interface RemoteGameFlowResult extends JackpotGameFlowResult {
    jackpotId: string;
}

export interface RemoteGameFlowProcessResult extends RemoteGameFlowResult {
    winPayouts?: WalletWinPayout[];
    ticker: Ticker;
}

export interface RemoteGameFlowRequest {
    transactionId: string;
    playerInfo: PlayerInformation;
    requestRegion: string;
    request: BaseRequest;
    results: RemoteGameFlowResult[];
}

export interface RemoteGameFlowResponse {
    results: RemoteGameFlowProcessResult[];
}

export class RemoteJackpotGameFlowImpl extends JackpotGameFlowImpl {

    private winPayouts: WalletWinPayout[];

    constructor(remoteRequest: RemoteGameFlowRequest, result: RemoteGameFlowResult,
                jpModule: JackpotModule, game: JackpotGame) {
        super(remoteRequest.request, jpModule, game, undefined);
        this.transactionId = remoteRequest.transactionId;
        this.contributions = result.contributions;
        this.playerContributions = result.playerContributions;
        this.gameResult = result.gameResult;
        this.wins = result.wins;
    }

    public async contribute(): Promise<void> {
        // do nothing because already calculated on remote side
    }

    public async checkWin(): Promise<void> {
        // do nothing because already calculated on remote side
    }

    public async winJackpot(): Promise<void> {
        // do nothing because already calculated on remote side
    }

    public async winMiniGame(): Promise<void> {
        // do nothing because already calculated on remote side
    }

    public getWinPayouts(): WalletWinPayout[] {
        return this.winPayouts;
    }

    public async finalize(): Promise<void> {
        await this.refreshTicker();
        await this.validateCheckWin();

        const result = await this.jpModule.processGameFlow(this);

        this.contributions = result.contributions;
        this.playerContributions = result.playerContributions;
        this.gameResult = result.gameResult;
        this.wins = result.wins;
        this.winPayouts = result.winPayouts;
    }

    public async continueDeferredContribute(result: DeferredContributionResult): Promise<void> {
        // do nothing
    }

    public getPoolManipulations(): PoolManipulation[] {
        return undefined;
    }
}

/**
 * Serves requests from remote JPN server.
 */
export class RemoteJackpotService {

    constructor(private lookup: JackpotLookup,
                private wallet: WalletService) {
    }

    public async findOrCreateJackpot(info: JackpotInstance, autoCreate?: boolean): Promise<JackpotInstance> {
        log.info("Find or create jackpot (autoCreate=%s): %j", autoCreate, info);
        try {
            const instance: JackpotInternalInstance = await this.lookup.findInternal(info.id);
            if (instance.type !== info.type) {
                return Promise.reject(new ValidationError("Remote jackpot has different jackpot type"));
            }
            if (!!instance.isTest !== !!info.isTest) {
                return Promise.reject(new ValidationError("Remote jackpot is (not) test"));
            }
            return instance;
        } catch (err) {
            if (err instanceof JackpotInstanceNotFound && autoCreate) {
                return this.lookup.create({ ...info, isGlobal: true });
            }

            return Promise.reject(err);
        }

    }

    public async generateTrxIds(count: number): Promise<TransactionIdBatch> {
        log.info("Generate trx ids %d", count);
        const trxIds: string[] = [];
        for (let i = 0; i < count; i += 1) {
            trxIds.push(await this.wallet.generateTransactionId());
        }

        return {
            transactionIds: trxIds
        };
    }

    public async getTicker(jackpotId: string): Promise<Ticker> {
        log.info("Get ticker %s", jackpotId);
        const instance = await this.lookup.findInternal(jackpotId);
        return new JackpotWallet(undefined, instance, 1, this.wallet).getTicker();
    }

    public async processRemoteGameFlow(req: RemoteGameFlowRequest): Promise<RemoteGameFlowResponse> {
        log.info("Process remote game flow", req);
        const response: RemoteGameFlowResponse = { results: [] };
        for (const r of req.results) {
            const jackpotModule = await this.getJackpotModule(req, r.jackpotId);
            const game = this.getGame(jackpotModule, req.request);
            const flow = new RemoteJackpotGameFlowImpl(req, r, jackpotModule, game);

            await flow.finalize();

            const contributionsPayout: ContributionPayout[] =
                convertContributionsToContributionsPayout(flow.getContributions());
            const result: RemoteGameFlowProcessResult = {
                jackpotId: r.jackpotId,
                gameResult: flow.getGameResult(),
                contributions: contributionsPayout,
                playerContributions: flow.getPlayerContributions(),
                wins: flow.getWins(),
                winPayouts: flow.getWinPayouts(),
                ticker: await flow.getTicker()
            };
            response.results.push(result);
        }
        return response;
    }

    private async getJackpotModule(flowRequest: RemoteGameFlowRequest, jackpotId: string): Promise<LocalJackpotModule> {
        const playerInfo = flowRequest.playerInfo;
        const instance: JackpotInternalInstance = await this.lookup.findInternal(jackpotId);
        const exchangeRate: number = flowRequest.request.exchangeRate ||
            currencyService.getExchangeRate(playerInfo.currency, instance.definition.currency);
        const params: BaseWalletParams = {
            fromRemote: true,
            remoteTrxId: flowRequest.request.transactionId,
            remoteTrxRegion: flowRequest.requestRegion,
            brandId: playerInfo.brandId,
            region: playerInfo.region,
            gameId: instance.jpGameId,
            gameCode: playerInfo.gameCode,
            playerCode: playerInfo.playerCode,
            playerCurrency: playerInfo.currency,
            roundId: flowRequest.request.roundId
        };
        const wallet = new JackpotWallet(params, instance, exchangeRate, this.wallet);
        return new LocalJackpotModule(wallet);
    }

    private getGame(module: JackpotModule, request: BaseRequest): JackpotGame {
        const exchangeService = createExchangeService(module.playerInfo.currency,
            module.instance.definition.currency,
            module.exchangeRate);
        const jackpot: Jackpot = {
            id: module.instance.id,
            definition: module.instance.definition,
            getTicker: () => module.getTicker()
        };
        const game: JackpotGame = GameService.load(module.instance.jpGameId,
            jackpot,
            exchangeService,
            createRandomGenerator(request));
        return game;
    }
}
