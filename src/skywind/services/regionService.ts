import * as Sequelize from "sequelize";
import { JackpotRegion } from "../api/model";
import * as Errors from "../errors";
import { parse as parseUrl } from "url";
import { get as getJackpotCache } from "./jackpotCache";
import {
    DBJackpotRegion,
    IJackpotRegionModel,
    JackpotRegionDBInstance,
    JackpotRegionModel,
} from "../models/jackpotRegion";

export class JackpotRegionService {

    public model: IJackpotRegionModel = JackpotRegionModel();

    public async create(data: JackpotRegion): Promise<JackpotRegion> {
        try {
            this.validateCreate(data);
            const dbRegion = await this.model.create(new DBJackpotRegion(data));
            return dbRegion.toInfo();
        } catch (err) {
            if (err instanceof Sequelize.UniqueConstraintError) {
                return Promise.reject(new Errors.ValidationError("Region already exists"));
            } else {
                return Promise.reject(err);
            }
        }
    }

    public async update(code: string, data: JackpotRegion): Promise<JackpotRegion> {
        this.validate(data);
        const region: JackpotRegionDBInstance = await this.model.findOne({ where: { code } });
        if (!region) {
            return Promise.reject(new Errors.RegionNotFoundError());
        }
        if (data.url) {
            region.url = data.url;
        }
        if (data.secureOptions) {
            region.secureOptions = data.secureOptions;
        }
        await region.save();
        getJackpotCache().invalidateAll();
        return region.toInfo();
    }

    public async find(code: string): Promise<JackpotRegion> {
        const region: JackpotRegionDBInstance = await this.model.findOne({ where: { code } });
        if (!region) {
            return Promise.reject(new Errors.RegionNotFoundError());
        }
        return region.toInfo();
    }

    public async findWithId(code: string): Promise<JackpotRegion&{ id: number }> {
        const region: JackpotRegionDBInstance = await this.model.findOne({ where: { code } });
        if (!region) {
            return Promise.reject(new Errors.RegionNotFoundError());
        }
        const info = region.toInfo();
        return { ...info, id: region.id };
    }

    public async findAll(): Promise<JackpotRegion[]> {
        const regions = await this.model.findAll({});
        return regions.map((region) => region.toInfo());
    }

    public async remove(code: string): Promise<void> {
        try {
            const removed = await this.model.destroy({ where: { code } });
            if (!removed) {
                return Promise.reject(new Errors.RegionNotFoundError());
            }
        } catch (err) {
            if (err instanceof Sequelize.ForeignKeyConstraintError) {
                return Promise.reject(new Errors.ValidationError("Region contains jackpot instances"));
            }
            return Promise.reject(err);
        }
    }

    private validateCreate(data: JackpotRegion) {
        if (typeof data.code !== "string") {
            throw new Errors.ValidationError("code is missing or not valid");
        }
        if (typeof data.url !== "string") {
            throw new Errors.ValidationError("url is missing or not valid");
        }
        this.validate(data);
    }

    private validate(data: JackpotRegion) {
        if (data.url !== undefined) {
            try {
                const url = parseUrl(data.url);
                if (!url.protocol || (url.protocol !== "http:" && url.protocol !== "https:") || !url.hostname) {
                    throw new Errors.ValidationError("url is not valid");
                }
            } catch (err) {
                throw new Errors.ValidationError("url is not valid");
            }
        }
        if (data.secureOptions && (typeof data.secureOptions !== "object" || Array.isArray(data.secureOptions))) {
            throw new Errors.ValidationError("secureOptions are not valid");
        }
    }
}

export default new JackpotRegionService();
