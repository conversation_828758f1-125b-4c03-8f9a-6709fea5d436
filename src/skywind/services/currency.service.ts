import {
    createCurrencyExchange,
    CurrencyExchange,
    ExchangeRateType,
    GameLimitsCurrency,
    getGameLimitsCurrency
} from "@skywind-group/sw-currency-exchange";
import { getRedisPool } from "../storage/redis";
import { CurrencyExchangeService } from "@skywind-group/sw-jpn-core";
import config from "../config";
import logger from "../utils/logger";

const LOG = logger("currency:service");

export let currencyService: CurrencyExchange;
export let multiplierCache: MultiplierCache;

export async function init(): Promise<void> {
    currencyService = await createCurrencyExchange(getRedisPool() as any, ExchangeRateType.ASK);
    multiplierCache = new MultiplierCache();
}

class CurrencyExchangeServiceImpl implements CurrencyExchangeService {

    constructor(private baseCurrency: string, private targetCurrency: string, private exchangeRate?: number) {
    }

    public exchange(amount: number): number {
        // TODO analyze impact of floating point error here
        const exchangeRate = this.exchangeRate ||
            currencyService.getExchangeRate(this.baseCurrency, this.targetCurrency);
        // avoid rounding during conversion
        return amount * exchangeRate;
    }
}

export function createExchangeService(baseCurrency: string,
                                      targetCurrency: string,
                                      exchangeRate?: number): CurrencyExchangeService {
    return new CurrencyExchangeServiceImpl(baseCurrency, targetCurrency, exchangeRate);
}

interface MultiplierData {
    multiplier: number;
    lastUpdate: Date;
}

class MultiplierCache {

    private cache = new Map<string, MultiplierData>();

    public async getToEURMultiplier(playerCurrency: string): Promise<number> {
        let multiplierData = this.cache.get(playerCurrency);
        if (!multiplierData || this.recordIsOld(multiplierData)) {
            try {
                const limitData: GameLimitsCurrency = await this.load(playerCurrency);
                if (!limitData || !limitData.toEURMultiplier) {
                    LOG.warn("Cannot get to eur multiplier");
                    return;
                }
                multiplierData = { lastUpdate: new Date(), multiplier: limitData.toEURMultiplier };
                this.cache.set(playerCurrency, multiplierData);
            } catch (e) {
                LOG.warn(e, "Cannot get to eur multiplier");
                return;
            }
        }
        return multiplierData.multiplier;
    }

    private load(playerCurrency: string): Promise<GameLimitsCurrency> {
        return getGameLimitsCurrency(playerCurrency, config.toEURMultiplier.version);
    }

    private recordIsOld(multiplierData: MultiplierData) {
        const updateRequiredAt = new Date(multiplierData.lastUpdate);
        updateRequiredAt.setMinutes(updateRequiredAt.getMinutes() + config.toEURMultiplier.cacheInvalidateMin);
        return updateRequiredAt < new Date();
    }
}

export async function getToEURMultiplier(playerCurrency: string): Promise<number> {
    if (playerCurrency === "EUR") {
        return 1;
    }
    return await multiplierCache.getToEURMultiplier(playerCurrency);
}
