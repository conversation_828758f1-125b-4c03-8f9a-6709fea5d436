import { measures, redis, sleep } from "@skywind-group/sw-utils";
import * as path from "path";
import logger from "../utils/logger";
import { RemoteGameFlowProcessResult, RemoteGameFlowRequest } from "./remoteJackpotService";
import config from "../config";
import { RedisClientPool } from "../storage/redis";
import { JackpotLookup } from "../../definition";
import { RemoteJackpotApi, RemoteJackpotApiCache } from "./remoteJackpotApi";
import { RemoteTickerService } from "./remoteTicker.service";

const log = logger("remote:request-queue");

interface ScheduledRemoteRequest {
    ts: number;
    request: RemoteGameFlowRequest;
}

export interface RemoteRequestScheduler {
    schedule(request: RemoteGameFlowRequest): Promise<void>;
}

/**
 * Handles asynchronous requests to process jackpot game flow on remote JPN.
 * Provides retransmission mechanism with exponential backoff algorithm.
 */
export class RemoteRequestQueue implements RemoteRequestScheduler {

    private retransmitRequestProc = new redis.RedisProc(log,
        path.resolve(__dirname, "../../../resources/lua/retransmit_remote_request.lua"));
    private requestQueue = config.remoteRequestQueue.queuePrefix + ":queue";

    constructor(private readonly redisPool: RedisClientPool,
                private lookup: JackpotLookup,
                private tickerService: RemoteTickerService) {
    }

    public async isEmpty(): Promise<boolean> {
        const client = await this.redisPool.get();
        try {
            const exists = await client.exists(this.requestQueue);
            return exists === 0;
        } finally {
            this.redisPool.release(client);
        }
    }

    public async schedule(request: RemoteGameFlowRequest): Promise<void> {
        const scheduledRequest: ScheduledRemoteRequest = {
            ts: Date.now(),
            request
        };
        const requestData = JSON.stringify(scheduledRequest);

        // schedule
        await this.scheduleRequest(requestData, scheduledRequest);

        // execute asynchronously
        this.processScheduledRequest(requestData, scheduledRequest);
    }

    public async retransmit(): Promise<void> {
        log.info("Start failed remote requests retransmission");

        while (true) {
            const data = await this.getScheduledRequest();
            if (data) {
                await this.retransmitScheduledRequest(data);
            } else {
                await sleep(config.remoteRequestQueue.retransmitTimeout);
            }
        }
    }

    private async retransmitScheduledRequest(data: string): Promise<void> {
        try {
            const scheduledRequest: ScheduledRemoteRequest = JSON.parse(data);
            await this.processScheduledRequest(data, scheduledRequest);
        } catch (err) {
            log.error(err, "Failed to process scheduled remote request");
            measures.measureProvider.saveError(err);
        }
    }

    private async processScheduledRequest(data: string, scheduledRequest: ScheduledRemoteRequest): Promise<void> {
        try {
            const remoteApi = await this.getRemoteApi(scheduledRequest);
            const response = await remoteApi.processGameFlow(scheduledRequest.request);
            // remove from queue
            await this.removeScheduledRequest(data);
            // update ticker
            const remoteResult: RemoteGameFlowProcessResult = response.results[0];
            await this.tickerService.updateTicker(remoteResult.ticker);
        } catch (err) {
            log.error(err, "Failed to execute remote request. Reschedule");
            measures.measureProvider.saveError(err);
            await this.rescheduleRequest(data, scheduledRequest);
        }
    }

    private async getRemoteApi(scheduledRequest: ScheduledRemoteRequest): Promise<RemoteJackpotApi> {
        const jackpotId = scheduledRequest.request.results[0].jackpotId;
        const jackpot = await this.lookup.findInternal(jackpotId);
        return RemoteJackpotApiCache.getRemoteJackpotApi(jackpot.region);
    }

    private async scheduleRequest(data: string, scheduledRequest: ScheduledRemoteRequest): Promise<void> {
        const client = await this.redisPool.get();
        try {
            const ts: any = scheduledRequest.ts + config.remoteRequestQueue.retransmitTimeout;
            await client.zadd(this.requestQueue, ts, data);
        } finally {
            this.redisPool.release(client);
        }
    }

    private async getScheduledRequest(): Promise<string> {
        try {
            const client = await this.redisPool.get();
            try {
                const ts = Date.now();
                const nextTs = ts + config.remoteRequestQueue.retransmitTimeout;
                return await this.retransmitRequestProc.exec(
                    client,
                    [this.requestQueue],
                    [ts.toString(), nextTs.toString()]
                );
            } finally {
                this.redisPool.release(client);
            }
        } catch (err) {
            log.error(err, "Failed to get request for retransmission");
            measures.measureProvider.saveError(err);
        }
    }

    private async rescheduleRequest(data: string, scheduledRequest: ScheduledRemoteRequest): Promise<void> {
        const client = await this.redisPool.get();
        try {
            const now = Date.now();
            const timeout = Math.min(Math.max(now - scheduledRequest.ts, config.remoteRequestQueue.retransmitTimeout),
                config.remoteRequestQueue.retransmitMaxTimeout);
            const ts: any = now + timeout;
            await client.zadd(this.requestQueue, "XX", ts, data);
        } finally {
            this.redisPool.release(client);
        }
    }

    private async removeScheduledRequest(data: string): Promise<void> {
        const client = await this.redisPool.get();
        try {
            await client.zrem(this.requestQueue, data);
        } finally {
            this.redisPool.release(client);
        }
    }
}
