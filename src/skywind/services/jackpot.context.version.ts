import { CURRENT_JACKPOT_CONTEXT_VERSION, JackpotContext, JackpotWinType } from "../../definition";
import { JackpotModule } from "../modules/jackpotModule";
import { JackpotGame, JackpotGameWinResult } from "@skywind-group/sw-jpn-core";

/**
 * Convert context to current version.
 */
export async function toCurrentVersion(context: JackpotContext,
                                       jpModule: JackpotModule,
                                       game: JackpotGame): Promise<JackpotContext> {
    if (!context || context.version === CURRENT_JACKPOT_CONTEXT_VERSION) {
        return context;
    }

    if (context.version === undefined) {
        // (SWS-4255) Calculate amounts of seed and progressive to be decremented from pots.
        if (context.win) {
            const wins = Array.isArray(context.win) ? context.win : [context.win];
            const result: JackpotGameWinResult[] = wins.map(win => ({ type: "win", ...win } as any));
            const ticker = await jpModule.getTicker(true);
            const payouts = await game.getWinPayouts(result, ticker.pools);
            for (const win of wins) {
                const payout = payouts.find((p) => p.pool === win.pool && p.amount === win.amount);
                win.seed = payout.seed;
                win.progressive = payout.progressive;
                win.type = JackpotWinType.PLAYER;
            }
            context.result = result;
        } else if (context.miniGame) {
            context.result = {
                type: "start-mini-game",
                gameData: (context as any).gameData
            };
        }
    } else if (context.version === 1) {
        if (context.win) {
            const wins = Array.isArray(context.win) ? context.win : [context.win];
            for (const win of wins) {
                win.type = JackpotWinType.PLAYER;
            }
        }
    }

    context.version = CURRENT_JACKPOT_CONTEXT_VERSION;

    return context;
}
