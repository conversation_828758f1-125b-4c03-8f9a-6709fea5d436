import { Jackpot<PERSON><PERSON>, JackpotGameCreator, CurrencyExchangeService, Jackpot } from "@skywind-group/sw-jpn-core";
import { RandomGenerator } from "@skywind-group/sw-random-cs";
import * as Errors from "../errors";
import * as path from "path";
import * as fs from "fs";
import { JackpotType } from "../api/model";
import { getFilesList, readJsonFile } from "../utils/utils";
import { UnableToLoadFileError, NoJackpotTypeConfigError } from "../errors";

const GAMES_LOCATION: string = require.resolve("@skywind-group/sw-jpn-games") + "/../skywind/games";

const games = new Map<string, JackpotGameCreator>();

// Original configs from sw-jpn-games module
const jpSourceTypeConfigCache: { [key: string]: Promise<JackpotType> } = {};

export function loadGames() {
    const gamesList = fs.readdirSync(path.resolve(__dirname, GAMES_LOCATION));
    for (const gameId of gamesList) {
        const game: any = require(GAMES_LOCATION + "/" + gameId);
        if (game.create !== undefined) {
            games[gameId] = game;
        }
    }
}

/**
 * load - load a specific game logic
 */
export function load(jpGameId: string,
                     jackpot: Jackpot,
                     currencyService: CurrencyExchangeService,
                     rng: RandomGenerator): JackpotGame {
    const game = games[jpGameId];
    if (!game) {
        throw new Errors.JackpotGameNotFound(jpGameId);
    }
    return game.create(jackpot, currencyService, rng);
}

export function exists(jpGameId: string): boolean {
    return games[jpGameId] !== undefined;
}

export async function lookupJackpotTypes(): Promise<JackpotType[]> {
    const gamePath = path.resolve(__dirname, GAMES_LOCATION);
    const gamesList = await getFilesList(gamePath);
    const result: JackpotType[] = [];

    for (const gameId of gamesList) {
        const jackpotType = await lookupJackpotType(gameId);
        result.push(jackpotType);
    }

    return result;
}

export async function lookupJackpotType(gameId: string): Promise<JackpotType> {

    const jpType = jpSourceTypeConfigCache[gameId];

    if (jpType) {
        return jpType;
    } else {

        const jpTypeLoader = loadJackpotTypeFromModules(gameId);
        jpSourceTypeConfigCache[gameId] = jpTypeLoader;
        return jpTypeLoader;
    }
}

export async function loadJackpotTypeFromModules(gameId: string): Promise<JackpotType> {
    const configPath = path.resolve(__dirname, GAMES_LOCATION + "/" + gameId + "/config.json");

    try {
        const configJson = await readJsonFile(configPath);

        const result: JackpotType = {
            name: configJson.name || gameId,
            baseType: configJson.baseType || configJson.name || gameId,
            jpGameId: gameId,
            definition: configJson.definition,
        };

        return result;

    } catch (err) {
        if (err instanceof UnableToLoadFileError) {
            return Promise.reject(new NoJackpotTypeConfigError(gameId));
        } else {
            return Promise.reject(err);
        }
    }
}
