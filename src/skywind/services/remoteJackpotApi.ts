import { JackpotInstance, JackpotRegion } from "../api/model";
import * as request from "request";
import { IncomingMessage } from "http";
import * as Errors from "../errors";
import logger from "../utils/logger";
import { RemoteGameFlowRequest, RemoteGameFlowResponse, TransactionIdBatch } from "./remoteJackpotService";
import { generateInternalToken } from "../utils/security";
import config from "../config";
import { Ticker } from "@skywind-group/sw-jpn-core";
import { keepalive } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import { SecureContextOptions } from "tls";

const log = logger("remote:api");

const agent = keepalive.createAgent(config.remoteApiServer.keepAlive);

/**
 * Cache of remote JPN tokens based on region code
 */
class RegionToken {

    private tokenExpire = config.internalServerToken.expiresIn * 1000 / 2;
    private tokens: Map<string, string> = new Map();

    public async getToken(regionCode: string): Promise<string> {
        if (!this.tokens.has(regionCode)) {
            const token = await generateInternalToken({ regionCode });
            this.tokens.set(regionCode, token);
            setTimeout(() => {
                this.tokens.delete(regionCode);
            }, this.tokenExpire);
        }
        return this.tokens.get(regionCode);
    }
}

/**
 * API wrapper to communicate with remote JPN
 */
interface BaseRequestOptions extends SecureContextOptions {
    baseUrl: string;
}
export class RemoteJackpotApi {

    private static BASE_URL = "/api/v2/jpn";
    private static FIND_OR_CREATE_JP_URL = RemoteJackpotApi.BASE_URL + "/remote/findOrCreateJackpot";
    private static TRANSACTIONS_BATCH_URL = RemoteJackpotApi.BASE_URL + "/remote/transactionId";
    private static GAME_FLOW_URL = RemoteJackpotApi.BASE_URL + "/remote/gameFlow";
    private static TICKER_URL = RemoteJackpotApi.BASE_URL + "/remote/ticker";

    public static tokens: RegionToken = new RegionToken();

    constructor(public region: JackpotRegion) {
    }

    public async findOrCreateJackpot(info: JackpotInstance, autoCreate = false): Promise<JackpotInstance> {
        return this.post(RemoteJackpotApi.FIND_OR_CREATE_JP_URL, { autoCreate }, info);
    }

    public async getTicker(jackpotId: string): Promise<Ticker> {
        return this.get<Ticker>(RemoteJackpotApi.TICKER_URL, { jackpotId });
    }

    public async generateTransactions(count: number): Promise<string[]> {
        const data = await this.get<TransactionIdBatch>(RemoteJackpotApi.TRANSACTIONS_BATCH_URL, { count });
        return data.transactionIds;
    }

    public async processGameFlow(request: RemoteGameFlowRequest): Promise<RemoteGameFlowResponse> {
        return this.post<RemoteGameFlowResponse>(RemoteJackpotApi.GAME_FLOW_URL, {}, request);
    }

    private async get<T>(url: string, qs?: any): Promise<T> {
        return this.request("get", url, qs);
    }

    private async post<T>(url: string, qs?: any, req?: any): Promise<T> {
        return this.request("post", url, qs, req);
    }

    private async request<T>(method: string, url: string, qs?: any, req?: any): Promise<T> {
        return initHttpFactory(this.region).request<T>(method, url, qs, req);
    }
}

export class RemoteJackpotApiCache {

    private static remoteApiCache: Map<string, RemoteJackpotApi> = new Map<string, RemoteJackpotApi>();

    public static getRemoteJackpotApi(region: JackpotRegion): RemoteJackpotApi {
        if (RemoteJackpotApiCache.remoteApiCache.has(region.code)) {
            const remoteApi = RemoteJackpotApiCache.remoteApiCache.get(region.code);
            // verify region has not been updated
            if (remoteApi.region.url === region.url) {
                return remoteApi;
            }
        }

        const remoteApi = new RemoteJackpotApi({
            code: region.code,
            url: region.url,
            secureOptions: region.secureOptions
        });
        RemoteJackpotApiCache.remoteApiCache.set(region.code, remoteApi);

        return remoteApi;
    }

    public static invalidate(region) {
        RemoteJackpotApiCache.remoteApiCache.delete(region.code);
    }
}

function initHttpFactory(region: JackpotRegion) {
    if (config.enableSuperagentLib) {
        return new SuperagentHttpClient(region);
    }
    return new RequestHttpClient(region);
}

class SuperagentHttpClient {
    private readonly baseRequestOptions: BaseRequestOptions;
    constructor(public region: JackpotRegion) {
        this.baseRequestOptions = {
            baseUrl: region.url,
            ...region.secureOptions
        };
    }
    public async request<T>(method: string, url: string, qs?: any, req?: any): Promise<T> {
        const token: string = await RemoteJackpotApi.tokens.getToken(this.region.code);
        log.info("Query remote API: url=%s", url, qs, req);
        const fullUrl = new URL(url, this.baseRequestOptions.baseUrl).toString();
        return new Promise<T>((resolve, reject) => {
            const request = superagent[method](fullUrl)
                .set("X-Access-Token", token)
                .set("Accept", "application/json");

            if (this.baseRequestOptions.ca) {
                request.ca(Buffer.from(this.baseRequestOptions.ca as string));
            }
            if (this.baseRequestOptions.key) {
                request.key(Buffer.from(this.baseRequestOptions.key as string));
            }
            if (this.baseRequestOptions.cert) {
                request.cert(Buffer.from(this.baseRequestOptions.cert as string));
            }
            if (qs) {
                request.query(qs);
            }
            if (agent) {
                request.agent(agent);
            }
            if (req) {
                request.send(req);
            }
            request.end((error, response) => {
                if (error) {
                    // If a raw packet is sent by node, decode it and append it to the logged error
                    // This is done mostly to help us debug node httpParser errors related to request headers
                    if (error.rawPacket) {
                        error.decodedPacket = error.rawPacket.toString();
                    }
                    log.warn("Querying remote API error: url=%s", url, qs, req, error);
                    RemoteJackpotApiCache.invalidate(this.region);
                    return reject(new Errors.RemoteJackpotWalletConnectionError());
                } else if (response.status !== 200) {
                    log.warn("Querying remote API error ", response.body);
                    return reject(new Errors.RemoteJackpotWalletError(response.status, response.body));
                }

                log.info("Remote API response", response.body);
                return resolve(response.body);
            });
        });
    }
}

class RequestHttpClient {
    private readonly baseRequestOptions: BaseRequestOptions;
    constructor(public region: JackpotRegion) {
        this.baseRequestOptions = {
            baseUrl: region.url,
            ...region.secureOptions
        };
    }
    public async request<T>(method: string, url: string, qs?: any, req?: any): Promise<T> {
        const token: string = await RemoteJackpotApi.tokens.getToken(this.region.code);
        log.info("Query remote API: url=%s", url, qs, req);
        return new Promise<T>((resolve, reject) => {
            request[method]({
                ...this.baseRequestOptions,
                url: url,
                qs,
                json: req || true,
                headers: {
                    "X-Access-Token": token
                },
                agent
            }, (error: any, response: IncomingMessage, body: any) => {
                if (error) {
                    // If a raw packet is sent by node, decode it and append it to the logged error
                    // This is done mostly to help us debug node httpParser errors related to request headers
                    if (error.rawPacket) {
                        error.decodedPacket = error.rawPacket.toString();
                    }
                    log.warn("Querying remote API error: url=%s", url, qs, req, error);
                    RemoteJackpotApiCache.invalidate(this.region);
                    return reject(new Errors.RemoteJackpotWalletConnectionError());
                } else if (response.statusCode !== 200) {
                    log.warn("Querying remote API error ", body);
                    return reject(new Errors.RemoteJackpotWalletError(response.statusCode, body));
                } else {
                    log.info("Remote API response", body);
                    return resolve(body);
                }
            });
        });
    }
}
