import { JackpotModule } from "../modules/jackpotModule";
import * as JWT from "jsonwebtoken";
import config from "../config";
import * as GameService from "./game.service";
import { Currencies, CurrencyNotFoundError as BadCurrencyCodeError } from "@skywind-group/sw-currency-exchange";
import * as Errors from "../errors";
import { CurrencyNotFoundError, ValidationError } from "../errors";
import {
    AuthRequest,
    AuthResponse,
    BaseRequest,
    CheckWinRequest,
    CheckWinResponse,
    ContributionEvent,
    ContributionRequest,
    ContributionResponse,
    DeferredContributionRequest,
    DeferredContributionResponse,
    DeferredContributionResult,
    EndJackpotMiniGameEvent,
    ExchangeRates,
    JackpotEvent,
    JackpotGame,
    JackpotOperationStatus,
    JackpotResult,
    JackpotShortInfo,
    JackpotWinConfirmEvent,
    JackpotWinEvent,
    MiniGameRequest,
    MiniGameResponse,
    PlayerInformation,
    PoolDepositRequest,
    PoolState,
    StartJackpotMiniGameEvent,
    TickerInformation,
    TickerRequest,
    TickerResponse,
    TransactionIdResponse,
    UpdatePoolRequest,
    WinConfirmRequest,
    WinConfirmResponse,
    WinJackpotRequest,
    WinJackpotResponse
} from "@skywind-group/sw-jpn-core";
import logger from "../utils/logger";
import { createExchangeService, currencyService } from "./currency.service";
import { measures } from "@skywind-group/sw-utils";
import { createRandomGenerator } from "../utils/random";
import {
    getPoolTitle,
    JackpotApplyPendingPool,
    JackpotContext,
    JackpotContextService,
    JackpotLookup,
    JackpotPlayerWin,
    JackpotStatus,
    JackpotWin,
    JackpotUpdateInfo,
    JackpotWinType,
    WalletService,
    ExternalJackpotLookup
} from "../../definition";
import { JackpotWallet } from "../modules/jackpotWallet";
import { LocalJackpotModule } from "../modules/localJackpotModule";
import { createJackpotGameFlow, JackpotGameFlow, TickerOptions } from "./jackpotGameFlow";
import { createRemoteServices, RemoteJackpotModule, RemoteServices } from "../modules/remoteJackpotModule";
import { BaseWalletParams } from "../modules/walletParams";
import { JackpotDisableMode, JackpotInternalInstance } from "../api/model";
import { createHash } from "../utils/hash";
import { EGPJackpotTickerResponse } from "@skywind-group/sw-gameprovider-adapter-core";
import { JackpotId } from "../utils/jackpotId";

const measure = measures.measure;

const log = logger("jackpot:service");

/**
 * Used as the controller for all jackpot actions
 */
export class JackpotService {
    constructor(private lookup: JackpotLookup & ExternalJackpotLookup & JackpotApplyPendingPool & JackpotUpdateInfo,
                private wallet: WalletService,
                private contextService?: JackpotContextService,
                private remoteServices?: RemoteServices) {
        this.remoteServices = remoteServices || createRemoteServices(lookup);
    }

    /**
     * auth - authorize specific jackpot and player
     */
    @measures.measure({ name: "jackpotService.auth", isAsync: true })
    public async auth(request: AuthRequest): Promise<AuthResponse> {
        try {
            Currencies.verifyExists(request.currency);
        } catch (err) {
            return Promise.reject(new CurrencyNotFoundError(request.currency));
        }

        // Remove duplicates
        if (request.jackpotIds.length > 1) {
            request.jackpotIds = [...new Set(request.jackpotIds)];
        }

        const jackpots = request.jackpotIds.map((jpId) => {
            if (request.isTest && request.autoCreateTestJackpot) {
                return this.lookup.findOrCreateTest(jpId);
            } else {
                return this.lookup.findInternal(jpId);
            }
        });
        const jpInstances = await Promise.all(jackpots);

        const jackpotShortInfoList: JackpotShortInfo[] = [];

        for (const jpInstance of jpInstances) {
            if (request.isTest && !jpInstance.isTest) {
                return Promise.reject(new Errors.TestPlayerUsesRealJackpotError());
            } else if (!request.isTest && jpInstance.isTest) {
                return Promise.reject(new Errors.RealPlayerUsesTestJackpotError());
            }
            let isDisabled = false;
            if (jpInstance.isDisabled) {
                if (jpInstance.disableMode === JackpotDisableMode.IMMEDIATE) {
                    isDisabled = true;
                } else {
                    const jpModule: JackpotModule = await this.createJackpotModule({} as any, jpInstance.id, undefined);
                    const ticker = await jpModule.getTicker();
                    if (ticker.isDisabled) {
                        isDisabled = true;
                    }
                }
            }
            if (isDisabled && !request.includeDisabled) {
                continue;
            }
            const jpId = jpInstance.id;
            const jpCurrency = jpInstance.definition.currency;
            const jpGameId = jpInstance.jpGameId;
            const jpShortInfo: JackpotShortInfo = {
                id: jpId,
                currency: jpCurrency,
                type: jpInstance.type,
                baseType: jpInstance.baseType,
                jpGameId: jpGameId,
                isTest: !!jpInstance.isTest,
                isDisabled,
                isLocal: jpInstance.isLocal,
                createdFromId: jpInstance.createdFromId
            };
            jackpotShortInfoList.push(jpShortInfo);
        }

        // TODO - probably need to actually do some checks here!!!
        const payload = {
            playerCode: request.playerCode,
            brandId: request.brandId,
            jackpotIds: jackpotShortInfoList.map((jpInstance) => jpInstance.id),
            currency: request.currency,
            gameCode: request.gameCode,
            region: request.region
        };

        if (request.nickname) {
            payload["nickname"] = request.nickname;
        }

        // create JWT signed token that will be used in all other API calls
        const token = JWT.sign(payload, config.token.secret);
        return {
            token: token,
            jackpots: jackpotShortInfoList,
        };
    }

    /**
     * authenticate - authenticate the token and return the relevant payload
     */
    @measure({ name: "jackpotService.authenticate", isAsync: false, debugOnly: true })
    public authenticate(token: string): AuthRequest {
        try {
            return JWT.verify(token, config.token.secret) as any;
        } catch (err) {
            if (err instanceof JWT.JsonWebTokenError) {
                throw new Errors.TokenNotValid();
            } else if (err instanceof JWT.TokenExpiredError) {
                throw new Errors.TokenExpired();
            } else {
                throw err;
            }
        }
    }

    /**
     * Authenticate transfer operations with pools ie. transfer/progressive, transfer/transactionId etc.
     * @param {string} token
     * @returns {AuthRequest}
     */
    @measure({ name: "jackpotService.authenticateTransfer", isAsync: false, debugOnly: true })
    public authenticateTransfer(token: string): AuthRequest {
        try {
            return JWT.verify(token, config.accessToken.secret) as any;
        } catch (err) {
            if (err instanceof JWT.JsonWebTokenError) {
                throw new Errors.TokenNotValid();
            } else if (err instanceof JWT.TokenExpiredError) {
                throw new Errors.TokenExpired();
            } else {
                throw err;
            }
        }
    }

    /**
     * getTicker - get all ticker information for a specific jackpot
     */
    @measure({ name: "jackpotService.getTicker", isAsync: true, debugOnly: true })
    public async getTicker(auth: AuthRequest, request: TickerRequest,
                           opts: TickerOptions = { withPots: true }): Promise<TickerResponse> {
        const tickers = [];
        for (const jackpotId of JackpotId.filterInternal(auth.jackpotIds)) {
            try {
                const ticker = await this.getJackpotTicker(auth, jackpotId, request.exchangeRates, opts);
                tickers.push(ticker);
            } catch (err) {
                if (opts.skipError) {
                    log.error(err, `Skipped error for jp instance - ${jackpotId}`);
                } else {
                    return Promise.reject(err);
                }
            }
        }

        for (const [gameProviderCode, ids] of JackpotId.filterAndGroupExternal(auth.jackpotIds)) {
            try {
                const gameProviderTickerResponse: EGPJackpotTickerResponse = await
                    this.lookup.getExternalTickers(gameProviderCode, ids as string[], auth.currency);
                for (const gameProviderTicker of gameProviderTickerResponse) {
                    const ticker: TickerInformation = {
                        jackpotId: gameProviderTicker.jackpotId,
                        jackpotType: gameProviderTicker.jackpotType,
                        jackpotBaseType: gameProviderTicker.jackpotType,
                        pools: gameProviderTicker.pools
                    };
                    tickers.push(ticker);
                }
            } catch (err) {
                log.error(err, `Skipped error when querying ticker for game provider - ${gameProviderCode}`);
            }
        }

        return tickers;
    }

    @measure({ name: "jackpotService.getJackpotTicker", isAsync: true, debugOnly: true })
    public async getJackpotTicker(auth: AuthRequest,
                                  jackpotId: string,
                                  exchangeRate?: ExchangeRates,
                                  opts: TickerOptions = { withPots: true }): Promise<TickerInformation> {
        const jpModule: JackpotModule = await this.createJackpotModule(auth, jackpotId, exchangeRate);
        const flow: JackpotGameFlow = await this.loadGameFlow(jpModule, {} as any, true);

        try {
            return await flow.getPlayerTicker(opts);
        } catch (err) {
            if (err instanceof BadCurrencyCodeError) {
                return Promise.reject(new CurrencyNotFoundError(err.currency));
            } else {
                return Promise.reject(err);
            }
        }
    }

    /**
     * contribute - contribute to specific jackpot
     */
    @measure({ name: "jackpotService.contribute", isAsync: true, debugOnly: true })
    public async contribute(auth: AuthRequest,
                            request: ContributionRequest): Promise<ContributionResponse> {
        const events: JackpotEvent[] = [];
        const jackpotIdsToContribute: string[] = request.jackpotIds || auth.jackpotIds;

        await Promise.all(
            jackpotIdsToContribute.map((jackpotId) => this.doContribute(jackpotId, auth, request)
                .then((jpEvents) => events.push(...jpEvents)))
        );

        return {
            events: events,
        };
    }

    @measure({ name: "jackpotService.deferredContribute", isAsync: true, debugOnly: true })
    public async deferredContribute(auth: AuthRequest,
                                    request: DeferredContributionRequest): Promise<DeferredContributionResponse> {
        const jackpotIdsToGetContributes: string[] = request.jackpotIds || auth.jackpotIds;
        const results: DeferredContributionResult[] = [];

        const transactionId = (await this.generateContributionTrxId()).transactionId;

        if (request.deferredWinsEnabled) {
            request.transactionId = transactionId;
        }

        await Promise.all(
            jackpotIdsToGetContributes.map((jackpotId) => this.doDeferredContribute(jackpotId, auth, request)
                .then((result) => results.push(result)))
        );

        return {
            transactionId: transactionId,
            result: this.encodeDeferredContributionData(results),
        };
    }

    @measure({ name: "jackpotService.continueDeferredContribute", isAsync: true, debugOnly: true })
    public async continueDeferredContribute(auth: AuthRequest,
                                            request: DeferredContributionRequest): Promise<ContributionResponse> {
        const results = this.decodeDeferredContributionData(request.deferredContribution);

        const events: JackpotEvent[] = [];

        await Promise.all(
            results.map((result) => this.doContinueDeferredContribute(auth, request, result)
                .then((jpEvents) => events.push(...jpEvents)))
        );

        return {
            events: events,
        };
    }

    @measure({ name: "jackpotService.doContinueDeferredContribute", isAsync: true, debugOnly: true })
    private async doContinueDeferredContribute(auth: AuthRequest, request: DeferredContributionRequest,
                                               result: DeferredContributionResult): Promise<JackpotEvent[]> {
        const jackpotId = result.jackpotId;
        const jpModule: JackpotModule = await this.createJackpotModule(auth, jackpotId,
            request.exchangeRates, request.roundId);
        const flow: JackpotGameFlow = await this.loadGameFlow(jpModule, request);

        await flow.continueDeferredContribute(result);
        return this.finalizeContribute(flow, jpModule, jackpotId);
    }

    @measure({ name: "jackpotService.doDeferredContribute", isAsync: true, debugOnly: true })
    private async doDeferredContribute(jackpotId: string,
                                       auth: AuthRequest,
                                       request: DeferredContributionRequest): Promise<DeferredContributionResult> {
        const jpModule: JackpotModule = await this.createJackpotModule(auth, jackpotId,
            request.exchangeRates, request.roundId);

        // load relevant game logic
        const flow: JackpotGameFlow = await this.loadGameFlow(jpModule, request, true);

        await flow.contribute();

        const gameResult = flow.getGameResult();

        const result: DeferredContributionResult = {
            jackpotId: jackpotId,
            totalPlayerContribution: flow.getPlayerTotalContribution(),
            contributions: flow.getContributions(),
            playerContributions: flow.getPlayerContributions(),
            gameResult
        };

        if (request.deferredWinsEnabled && this.checkIfGameResultIsWin(gameResult)) {
            result.events = await this.doContinueDeferredContribute(auth, request, result);
        }

        return result;
    }

    private checkIfGameResultIsWin(gameResult: JackpotResult) {
        if (!gameResult) {
            return false;
        }
        if (Array.isArray(gameResult)) {
            if (!gameResult[0]) {
                return false;
            }
            return gameResult[0].type === "win";
        } else {
            return gameResult.type === "win";
        }
    }

    @measure({ name: "jackpotService.doContribute", isAsync: true, debugOnly: true })
    private async doContribute(jackpotId: string,
                               auth: AuthRequest,
                               request: ContributionRequest): Promise<JackpotEvent[]> {
        const jpModule: JackpotModule = await this.createJackpotModule(auth, jackpotId,
            request.exchangeRates, request.roundId);

        // load relevant game logic
        const flow: JackpotGameFlow = await this.loadGameFlow(jpModule, request);

        await flow.contribute();
        return this.finalizeContribute(flow, jpModule, jackpotId);
    }

    private async finalizeContribute(flow: JackpotGameFlow,
                                     jpModule: JackpotModule,
                                     jackpotId: string): Promise<JackpotEvent[]> {
        await flow.finalize();

        const events: JackpotEvent[] = await this.handleGameResult(jpModule, flow);

        if (flow.hasContributions()) {

            const ticker: TickerInformation = await flow.getPlayerTicker({ withPots: true });
            const totalContributionInPlayerCurrency = flow.getPlayerTotalContribution();

            const contributionEvent: ContributionEvent = {
                type: "contribution",
                jackpotId,
                jackpotType: ticker.jackpotType,
                jackpotBaseType: ticker.jackpotBaseType,
                pools: ticker.pools,
                totalContribution: totalContributionInPlayerCurrency,
                contributions: flow.getPlayerContributions()
            };

            if (flow.request.deferredWinsEnabled) {
                contributionEvent.status = JackpotOperationStatus.PENDING;
            }

            events.push(contributionEvent);
        }

        return events;
    }

    /**
     * checkWin - check probability of jackpot win
     */
    @measure({ name: "jackpotService.checkWin", isAsync: true, debugOnly: true })
    public async checkWin(auth: AuthRequest,
                          request: CheckWinRequest): Promise<CheckWinResponse> {
        const events: JackpotEvent[] = [];
        const jackpotIds: string[] = request.jackpotIds || auth.jackpotIds;

        await Promise.all(
            jackpotIds.map((jackpotId) => this.doCheckWin(jackpotId, auth, request)
                .then((jpEvents) => events.push(...jpEvents)))
        );

        return {
            events: events,
        };
    }

    @measure({ name: "jackpotService.doCheckWin", isAsync: true, debugOnly: true })
    private async doCheckWin(jackpotId: string,
                             auth: AuthRequest,
                             request: CheckWinRequest): Promise<JackpotEvent[]> {
        const jpModule: JackpotModule = await this.createJackpotModule(auth, jackpotId,
            request.exchangeRates, request.roundId);

        const flow: JackpotGameFlow = await this.loadGameFlow(jpModule, request);

        await flow.checkWin();
        await flow.finalize();

        return this.handleGameResult(jpModule, flow);
    }

    /**
     * Generate transaction id for contribution
     */
    @measure({ name: "jackpotService.generateContributionTrxId", isAsync: true, debugOnly: true })
    public async generateContributionTrxId(): Promise<TransactionIdResponse> {
        return {
            transactionId: await this.wallet.generateTransactionId(),
        };
    }

    /**
     * Confirm that jackpot win was paid to the player and cleanup jackpot context.
     */
    @measure({ name: "jackpotService.confirmWin", isAsync: true })
    public async confirmWin(auth: AuthRequest,
                            request: WinConfirmRequest): Promise<WinConfirmResponse> {
        const events: JackpotEvent[] = [];
        const jackpotIds = Array.isArray(request.jackpotId) ? request.jackpotId : [request.jackpotId];

        for (const jackpotId of jackpotIds) {
            const context: JackpotContext = await this.contextService.find(jackpotId, auth, request.transactionId);
            if (context && context.status !== JackpotStatus.PAID) {
                return Promise.reject(new Errors.BadContextState());
            }

            log.info("Confirm jackpot win", auth, request);

            if (request.deferredWinsEnabled) {
                const jpModule = await this.createJackpotModule(auth, jackpotId);
                const flow = await this.loadGameFlow(jpModule, request);
                await jpModule.resolveWin(flow, JackpotOperationStatus.RESOLVED);
            }

            const instance = await this.lookup.findInternal(jackpotId);

            const confirmationEvent: JackpotWinConfirmEvent = {
                type: "win-confirm",
                jackpotId: jackpotId,
                jackpotType: instance.type,
                jackpotBaseType: instance.baseType,
                transactionId: request.transactionId,
            };
            events.push(confirmationEvent);

            await this.applyPendingPool(instance, context);
            await this.updateInfo(instance);

            if (context) {
                await this.contextService.remove(context);
            }
        }

        return {
            events: events,
        };
    }

    private async applyPendingPool(instance: JackpotInternalInstance, context: JackpotContext) {
        const win = context && context.win;
        const poolId = Array.isArray(win) ? win.length && win[0].pool : win && win.pool;
        if (poolId) {
            await this.lookup.applyPendingPool(instance.id, poolId);
        }
    }

    private async updateInfo(instance: JackpotInternalInstance) {
        const externalStartDate = new Date();
        const hash = createHash(instance.id + externalStartDate.getTime());
        const externalId = `${instance.id}_${hash}`;
        await this.lookup.updateInfo(instance.id, { externalId, externalStartDate: externalStartDate.toISOString() });
    }

    /**
     * Rollback jackpot win for the player and cleanup jackpot context.
     */
    @measure({ name: "jackpotService.rollbackWin", isAsync: true })
    public async rollbackWin(auth: AuthRequest,
                             request: WinConfirmRequest): Promise<any> {
        const events: JackpotEvent[] = [];
        const jackpotIds = Array.isArray(request.jackpotId) ? request.jackpotId : [request.jackpotId];

        for (const jackpotId of jackpotIds) {
            const context: JackpotContext = await this.contextService.find(jackpotId, auth, request.transactionId);

            if (context && context.status !== JackpotStatus.PAID) {
                return Promise.reject(new Errors.BadContextState());
            }

            log.info("Rollback jackpot win", auth, request);

            const jpModule: JackpotModule = await this.createJackpotModule(auth, jackpotId);
            const flow: JackpotGameFlow = await this.loadGameFlow(jpModule, request);
            await jpModule.resolveWin(flow, JackpotOperationStatus.REJECTED);

            const instance = await this.lookup.findInternal(jackpotId);

            const rollbackEvent = {
                type: "win-rollback",
                jackpotId: jackpotId,
                jackpotType: instance.type,
                jackpotBaseType: instance.baseType,
                transactionId: request.transactionId,
            };
            events.push(rollbackEvent);

            if (context) {
                await this.contextService.remove(context);
            }
        }
        return { events };
    }

    /**
     * Play mini game and get jackpot win result.
     */
    @measure({ name: "jackpotService.updateMiniGame", isAsync: true })
    public async updateMiniGame(auth: AuthRequest,
                                request: MiniGameRequest): Promise<MiniGameResponse> {
        const jpModule: JackpotModule = await this.createJackpotModule(auth, request.jackpotId,
            request.exchangeRates, request.roundId);

        log.info("Update mini game", auth, request);

        const flow = await this.loadGameFlow(jpModule, request);

        if (!flow.hasWonMiniGame() && !flow.hasWonJackpot()) {
            return Promise.reject(new Errors.MiniGameNotFound());
        }

        await flow.winMiniGame();
        await flow.finalize();

        const events: JackpotEvent[] = await this.handleGameResult(jpModule, flow);

        const endMiniGame: EndJackpotMiniGameEvent = {
            type: "end-mini-game",
            jackpotId: jpModule.instance.id,
            jackpotType: jpModule.instance.type,
            jackpotBaseType: jpModule.instance.baseType,
            transactionId: request.transactionId,
        };
        events.push(endMiniGame);

        return { events };
    }

    /**
     *  Win from jackpot
     */
    @measure({ name: "jackpotService.winJackpot", isAsync: true })
    public async winJackpot(auth: AuthRequest, request: WinJackpotRequest): Promise<WinJackpotResponse> {

        log.info("win jackpot", auth, request);

        const jpModule: JackpotModule = await this.createJackpotModule(auth, request.jackpotId,
            request.exchangeRates, request.roundId);

        const flow: JackpotGameFlow = await this.loadGameFlow(jpModule, request);

        await flow.winJackpot();
        await flow.finalize();

        const events = await this.handleGameResult(jpModule, flow);

        return { events };
    }

    /**
     * Generate transaction for pool transfer operations
     * @returns {Promise<TransactionIdResponse>}
     */
    public async getTransferTransactionId(): Promise<TransactionIdResponse> {
        return {
            transactionId: await this.wallet.generateTransactionId(),
        };
    }

    /**
     * Transfer fixed amount of progressive money from one pool to another within one jackpot.
     *
     * @param {AuthRequest} auth
     * @param {TransferPoolRequest} request
     *
     * @throws ValidationError, JackpotNotInitialized, DuplicateTransactionError, InsufficientJackpotBalance
     * @returns {Promise<void>}
     */
    public async transferProgressive(auth: AuthRequest, request: TransferPoolRequest) {
        const jackpotId = this.validateJackpotIdForTransferOperations(auth);
        const jpModule: JackpotModule = await this.createJackpotModule(auth, jackpotId);
        await jpModule.transferProgressive(request.fromPoolId, request.toPoolId, request.transactionId, request.amount);
    }

    public async getPoolState(auth: AuthRequest, poolId: string): Promise<PoolState> {
        const jackpotId = this.validateJackpotIdForTransferOperations(auth);
        const jpModule: JackpotModule = await this.createJackpotModule(auth, jackpotId);
        return await jpModule.getPoolState(poolId);
    }

    public async getAllPoolsState(auth: AuthRequest): Promise<PoolState[]> {
        const jackpotId = this.validateJackpotIdForTransferOperations(auth);
        const jpModule: JackpotModule = await this.createJackpotModule(auth, jackpotId);
        return await jpModule.getAllPoolsState();
    }

    public async poolDeposit(auth: AuthRequest, poolId, request: PoolDepositRequest) {
        const jackpotId = this.validateJackpotIdForTransferOperations(auth);
        const jpModule: JackpotModule = await this.createJackpotModule(auth, jackpotId);
        await jpModule.poolDeposit(poolId, request);
    }

    public async updatePool(auth: AuthRequest, poolId, request: UpdatePoolRequest) {
        const jackpotId = this.validateJackpotIdForTransferOperations(auth);
        const jpModule: JackpotModule = await this.createJackpotModule(auth, jackpotId);
        if (!config.isProduction() && jpModule.instance.isTest) {
            const transactionId = await this.wallet.generateTransactionId();
            await jpModule.updatePool(poolId, transactionId, request);
        } else {
            throw new ValidationError("Production environment OR not test jackpot instance");
        }
    }

    /**
     * Returns jackpot Id. or throw ValidationError
     *
     * @param {AuthRequest} auth
     * @returns {string}
     */
    private validateJackpotIdForTransferOperations(auth: AuthRequest): string {
        if (!auth.jackpotIds || !Array.isArray(auth.jackpotIds) || auth.jackpotIds.length === 0) {
            throw new ValidationError("Invalid authorised jackpot Id");
        }

        if (auth.jackpotIds.length !== 1) {
            throw new ValidationError("Pool transfer doesn't support multiple jackpots");
        }

        return auth.jackpotIds[0];
    }

    private async handleGameResult(jpModule: JackpotModule, gameFlow: JackpotGameFlow): Promise<JackpotEvent[]> {
        const events: JackpotEvent[] = [];

        if (gameFlow.hasWonMiniGame()) {
            const event: StartJackpotMiniGameEvent = {
                type: gameFlow.getMiniGame().type,
                jackpotId: jpModule.instance.id,
                jackpotType: jpModule.instance.type,
                jackpotBaseType: jpModule.instance.baseType,
                transactionId: gameFlow.request.transactionId,
                gameData: gameFlow.getMiniGame().gameData,
            };

            events.push(event);
        }

        if (gameFlow.hasWonJackpot()) {
            const wins = gameFlow.getWins();
            for (const win of wins) {
                if (!win.type || win.type === JackpotWinType.PLAYER) {
                    const playerWin = win as JackpotPlayerWin;

                    const poolTitle = getPoolTitle(jpModule.instance, playerWin.pool);

                    const winEvent: JackpotWinEvent = {
                        type: "win",
                        jackpotId: jpModule.instance.id,
                        jackpotType: jpModule.instance.type,
                        jackpotBaseType: jpModule.instance.baseType,
                        transactionId: gameFlow.request.transactionId,
                        amount: playerWin.playerAmount,
                        pool: playerWin.pool,
                        title: poolTitle,
                    } as JackpotWinEvent;

                    if (gameFlow.request.deferredWinsEnabled) {
                        winEvent.status = JackpotOperationStatus.PENDING;
                    }

                    if (playerWin.seed !== undefined) {
                        winEvent.seed = playerWin.seed / playerWin.exchangeRate;
                    }
                    if (playerWin.progressive !== undefined) {
                        winEvent.progressive = playerWin.progressive / playerWin.exchangeRate;
                    }
                    if (playerWin.endedPool) {
                        winEvent.endedPool = playerWin.endedPool;
                    }

                    events.push(winEvent);
                }
            }
        }

        return events;
    }

    public async loadGameFlow(jpModule: JackpotModule, request: BaseRequest,
                              ignoreContext: boolean = false): Promise<JackpotGameFlow> {
        const exchangeService = createExchangeService(jpModule.playerInfo.currency,
            jpModule.instance.definition.currency, jpModule.exchangeRate);

        // load relevant game logic
        const game: JackpotGame = GameService.load(jpModule.instance.jpGameId, undefined,
            exchangeService, createRandomGenerator(request));

        return createJackpotGameFlow(request, jpModule, game, this.contextService, ignoreContext);
    }

    public async createJackpotModule(playerInfo: PlayerInformation,
                                     jackpotId: string,
                                     rates?: ExchangeRates,
                                     roundId?: string): Promise<JackpotModule> {
        const instance = await this.lookup.findInternal(jackpotId);

        let exchangeRate: number;
        if (playerInfo.currency) {
            exchangeRate = this.getExchangeRate(playerInfo.currency, instance.definition.currency, rates);
        }

        if (instance.region) {
            return new RemoteJackpotModule(playerInfo, instance, exchangeRate, this.wallet, this.remoteServices);
        } else {
            const params: BaseWalletParams = {
                brandId: playerInfo.brandId,
                region: playerInfo.region,
                gameId: instance.jpGameId,
                gameCode: playerInfo.gameCode,
                playerCode: playerInfo.playerCode,
                playerCurrency: playerInfo.currency,
                roundId
            };

            if (playerInfo.nickname) {
                params.nickname = playerInfo.nickname;
            }

            const jackpotWallet = new JackpotWallet(params, instance, exchangeRate, this.wallet);

            return new LocalJackpotModule(jackpotWallet);
        }
    }

    private getExchangeRate(playerCurrency: string, jackpotCurrency: string, rates?: ExchangeRates): number {
        let exchangeRate: number;
        if (rates) {
            exchangeRate = rates[jackpotCurrency];
        }
        if (!exchangeRate) {
            exchangeRate = currencyService.getExchangeRate(playerCurrency, jackpotCurrency);
        }
        return exchangeRate;
    }

    private encodeDeferredContributionData(results: DeferredContributionResult[]): string {
        return JWT.sign({ payload: results }, config.token.secret);
    }

    private decodeDeferredContributionData(data: string): DeferredContributionResult[] {
        try {
            const payload: any = JWT.verify(data, config.token.secret);
            return payload.payload as DeferredContributionResult[];
        } catch (err) {
            if (err instanceof JWT.JsonWebTokenError) {
                throw new Errors.DeferredContributeInvalidDataError();
            } else if (err instanceof JWT.TokenExpiredError) {
                throw new Errors.TokenExpired();
            } else {
                throw err;
            }
        }
    }
}

// TODO move to jpn-core?
export interface TransferPoolRequest {
    transactionId: string;
    fromPoolId: string;
    toPoolId: string;
    amount?: number;
}
