import { Sequelize, Transaction } from "sequelize";
import { Options, PoolOptions } from "sequelize";
import config from "../config";
import * as pg from "pg";

pg.types.setTypeParser(1114, stringValue => {
    return new Date(stringValue + "+0000");
});

function constructOptions(dbConfig): Options {
    const dbPool: PoolOptions = {

        /**
         * Maximum connections of the pool
         */
        max: dbConfig.maxConnections,

        /**
         * The maximum time, in milliseconds, that a connection can be idle before being released.
         */
        idle: dbConfig.maxIdleTime,

    };
    const dbOptions: Options = {

        /**
         * The dialect of the database you are connecting to. One of mysql, postgres, sqlite, mariadb and mssql.
         */
        dialect: "postgres",

        /**
         * Default isolation level
         */
        isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,

        /**
         * The dialect specific options
         */
        dialectOptions: {},

        /**
         * The host of the relational database.
         */
        host: dbConfig.host,

        /**
         * The port of the relational database.
         */
        port: dbConfig.port,

        /**
         * Connection pool options
         */
        pool: dbPool,

        /**
         * A function that gets executed everytime Sequelize would log something.
         */
        logging: config.queryLogging,

        define: {
            schema: dbConfig.schema,
        }

    };

    if (config.db.ssl.isEnabled) {
        dbOptions.dialectOptions["ssl"] = config.db.ssl;
    }

    return dbOptions;
}

const sequelize = new Sequelize(config.db.database, config.db.user, config.db.password, constructOptions(config.db));

export const sequelizeWalletArchive =
    config.walletArchiveDb.host && new Sequelize(config.walletArchiveDb.database,
    config.walletArchiveDb.user,
    config.walletArchiveDb.password,
    constructOptions(config.walletArchiveDb));

export default sequelize;
