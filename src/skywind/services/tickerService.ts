import { JackpotModule } from "../modules/jackpotModule";
import { TickerInformation, TickerResponse } from "@skywind-group/sw-jpn-core";
import logger from "../utils/logger";
import { measures } from "@skywind-group/sw-utils";
import { ExternalJackpotLookup, JackpotLookup } from "../../definition";
import { JackpotGameFlow, TickerOptions } from "./jackpotGameFlow";
import { JackpotService } from "./jackpot.service";
import { JackpotId } from "../utils/jackpotId";
import { EGPJackpotTickerResponse } from "@skywind-group/sw-gameprovider-adapter-core";

const measure = measures.measure;

const log = logger("jackpot:ticker:service");

/**
 * Used as the controller for all jackpot actions
 */
export class TickerService {

    constructor(private lookup: JackpotLookup & ExternalJackpotLookup,
                private jackpotService: JackpotService) {
    }

    /**
     * getTicker - get all ticker information for a specific jackpot
     */
    @measure({ name: "TickerService.getTicker", isAsync: true, debugOnly: true })
    public async getTicker(jackpotIds: string[],
                           currency?: string,
                           opts: TickerOptions = { withPots: true }): Promise<TickerResponse> {
        const tickers = [];
        const internalJackpotIds = JackpotId.filterInternal(jackpotIds);
        const externalJackpotMap = JackpotId.filterAndGroupExternal(jackpotIds);

        for (const jackpotId of internalJackpotIds) {
            try {
                const ticker = await this.getJackpotTicker(jackpotId, currency, opts);
                tickers.push(ticker);
            } catch (err) {
                if (opts.skipError) {
                    log.error(err, `Skipped error for jp instance - ${jackpotId}`);
                } else {
                    return Promise.reject(err);
                }
            }
        }

        for (const [gameProviderCode, ids] of externalJackpotMap) {
            try {
                const gameProviderTickerResponse: EGPJackpotTickerResponse = await this.lookup.getExternalTickers(
                    gameProviderCode,
                    ids,
                    currency
                );
                for (const gameProviderTicker of gameProviderTickerResponse) {
                    const ticker: TickerInformation = {
                        jackpotId: gameProviderTicker.jackpotId,
                        jackpotType: gameProviderTicker.jackpotType,
                        jackpotBaseType: gameProviderTicker.jackpotType,
                        pools: gameProviderTicker.pools
                    };
                    tickers.push(ticker);
                }
            } catch (err) {
                log.error(err, `Skipped error when querying ticker for game provider - ${gameProviderCode}`);
            }
        }

        return tickers;
    }

    @measure({ name: "TickerService.getJackpotTicker", isAsync: true, debugOnly: true })
    public async getJackpotTicker(jackpotId: string,
                                  currency?: string,
                                  opts: TickerOptions = { withPots: true }): Promise<TickerInformation> {
        if (!currency) {
            const jackpot = await this.lookup.findInternal(jackpotId);
            currency = jackpot.definition.currency;
        }
        const jpModule: JackpotModule = await this.jackpotService.createJackpotModule({ currency } as any, jackpotId);
        const flow: JackpotGameFlow = await this.jackpotService.loadGameFlow(jpModule, {} as any, true);

        const ticker = await flow.getTickerForLobby(opts) as TickerInformation&{currency: string};
        ticker.currency = currency;

        return ticker;
    }
}
