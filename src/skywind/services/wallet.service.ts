import {
    IRedisConnection,
    IWallet,
    connect,
    IWalletManager,
    ITransaction,
    IOperation,
    IWalletConsumerConfiguration,
    ITrxData,
    IExternalTransaction,
    IChangeInfo,
    ITrxId,
    TRANSACTION_IS_PROCESSING,
} from "@skywind-group/sw-wallet";
import db, { sequelizeWalletArchive as archiveDB } from "./db.service";
import config from "../config";
import { WalletService } from "../../definition";
import { walletRedisPool } from "../storage/redis";
import { TransactionIsProcessing } from "../errors";

// create instance of the redisConnection
let redisConnection: IRedisConnection;

function getRedisConnection(): IRedisConnection {
    if (!redisConnection) {
        redisConnection = walletRedisPool.get();
    }
    return redisConnection;
}

const walletConfig: IWalletConsumerConfiguration = {
    type: "direct",

    trxStoragePrefix: config.trxStoragePrefix,

    recentTrxStorage: config.recentTrxStorage,

    unloader: config.unloader,

    trxIdRange: config.trxIdRange,

    db: () => {
        return db;
    },

    archiveDB: () => {
        return archiveDB;
    },

    connection: () => {
        return getRedisConnection();
    },
};

export function getWalletConfig(): IWalletConsumerConfiguration {
    return walletConfig;
}

let walletManager: IWalletManager;

/**
 * getManager - singleton which return the current wallet management access
 * @returns {any}
 */
async function getManager(): Promise<IWalletManager> {
    if (walletManager) {
        return walletManager;
    }
    walletManager = await connect(walletConfig);
    return walletManager;
}

export class WalletServiceImpl implements WalletService {

    public async get(key: string): Promise<IWallet> {
        const manager = await getManager();
        return manager.get(key);
    }

    public async startTransaction(id: string, operation?: IOperation): Promise<ITransaction> {
        const manager: IWalletManager = await getManager();
        const transactionId = id === undefined ? await manager.generateTransactionId() : id;
        return manager.startTransaction(transactionId, operation);
    }

    public async generateTransactionId(): Promise<string> {
        const manager: IWalletManager = await getManager();
        const trxId = await manager.generateTransactionId();
        return trxId.publicId;
    }

    public async parseTransactionId(trxId: string): Promise<ITrxId> {
        const manager: IWalletManager = await getManager();
        return manager.parseTransactionId(trxId);
    }

    public async findCommittedTransaction(trxId: string, operationId?: number): Promise<ITrxData> {
        const manager: IWalletManager = await getManager();
        try {
            return await manager.findCommittedTransaction(trxId, operationId);
        } catch (err) {
            if (err === TRANSACTION_IS_PROCESSING) {
                return Promise.reject(new TransactionIsProcessing());
            }
            return Promise.reject(err);
        }
    }

    public async saveExternalTransaction(transaction: IExternalTransaction): Promise<IChangeInfo[]> {
        const manager: IWalletManager = await getManager();
        return manager.saveExternalTransaction(transaction);
    }
}

export default new WalletServiceImpl();
