import * as Sequelize from "sequelize";
import { Op, FindOptions } from "sequelize";
import { measures } from "@skywind-group/sw-utils";
import { GameAction, Jackpot, JackpotDefinition, JackpotPoolDefinition, Ticker } from "@skywind-group/sw-jpn-core";
import {
    EGPTickerService,
    EGPJackpotInfoService,
    EGPJackpotTickerResponse,
    EGPJackpotInfoResponse
} from "@skywind-group/sw-gameprovider-adapter-core";
import {
    AuditInfo,
    JackpotAuditType,
    JackpotConfigurationLevel,
    JackpotDisableMode,
    JackpotInstance,
    JackpotInstanceInfo,
    JackpotInternalInstance,
    JackpotRegion
} from "../api/model";
import * as Errors from "../errors";
import { JackpotTypeDBInstance } from "../models/jackpotType";
import { JackpotTypeService } from "./jackpot.type";
import { JackpotWallet, JP_FEATURE_OVERRIDE_POOLS_BY_INSTANCE } from "../modules/jackpotWallet";
import logger from "../utils/logger";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { currencyService } from "./currency.service";
import wallet from "./wallet.service";
import { ExternalJackpotLookup, JackpotApplyPendingPool, JackpotLookup, JackpotUpdateInfo } from "../../definition";
import JackpotRegionService from "./regionService";
import { remoteTickerPool } from "../storage/redis";
import { RemoteTickerService } from "./remoteTicker.service";
import { get as getCache, init as initCache } from "./jackpotCache";
import * as GameService from "./game.service";
import JackpotAuditService, { auditJackpot } from "./jackpotAudits";
import {
    DBJackpotInstance,
    IJackpotInstanceModel,
    JackpotInstanceDBInstance,
    JackpotInstanceModel
} from "../models/jackpotInstance";
import config from "../config";
import { RemoteJackpotApiCache } from "./remoteJackpotApi";
import { JackpotId } from "../utils/jackpotId";

const measure = measures.measure;

const log = logger("jackpot:instance");

export function isValidJackpotId(id: string): boolean {
    return typeof id === "string" && id.length && !id.includes(":");
}

export class JackpotInstanceService implements JackpotLookup, ExternalJackpotLookup, JackpotApplyPendingPool,
    JackpotUpdateInfo {

    public static model: IJackpotInstanceModel = JackpotInstanceModel();
    private static tickerService: RemoteTickerService = new RemoteTickerService(remoteTickerPool.get(),
        new JackpotInstanceService());
    private static externalTickerServices = new Map<string, EGPTickerService>(
        Object.entries(config.gameProviderAdapterUri).map(uriKeyValuePair =>
            [uriKeyValuePair[0], new EGPTickerService(uriKeyValuePair[1])]));
    private static externalJackpotInfoServices = new Map<string, EGPJackpotInfoService>(
        Object.entries(config.gameProviderAdapterUri).map(uriKeyValuePair =>
            [uriKeyValuePair[0], new EGPJackpotInfoService(uriKeyValuePair[1])]));

    constructor(private audit?: AuditInfo) {
        this.initCache();
    }

    public initCache() {
        initCache(
            async (id) => {
                const instance: JackpotInstanceDBInstance = await JackpotInstanceService.model.findOne({ where: { pid: id } });
                return instance ? instance.toInfo(true) : undefined;
            });
    }

    @auditJackpot(JackpotAuditType.CREATE)
    public async create(info: JackpotInstance, autoCreateRemote = false): Promise<JackpotInstance> {
        log.info("Create jackpot instance", info);

        this.validateJackpotConfigurationLevel(info);
        const [typeId, type] = await new JackpotTypeService().findId(info.type as string);
        this.validateJackpotDefinition(type.jpGameId,
            JackpotInstanceService.buildJackpotDefinition(type.definition, info.definition),
            { supportsWinCap: type.supportsWinCap });

        const region = info.regionCode !== undefined ?
                       await JackpotRegionService.findWithId(info.regionCode) : undefined;

        if (region) {
            await this.findOrCreateRemote(region, { ...info, regionCode: undefined, isGlobal: true }, autoCreateRemote);
        }

        // since the table declared with paranoid we can't user findOrCreate - the ORM won't find the record if it was
        // sooft-deleted but we will have a constraint error while creating - so we need to check for existence with
        // findAll
        const records = await JackpotInstanceService.model.findAll(
            {
                where: { pid: info.id },
                paranoid: false
            });

        if (records.length) {
            throw new Errors.JackpotInstanceAlreadyExist();
        }

        const dbInstance = await JackpotInstanceService.model.create(
            new DBJackpotInstance(info, typeId, region && region.id)
        );
        return await dbInstance.toInfo();
    }

    public async getExternalTickers(gameProviderCode: string, ids: string[], currency: string):
        Promise<EGPJackpotTickerResponse> {
        return JackpotInstanceService.externalTickerServices.get(gameProviderCode).getTickers(ids, currency);
    }

    public async getExternalJackpots(gameProviderCode: string, ids: string[]):
        Promise<EGPJackpotInfoResponse> {
        return JackpotInstanceService.externalJackpotInfoServices.get(gameProviderCode).getJackpots(ids);
    }

    @auditJackpot(JackpotAuditType.CLONE)
    public async clone(cloneId: string,
                       instanceOrigin: JackpotInternalInstance,
                       isTest: boolean): Promise<JackpotInternalInstance> {
        log.info("Clone jackpot instance", cloneId, instanceOrigin);

        const dbInstance: JackpotInstanceDBInstance = await JackpotInstanceService.model.findOne(
            { where: { id: instanceOrigin.internalId } });
        const clone: JackpotInstance = {
            id: cloneId,
            type: instanceOrigin.type,
            definition: dbInstance.definition || undefined,
            disableMode: dbInstance.disableMode,
            isDisabled: dbInstance.isDisabled,
            isGlobal: dbInstance.isGlobal,
            isOwned: dbInstance.isOwned,
            isLocal: dbInstance.isLocal,
            isTest
        };

        if (dbInstance.regionId) {
            await this.findOrCreateRemote(await dbInstance.getRegion(), clone, true);
        }

        let instance: JackpotInternalInstance;
        try {
            const cloneInstance = await JackpotInstanceService.model.create(
                new DBJackpotInstance(clone, dbInstance.typeId, dbInstance.regionId));
            instance = await cloneInstance.toInfo(true);
        } catch (err) {
            if (err instanceof Sequelize.UniqueConstraintError) {
                instance = await this.findInternal(cloneId);
            } else {
                return Promise.reject(err);
            }
        }

        return instance;
    }

    public async findAll(jackpotIds?: string[]): Promise<JackpotInstance[]> {
        let instances = await this.findAllInternal(JackpotId.filterInternal(jackpotIds));
        for (const [gameProviderCode, ids] of JackpotId.filterAndGroupExternal(jackpotIds)) {
            const externalInstances = await this.getExternalJackpots(gameProviderCode, ids);
            instances = instances.concat(externalInstances.map(eInstance => ({
                id: eInstance.jackpotId,
                type: eInstance.jackpotType
            } as JackpotInstance)));
        }
        return instances;
    }

    public async findAllInternal(jpIds?: string[]): Promise<JackpotInstance[]> {
        const queryOptions: FindOptions<JackpotInstanceDBInstance> = {
            include: [
                {
                    model: JackpotTypeService.model,
                    as: "type",
                },
                {
                    model: JackpotRegionService.model,
                    as: "region",
                },
            ],
        };

        if (jpIds?.length > 0) {
            queryOptions.where = { pid: { [Op.in]: jpIds } };
        }

        const instances: JackpotInstanceDBInstance[] = await JackpotInstanceService.model.findAll(queryOptions);
        const info = instances.map(instance => instance.toInfo());
        return Promise.all(info);
    }

    @measure({ name: "JackpotInstanceService.findInternal", isAsync: true, debugOnly: true })
    public async findInternal(id: string): Promise<JackpotInternalInstance> {
        const value: JackpotInternalInstance = await getCache().find(id);
        if (!value) {
            return Promise.reject(new Errors.JackpotInstanceNotFound(id));
        }
        return value;
    }

    public async findOrCreateTest(id: string): Promise<JackpotInternalInstance> {
        const value: JackpotInternalInstance = await this.findInternal(id);
        if (value.isTest) {
            return value;
        }
        const testId = this.buildTestJpId(id);
        const testValue: JackpotInternalInstance = await getCache().find(testId);

        if (testValue) {
            if (testValue.isTest) {
                testValue.createdFromId = id;
                return testValue;
            } else {
                return Promise.reject(new Errors.TestJackpotError());
            }
        }

        const clonedValue = await this.clone(testId, value, true);
        clonedValue.createdFromId = id;

        return clonedValue;
    }

    @measure({ name: "JackpotInstanceService.find", isAsync: true, debugOnly: true })
    public async find(id: string): Promise<JackpotInstance> {
        const instance: JackpotInstanceDBInstance = await JackpotInstanceService.model.findOne({ where: { pid: id } });
        if (!instance) {
            return Promise.reject(new Errors.JackpotInstanceNotFound(id));
        }
        return instance.toInfo();
    }

    public async findTypeOverride(typeId: number): Promise<JackpotInstance[]> {
        const instances = await JackpotInstanceService.model.findAll({ where: { typeId: typeId } });
        const data = [];
        for (const instance of instances) {
            if (instance.definition && Object.keys(instance.definition).length) {
                data.push(await instance.toInfo());
            }
        }
        return data;
    }

    public async getTicker(id: string, currencyCode?: string): Promise<Ticker> {
        const instance: JackpotInternalInstance = await this.findInternal(id);

        let ticker;
        if (instance.region) {
            ticker = await JackpotInstanceService.tickerService.getTicker(id);
        } else {
            const jpModule = new JackpotWallet(undefined, instance, 1, wallet);
            ticker = await jpModule.getTicker();
        }

        if (currencyCode && instance.definition.currency !== currencyCode) {
            return Promise.resolve(this.exchangeTicker(ticker, instance.definition.currency, currencyCode));
        } else {
            return Promise.resolve(ticker);
        }
    }

    @auditJackpot(JackpotAuditType.UPDATE)
    public async update(id: string, info: JackpotInstance): Promise<JackpotInstance> {
        const instance = await this.findInstance(id);

        if (info.isTest !== undefined && instance.isTest !== info.isTest) {
            return Promise.reject(new Errors.ValidationError("Cannot update jackpot isTest parameter"));
        }

        if (info.definition) {
            const type = await instance.getType();
            if (!type.overridable) {
                return Promise.reject(new Errors.ValidationError("Not allowed to override type definition"));
            }
            const instanceInfo = await instance.toInfo();
            const currentDefinition = instanceInfo.definition;
            if (info.definition.currency && currentDefinition.currency !== info.definition.currency) {
                return Promise.reject(new Errors.ValidationError("Cannot update jackpot currency"));
            }
            this.validateJackpotDefinition(instanceInfo.jpGameId,
                JackpotInstanceService.buildJackpotDefinition(currentDefinition, info.definition),
                { supportsWinCap: type.supportsWinCap });

            instance.definition = JackpotInstanceService.buildJackpotDefinition(instance.definition, info.definition);
        }

        const currentRegion = await instance.getRegion();
        if (info.regionCode && !(currentRegion && currentRegion.code === info.regionCode)) {
            const region = await JackpotRegionService.findWithId(info.regionCode);
            instance.regionId = region && region.id;
            instance.migratedAt = new Date();
        } else if (info.regionCode === null) {
            instance.regionId = null;
            instance.migratedAt = null;
        }

        if (info.isGlobal !== undefined || instance.regionId) {
            instance.isGlobal = info.isGlobal || !!instance.regionId;
        }

        if (info.isOwned !== undefined) {
            instance.isOwned = info.isOwned;
        }

        if (info.isLocal !== undefined) {
            instance.isLocal = info.isLocal;
        }

        if (info.jackpotConfigurationLevel !== undefined || instance.jackpotConfigurationLevel !== null) {
            this.validateJackpotConfigurationLevel(info, instance);
            instance.jackpotConfigurationLevel = info.jackpotConfigurationLevel || instance.jackpotConfigurationLevel;
        }

        if (info.entityId !== undefined) {
            instance.entityId = info.entityId;
        }

        if (info.jurisdictionCode !== undefined) {
            instance.jurisdictionCode = info.jurisdictionCode;
        }

        if (info.info !== undefined) {
            instance.info = info.info;
        }

        log.info("Update jackpot instance", info);
        await instance.save();

        getCache().invalidate(id);

        return instance.toInfo();
    }

    @auditJackpot(JackpotAuditType.UPDATE)
    public async updateIsTest(id: string, isTest: boolean): Promise<JackpotInstance> {
        const instance = await this.findInstance(id);

        instance.isTest = isTest;

        log.info("Update jackpot instance isTest parameter", `isTest: ${isTest}`);
        await instance.save();

        getCache().invalidate(id);

        return instance.toInfo();
    }

    private async findInstance(id: string): Promise<JackpotInstanceDBInstance> {
        const instance: JackpotInstanceDBInstance = await JackpotInstanceService.model
            .findOne({
                where: { pid: id },
                include: [
                    {
                        model: JackpotTypeService.model,
                        as: "type",
                    },
                    {
                        model: JackpotRegionService.model,
                        as: "region",
                    },
                ],
            });
        if (!instance) {
            return Promise.reject(new Errors.JackpotInstanceNotFound(id));
        }

        return instance;
    }

    private async findInstanceSilently(id: string): Promise<JackpotInstanceDBInstance> {
        try {
            return await this.findInstance(id);
        } catch (err) {
            log.error(err, "Failed to find jackpot instance");
            return undefined;
        }
    }

    public async applyPendingPool(id: string, poolId: string): Promise<JackpotInstance> {
        const instance = await this.findInstanceSilently(id);
        if (!instance) {
            return undefined;
        }

        const definition = instance.definition;
        const pendingPool = definition && definition.pendingList && definition.pendingList.find(pool => pool.id === poolId);

        if (pendingPool) {

            const poolIndex = definition.list && definition.list.findIndex(pool => pool.id === poolId);
            if (poolIndex !== undefined) {
                // replace specific pool definition with corresponding pending pool definition
                definition.list[poolIndex] = pendingPool;
                // update pending list excluding previously applied pending pool definition
                definition.pendingList = definition.pendingList.filter(pool => pool.id !== poolId);
                // a shallow copy to force sequelize to update
                instance.definition = { ...definition };

                log.debug(pendingPool, `Apply pending pool: id: ${id}, pool: ${poolId}`);
                // Sequelize does not detect deep mutations that's why we use 'changed' method for 'definition' field
                instance.changed("definition", true);
                await instance.save();
                getCache().invalidate(id);

                try {
                    const instanceInfo = await instance.toInfo();
                    await JackpotAuditService.auditJackpot(instanceInfo,
                        JackpotAuditType.APPLY_PENDING,
                        this.audit);
                } catch (err) {
                    log.error(err, "Error during audit apply pending pool for jackpot instance after win confirm");
                }

            } else {
                log.warn(`Undefined list in instance's definition or not found pool. id: ${id} poolId: ${poolId}`);
            }
        }

        return instance.toInfo();
    }

    public async updateInfo(id: string, update: JackpotInstanceInfo): Promise<JackpotInstance> {
        const instance = await this.findInstanceSilently(id);
        if (!instance) {
            return undefined;
        }

        if (update) {
            if (!instance.info) {
                instance.info = {};
            }
            if (update.externalId) {
                instance.info.externalId = update.externalId;
            }
            if (update.externalStartDate) {
                instance.info.externalStartDate = update.externalStartDate;
            }

            // making shallow copy to force sequelize regard 'info' as updated and save it after
            instance.info = { ...instance.info };

            log.debug(update, `Update info: id: ${id}`);
            await instance.save();
            getCache().invalidate(id);
        }

        return instance.toInfo();
    }

    @auditJackpot(JackpotAuditType.DISABLE)
    public async disable(id: string, disableMode: JackpotDisableMode): Promise<JackpotInstance> {
        const instance: JackpotInstanceDBInstance =
            await JackpotInstanceService.model.findOne({ where: { pid: id } });
        if (!instance) {
            return Promise.reject(new Errors.JackpotInstanceNotFound(id));
        }
        const canBeDisabled = (await instance.getType()).canBeDisabled;
        if (!canBeDisabled) {
            return Promise.reject(new Errors.JackpotCantBeDisabledError());
        }
        if (instance.isDisabled) {
            return Promise.reject(new Errors.JackpotDisabledError());
        }
        if (disableMode === JackpotDisableMode.IMMEDIATE && !instance.regionId) {
            const jpModule = new JackpotWallet(undefined, await instance.toInfo(), 1, wallet);
            await jpModule.disable();
        }
        instance.disableMode = disableMode;
        instance.isDisabled = true;
        await instance.save();
        getCache().invalidate(id);

        return instance.toInfo();
    }

    @auditJackpot(JackpotAuditType.ENABLE)
    public async enable(id: string): Promise<JackpotInstance> {
        const instance: JackpotInstanceDBInstance =
            await JackpotInstanceService.model.findOne({ where: { pid: id } });
        if (!instance) {
            return Promise.reject(new Errors.JackpotInstanceNotFound(id));
        }
        const canBeDisabled = (await instance.getType()).canBeDisabled;
        if (!canBeDisabled) {
            return Promise.reject(new Errors.JackpotCantBeDisabledError());
        }
        if (!instance.regionId) {
            const jpModule = new JackpotWallet(undefined, await instance.toInfo(), 1, wallet);
            await jpModule.enable();
        }

        instance.isDisabled = false;
        await instance.save();
        getCache().invalidate(id);

        return instance.toInfo();
    }

    @auditJackpot(JackpotAuditType.ARCHIVE)
    public async remove(id: string): Promise<JackpotInstance> {
        const instance: JackpotInstanceDBInstance =
            await JackpotInstanceService.model.findOne({ where: { pid: id } });
        if (!instance) {
            return Promise.reject(new Errors.JackpotInstanceNotFound(id));
        }
        log.info("Remove jackpot instance", id);
        const removed = await JackpotInstanceService.model.destroy({ where: { pid: id } });
        if (!removed) {
            return Promise.reject(new Errors.JackpotInstanceNotFound(id));
        }
        getCache().invalidate(id);

        return instance.toInfo();
    }

    @auditJackpot(JackpotAuditType.GAME_ACTION)
    public async performGameAction(id: string, action: GameAction): Promise<any> {
        const instance: JackpotInstanceDBInstance =
            await JackpotInstanceService.model.findOne({ where: { pid: id } });
        if (!instance) {
            return Promise.reject(new Errors.JackpotInstanceNotFound(id));
        }

        if (!instance.regionId) {
            const info: JackpotInternalInstance = await instance.toInfo();
            const jackpot: Jackpot = { id, definition: info.definition, getTicker: undefined };
            const game = GameService.load(info.jpGameId, jackpot, undefined, undefined);
            if (!game.performAction) {
                return Promise.reject(new Errors.ValidationError("Jpn game doesn't support actions"));
            }
            const jpModule = new JackpotWallet(undefined, info, 1, wallet);
            const ticker = await jpModule.getTicker();
            const changes = game.performAction(action, ticker.pools);
            log.debug("Game action: property changes", changes);
            if (changes && changes.length) {
                return await jpModule.applyPropertyChanges(changes);
            } else {
                return [];
            }
        } else {
            return Promise.reject(new Errors.ValidationError("Actions can be applied only to local instance"));
        }
    }

    private exchangeTicker(originTicker: Ticker, baseCurrency: string,
                           targetCurrency: string): Ticker {

        const exchangeRate = 1 / currencyService.getExchangeRate(targetCurrency, baseCurrency);
        const pools = {};

        const result: Ticker = {
            id: originTicker.id,
            currency: targetCurrency,
            pools: pools,
            seqId: originTicker.seqId
        };

        if (originTicker.isDisabled) {
            result.isDisabled = originTicker.isDisabled;
        }

        for (const pool of Object.keys(originTicker.pools)) {
            const originalPool = originTicker.pools[pool];
            pools[pool] = {
                seed: originalPool.seed * exchangeRate,
                progressive: originalPool.progressive * exchangeRate,
            };
        }

        return result;
    }

    public async updateJackpotType(type: JackpotTypeDBInstance) {
        const instances: JackpotInstanceDBInstance[] =
            await JackpotInstanceService.model.findAll({ where: { typeId: type.internalId } });
        for (const instance of instances) {
            getCache().invalidate(instance.pid);
        }

        for (const instance of instances) {
            const info = await instance.toInfo();
            await JackpotAuditService.auditJackpot(info, JackpotAuditType.TYPE_UPDATE, this.audit);
        }
    }

    private validateJackpotDefinition(jpGameId: string, definition: JackpotDefinition,
                                      options: { supportsWinCap?: boolean } = {}): void {
        if (!Currencies.exists(definition.currency)) {
            throw new Errors.CurrencyNotFoundError(definition.currency);
        }

        const game = GameService.load(jpGameId, { definition } as any, undefined, undefined);

        if ("winCapping" in definition &&
            (!options.supportsWinCap || typeof definition.winCapping !== "number" || definition.winCapping <= 0)) {
            throw new Errors.ValidationError(`Jackpot win capping is unsupported or invalid`);
        }

        const errors = game.validateJackpotDefinition();
        if (errors && errors.length) {
            throw new Errors.ValidationError(`Jackpot definition is not valid: ${errors.join(",")}`);
        }
    }

    private validateJackpotConfigurationLevel(info: JackpotInstance,
                                              instance?: JackpotInstanceDBInstance): void {
        const expectedValues = Object.values(JackpotConfigurationLevel).filter(el => typeof el === "number");
        if (info.jackpotConfigurationLevel !== undefined && isNaN(+info.jackpotConfigurationLevel)) {
            throw new Errors.ValidationError(`jackpotConfigurationLevel should be a number. Expected values: ${expectedValues}`);
        }
        if (info.jackpotConfigurationLevel !== undefined && (info.jackpotConfigurationLevel < 1 || info.jackpotConfigurationLevel > 6)) {
            throw new Errors.ValidationError(`Jackpot configuration level is not valid. Expected values: ${expectedValues}`);
        }
        this.validateJackpotConfigurationLevelEntityAndJurisdiction(info, instance);
        this.validateAndSetJackpotConfigurationFlags(info, instance);
        this.removeFieldsBasedOnConfigurationLevel(info, instance);
    }

    private removeFieldsBasedOnConfigurationLevel(info: JackpotInstance,
                                                  instance?: JackpotInstanceDBInstance): void {
        const jackpotConfigurationLevel = info.jackpotConfigurationLevel || instance?.jackpotConfigurationLevel;
        if (!jackpotConfigurationLevel) {
            return;
        }
        const configLevelsThatRequireEntityId = [
            JackpotConfigurationLevel.SPECIFIC_BRAND_ONLY,
            JackpotConfigurationLevel.SHARED_BETWEEN_ONE_OPERATOR_FOR_ONE_JURISDICTION,
            JackpotConfigurationLevel.SHARED_BETWEEN_SEVERAL_JURISDICTIONS_FOR_ONE_OPERATOR
        ];
        const configLevelsThatRequireJurisdictionCode = [
            JackpotConfigurationLevel.SHARED_BETWEEN_ONE_OPERATOR_FOR_ONE_JURISDICTION,
            JackpotConfigurationLevel.SHARED_BETWEEN_SEVERAL_OPERATORS_FOR_ONE_JURISDICTION
        ];
        if (!configLevelsThatRequireEntityId.includes(jackpotConfigurationLevel)) {
            info.entityId = null;
        }
        if (!configLevelsThatRequireJurisdictionCode.includes(jackpotConfigurationLevel)) {
            info.jurisdictionCode = null;
        }

        const configLevelsThatAllowIsOwnedFlag = [
            JackpotConfigurationLevel.SPECIFIC_BRAND_ONLY,
            JackpotConfigurationLevel.SHARED_BETWEEN_ONE_OPERATOR_FOR_ONE_JURISDICTION,
            JackpotConfigurationLevel.SHARED_BETWEEN_SEVERAL_JURISDICTIONS_FOR_ONE_OPERATOR
        ];
        if (!configLevelsThatAllowIsOwnedFlag.includes(jackpotConfigurationLevel)) {
            info.isOwned = false;
        }
    }

    private validateAndSetJackpotConfigurationFlags(info: JackpotInstance,
                                                    instance?: JackpotInstanceDBInstance): void {
        const jackpotConfigurationLevel = info.jackpotConfigurationLevel || instance?.jackpotConfigurationLevel;
        if (!jackpotConfigurationLevel) {
            return;
        }
        const configLevelsThatAllowIsOwnedFlag = [
            JackpotConfigurationLevel.SPECIFIC_BRAND_ONLY,
            JackpotConfigurationLevel.SHARED_BETWEEN_ONE_OPERATOR_FOR_ONE_JURISDICTION,
            JackpotConfigurationLevel.SHARED_BETWEEN_SEVERAL_JURISDICTIONS_FOR_ONE_OPERATOR
        ];
        if (!configLevelsThatAllowIsOwnedFlag.includes(jackpotConfigurationLevel) && info.isOwned) {
            throw new Errors.ValidationError(
                `isOwned can only be set to true for the following configuration levels: ${configLevelsThatAllowIsOwnedFlag}`
            );
        }
        if (jackpotConfigurationLevel === JackpotConfigurationLevel.SHARED_GLOBALLY) {
            if (info.isLocal === true) {
                throw new Errors.ValidationError(
                    `You cannot have isLocal = true for jackpot configuration level ${jackpotConfigurationLevel}`
                );
            }
            if (info.isGlobal === false) {
                throw new Errors.ValidationError(
                    `You cannot have isGlobal = false for jackpot configuration level ${jackpotConfigurationLevel}`
                );
            } else {
                info.isGlobal = true;
            }
        } else {
            if (info.isGlobal === true) {
                throw new Errors.ValidationError(
                    `You cannot have isGlobal = true for a jackpot configuration level lower than ${JackpotConfigurationLevel.SHARED_GLOBALLY}`
                );
            }
            if (instance?.isGlobal === true) {
                instance.isGlobal = false;
            }
        }
    }

    private validateJackpotConfigurationLevelEntityAndJurisdiction(info: JackpotInstance,
                                                                   instance?: JackpotInstanceDBInstance) {
        if (!info.jackpotConfigurationLevel) {
            return;
        }
        const configLevelsThatRequireEntityId = [
            JackpotConfigurationLevel.SPECIFIC_BRAND_ONLY,
            JackpotConfigurationLevel.SHARED_BETWEEN_ONE_OPERATOR_FOR_ONE_JURISDICTION,
            JackpotConfigurationLevel.SHARED_BETWEEN_SEVERAL_JURISDICTIONS_FOR_ONE_OPERATOR
        ];
        const entityId = info.entityId || (instance && instance.get("entityId"));
        if (configLevelsThatRequireEntityId.includes(info.jackpotConfigurationLevel) && !entityId) {
            throw new Errors.ValidationError(`entityId is required for jackpotConfigurationLevel = ${info.jackpotConfigurationLevel}`);
        }
        const configLevelsThatRequireJurisdictionCode = [
            JackpotConfigurationLevel.SHARED_BETWEEN_ONE_OPERATOR_FOR_ONE_JURISDICTION,
            JackpotConfigurationLevel.SHARED_BETWEEN_SEVERAL_OPERATORS_FOR_ONE_JURISDICTION
        ];
        const jurisdictionCode = info.jurisdictionCode || (instance && instance.get("jurisdictionCode"));
        if (configLevelsThatRequireJurisdictionCode.includes(info.jackpotConfigurationLevel) && !jurisdictionCode) {
            throw new Errors.ValidationError(`jurisdictionCode is required for jackpotConfigurationLevel = ${info.jackpotConfigurationLevel}`);
        }
    }

    public static buildJackpotDefinition(definition: JackpotDefinition,
                                         definitionOverride: JackpotDefinition): JackpotDefinition {
        if (!definitionOverride) {
            return definition;
        }
        if (!definition) {
            return definitionOverride;
        }

        const { currency, loanPool, features, list, ...propertiesOverride } = definitionOverride;
        const result: JackpotDefinition = { ...definition, ...propertiesOverride };

        if (currency) {
            result.currency = currency;
        }

        if (definitionOverride.list && definition.list) {
            const overrideByInstance: boolean = definition.features && definition.features.includes(
                JP_FEATURE_OVERRIDE_POOLS_BY_INSTANCE);
            const resultPools: JackpotPoolDefinition[] = [];
            if (overrideByInstance) {
                resultPools.push(...list);
            } else {
                for (const typeDefPool of definition.list) {
                    const defPool = list.find((item) => item.id === typeDefPool.id);
                    if (defPool) {
                        resultPools.push({ ...typeDefPool, ...defPool });
                    } else {
                        resultPools.push(typeDefPool);
                    }
                }
            }

            result.list = resultPools;
        }

        if (loanPool) {
            result.loanPool = { ...definition.loanPool, ...loanPool };
        }

        return result;
    }

    private buildTestJpId(id: string): string {
        return config.autoCreateTestJp.prefix + id + config.autoCreateTestJp.suffix;
    }

    private async findOrCreateRemote(region: JackpotRegion,
                                     info: JackpotInstance, autoCreate = false): Promise<JackpotInstance> {
        const remoteApi = RemoteJackpotApiCache.getRemoteJackpotApi(region);
        try {
            return await remoteApi.findOrCreateJackpot(info, autoCreate);
        } catch (err) {
            log.error(err, "Failed to find/create remote jackpot instance");
            return Promise.reject(new Errors.ValidationError("Failed to find/create remote jackpot instance"));
        }
    }
}

export default new JackpotInstanceService();
