import { NextFunction, Request, Response } from "express";
import { ACCESS_TOKEN } from "../utils/security";
import { ContextVariables } from "../utils/contextVariables";

export const sanitizeCommaSeparatedString = (value) => {
    if (!value) {
        return;
    }
    return value.toString().split(",").map(name => name.trim());
};

export function setAuthContext(req: Request, res: Response, next: NextFunction) {
    const accessToken = req.header(ACCESS_TOKEN);
    ContextVariables.setAuth(accessToken, req.body);
    next();
}
