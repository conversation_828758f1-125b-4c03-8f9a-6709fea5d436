import { Application, static as staticContent } from "express";
import * as path from "path";
import VersionRouter from "./version";
import HealthRouter from "./health";
import RemoteJPNRouter from "./remoteJpnRouter";
import { createErrorHandler } from "./errorMiddleware";
import logger from "../utils/logger";
import * as bodyParser from "body-parser";
import { apiSwaggerRemoteApi, swaggerCspMiddleware } from "../utils/swagger";
import config from "../config";

const log = logger("remote-api-routers");

const swaggerTools = require("swagger-tools");
const swaggerUiConf = {
    swaggerUiDir: "node_modules/swagger-ui-dist",
    swaggerUi: "/docs",
};

export function defineRoutes(app: Application): Application {
    app.use(bodyParser.json());

    app.use("/api/v2/jpn", RemoteJPNRouter);

    app.use("/v1", VersionRouter);
    app.use("/v1", HealthRouter);

    app.use(createErrorHandler(log));

    if (!config.disableSwagger) {
        app.use("/docs", swaggerCspMiddleware);
        app.use("/swagger-resources", swaggerCspMiddleware);
        app.use("/api-docs", swaggerCspMiddleware);

        swaggerTools.initializeMiddleware(apiSwaggerRemoteApi.get(), (middleware) => {
            app.use(middleware.swaggerMetadata());
            app.use("/", middleware.swaggerUi(swaggerUiConf));
        });
        app.use("/docs", staticContent(path.resolve(process.cwd(), "swagger-ui")));
        app.use("/swagger-resources", staticContent(path.resolve(process.cwd(), "swagger-resources")));
        app.route("/api-docs").get((req, resp) => resp.send(apiSwaggerRemoteApi.get()));
    }
    app.route("/").get((req, res, next) => {
        res.sendStatus(200);
    });
    return app;
}
