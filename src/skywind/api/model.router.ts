import { NextFunction, Request, Response, Router, } from "express";
import { sanitizeCommaSeparatedString } from "./middleware";
import { JackpotTypeService } from "../services/jackpot.type";
import { isValidJackpotId, JackpotInstanceService } from "../services/jackpot.instance";
import { authenticate } from "../utils/security";
import * as Errors from "../errors";
import { CurrencyNotFoundError as BadCurrencyCodeError } from "@skywind-group/sw-currency-exchange";
import { AuditInfo, DISABLE_MODES, JackpotAuditInitiator, JackpotInstance, JackpotType } from "./model";
import RegionService from "../services/regionService";
import JackpotAuditService, { DEFAULT_AUDIT_LIST_LIMIT, DEFAULT_AUDIT_LIST_OFFSET } from "../services/jackpotAudits";
import { JPNRequest } from "./jpn.router";
import { IpFamily, Valida<PERSON> } from "../utils/validators";
import { GameAction } from "@skywind-group/sw-jpn-core";
import { Op } from "sequelize";

const router: Router = Router();

interface WithAudit {
    auditInfo: AuditInfo;
}

async function auditInfo(req: Request & WithAudit, res: Response, next: NextFunction) {
    const audit: AuditInfo = {
        initiatorType: JackpotAuditInitiator.USER,
        userAgent: req.headers["user-agent"] as string,
        initiatorName: req?.query?.initiator as string,
        ip: (req?.query?.ip as string) || req.connection.remoteAddress
    };
    req.auditInfo = audit;
    next();
}

async function getTypes(req: Request, res: Response, next: NextFunction) {
    try {
        const names = sanitizeCommaSeparatedString(req.query.names);
        const types = await new JackpotTypeService().findAll(names);
        res.json(types);
    } catch (ex) {
        next(ex);
    }
}

async function createType(req: Request, res: Response, next: NextFunction) {
    const request: JackpotType = req.body;
    if (!request.name) {
        return next(new Errors.ValidationError("name is missing"));
    }
    if (!request.jpGameId) {
        return next(new Errors.ValidationError("jpGameId is missing"));
    }
    if (!request.definition) {
        return next(new Errors.ValidationError("definition is missing"));
    }
    try {
        const type = await new JackpotTypeService().create(req.body);
        res.status(201).json(type);
    } catch (ex) {
        next(ex);
    }
}

async function getType(req: Request, res: Response, next: NextFunction) {
    try {
        const type = await new JackpotTypeService().find(req.params.type);
        res.json(type);
    } catch (ex) {
        next(ex);
    }
}

async function updateType(req: Request & WithAudit, res: Response, next: NextFunction) {
    try {
        const type = await new JackpotTypeService(req.auditInfo).update(req.params.type, req.body);
        res.json(type);
    } catch (ex) {
        next(ex);
    }
}

async function deleteType(req: Request, res: Response, next: NextFunction) {
    try {
        await new JackpotTypeService().remove(req.params.type);
        res.status(204).end();
    } catch (ex) {
        next(ex);
    }
}

async function getInstances(req: Request, res: Response, next: NextFunction) {
    const jackpotIds = sanitizeCommaSeparatedString(req.query.ids);
    try {
        const instances = await new JackpotInstanceService().findAll(jackpotIds);
        res.json(instances);
    } catch (ex) {
        next(ex);
    }
}

async function createInstance(req: Request & WithAudit, res: Response, next: NextFunction) {
    const request: JackpotInstance = req.body;
    if (!isValidJackpotId(request.id)) {
        return next(new Errors.ValidationError("id is missing or not valid"));
    }
    if (!request.type) {
        return next(new Errors.ValidationError("type is missing"));
    }
    try {
        const instance = await new JackpotInstanceService(req.auditInfo)
            .create(req.body, req.query.autoCreateRemote === "true");
        res.status(201).json(instance);
    } catch (ex) {
        next(ex);
    }
}

async function getInstance(req: Request, res: Response, next: NextFunction) {
    try {
        const instance = await new JackpotInstanceService().find(req.params.id);
        res.json(instance);
    } catch (ex) {
        next(ex);
    }
}

async function getInstanceTicker(req: Request, res: Response, next: NextFunction) {
    const currencyCode: string = req.query.currency as string;
    try {
        const ticker = await new JackpotInstanceService().getTicker(req.params.id, currencyCode);
        res.json(ticker);
    } catch (ex) {
        if (ex instanceof BadCurrencyCodeError) {
            next(new Errors.CurrencyNotFoundError(ex.currency));
        } else {
            next(ex);
        }
    }
}

async function updateInstance(req: Request & WithAudit, res: Response, next: NextFunction) {
    try {
        const instance = await new JackpotInstanceService(req.auditInfo).update(req.params.id, req.body);
        res.json(instance);
    } catch (ex) {
        next(ex);
    }
}

async function updateInstanceIsTestParam(req: Request & WithAudit, res: Response, next: NextFunction) {
    try {
        const instance = await new JackpotInstanceService(req.auditInfo).updateIsTest(req.params.id,
            req.query.isTest === "true");
        res.json(instance);
    } catch (ex) {
        next(ex);
    }
}

async function deleteInstance(req: Request & WithAudit, res: Response, next: NextFunction) {
    try {
        await new JackpotInstanceService(req.auditInfo).remove(req.params.id);
        res.status(204).end();
    } catch (ex) {
        next(ex);
    }
}

async function disableInstance(req: Request & WithAudit, res: Response, next: NextFunction) {
    try {
        const disableMode = +req.query.disableMode || 0;
        if (!DISABLE_MODES.includes(disableMode)) {
            return next(new Errors.ValidationError("disableMode is not valid"));
        }
        const response = await new JackpotInstanceService(req.auditInfo).disable(req.params.id, disableMode);
        res.status(200).send(response).end();
    } catch (ex) {
        next(ex);
    }
}

async function enableInstance(req: Request & WithAudit, res: Response, next: NextFunction) {
    try {
        const response = await new JackpotInstanceService(req.auditInfo).enable(req.params.id);
        res.status(200).send(response).end();
    } catch (ex) {
        next(ex);
    }
}

async function gameAction(req: Request & WithAudit, res: Response, next: NextFunction) {
    try {
        const action: GameAction = req.body;
        if (!action || !action.action || !action.payload) {
            return next(new Errors.ValidationError("action is not valid"));
        }
        const auditInfo = req.auditInfo;
        auditInfo.id = req.params.id;
        const response = await new JackpotInstanceService(auditInfo).performGameAction(req.params.id, action);
        res.status(200).send(response).end();
    } catch (ex) {
        next(ex);
    }
}

/**
 * Creates type by typeId from jpn-games configuration.
 */
async function createTypeFromModule(req: Request, res: Response, next: NextFunction) {
    if (!req.params.type) {
        return next(new Errors.ValidationError("type is missing"));
    }

    try {
        const type = await new JackpotTypeService().createTypeFromModule(req.params.type,
            req.query.configurable === "true", req.query.overridable === "true", req.query.canBeDisabled === "true");
        res.status(201).json(type);
    } catch (ex) {
        next(ex);
    }
}

/**
 * Updates type by typeId from jpn-games configuration.
 */
async function updateTypeFromModule(req: Request & WithAudit, res: Response, next: NextFunction) {
    if (!req.params.type) {
        return next(new Errors.ValidationError("type is missing"));
    }

    try {
        const type = await new JackpotTypeService(req.auditInfo).updateTypeFromModule(req.params.type);
        res.status(200).json(type);
    } catch (ex) {
        next(ex);
    }
}

/**
 * Validates jp types in database with config in jp-games module
 */
async function validateTypes(req: Request, res: Response, next: NextFunction) {
    try {
        await new JackpotTypeService().validateTypes();
        res.status(200).json({ "validation": "Ok" });
    } catch (ex) {
        next(ex);
    }
}

async function createRegion(req: Request, res: Response, next: NextFunction) {
    try {
        const data = await RegionService.create(req.body);
        res.status(201).json(data);
    } catch (err) {
        next(err);
    }
}

async function getRegions(req: Request, res: Response, next: NextFunction) {
    try {
        const data = await RegionService.findAll();
        res.status(200).json(data);
    } catch (err) {
        next(err);
    }
}

async function getRegion(req: Request, res: Response, next: NextFunction) {
    try {
        const data = await RegionService.find(req.params.code);
        res.status(200).json(data);
    } catch (err) {
        next(err);
    }
}

async function updateRegion(req: Request, res: Response, next: NextFunction) {
    try {
        const data = await RegionService.update(req.params.code, req.body);
        res.status(200).json(data);
    } catch (err) {
        next(err);
    }
}

async function removeRegion(req: Request, res: Response, next: NextFunction) {
    try {
        await RegionService.remove(req.params.code);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

// export for tests
export async function validateAudit(req: JPNRequest, res: Response, next: NextFunction) {
    const initiatorType: any = req.query.initiatorType;
    if (initiatorType) {
        if (!Object.values(JackpotAuditInitiator).includes(initiatorType.toLowerCase())) {
            return next(new Errors.ValidationError(
                `Invalid initiatorType, should be one of: [${Object.values(JackpotAuditInitiator)}]`));
        }
    }

    if (req.query.ip) {
        if (Validators.validateIP(req.query.ip as string) === IpFamily.NONE) {
            return next(new Errors.ValidationError("Invalid IP address"));
        }
    }

    if (!Validators.isOptionalTs(req.query.ts__gte as any)) {
        return next(new Errors.ValidationError("Invalid ts__gte"));
    }

    if (!Validators.isOptionalTs(req.query.ts__gte as any)) {
        return next(new Errors.ValidationError("Invalid ts__lte"));
    }

    next();
}

async function getAudits(req: Request, res: Response, next: NextFunction) {
    try {
        const query = {} as any;
        if (req.query.jackpotId) {
            query.jackpotId = req.query.jackpotId;
        }
        if (req.query.type) {
            query.type = req.query.type;
        }
        if (req.query.initiatorType) {
            query.initiatorType = (req.query.initiatorType as any).toLowerCase();
        }
        if (req.query.initiatorName) {
            query.initiatorName = req.query.initiatorName;
        }
        if (req.query.ip) {
            if (Validators.validateIP(req.query.ip as string) === IpFamily.IP_V4) {
                query.ip = "::ffff:" + req.query.ip;
            } else {
                query.ip = req.query.ip;
            }
        }
        if (req.query.ts__gte) {
            query.ts = { [Op.gte]: new Date(+req.query.ts__gte) };
        }
        if (req.query.ts__lte) {
            query.ts = { ...query.ts, [Op.lte]: new Date(+req.query.ts__lte) };
        }
        const limit: number = +(req.query.limit || DEFAULT_AUDIT_LIST_LIMIT);
        const offset: number = +(req.query.offset || DEFAULT_AUDIT_LIST_OFFSET);
        const data = await JackpotAuditService.findAll(query, offset, limit);
        res.status(200).json(data);
    } catch (err) {
        next(err);
    }
}

router.get("/types", authenticate, getTypes);
router.post("/types", authenticate, createType);
router.get("/types/:type", authenticate, getType);
router.patch("/types/:type", authenticate, auditInfo, updateType);
router.delete("/types/:type", authenticate, deleteType);
router.post("/initType/:type", authenticate, createTypeFromModule);
router.post("/updateType/:type", authenticate, auditInfo, updateTypeFromModule);
router.get("/validateTypes", authenticate, validateTypes);

router.get("/jackpots", authenticate, getInstances);
router.post("/jackpots", authenticate, auditInfo, createInstance);
router.get("/jackpots/:id", authenticate, getInstance);
router.get("/jackpots/:id/ticker", authenticate, getInstanceTicker);
router.patch("/jackpots/:id", authenticate, auditInfo, updateInstance);
router.patch("/jackpots/:id/isTest", authenticate, auditInfo, updateInstanceIsTestParam);
router.delete("/jackpots/:id", authenticate, auditInfo, deleteInstance);
router.put("/jackpots/:id/disable", authenticate, auditInfo, disableInstance);
router.put("/jackpots/:id/enable", authenticate, auditInfo, enableInstance);
router.post("/jackpots/:id/game-action", authenticate, auditInfo, gameAction);

router.post("/regions", authenticate, createRegion);
router.get("/regions", authenticate, getRegions);
router.get("/regions/:code", authenticate, getRegion);
router.patch("/regions/:code", authenticate, updateRegion);
router.delete("/regions/:code", authenticate, removeRegion);

router.get("/audits", authenticate, validateAudit, getAudits);

export default router;
