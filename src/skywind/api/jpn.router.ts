import {
    Router,
    Request,
    Response,
    NextFunction,
} from "express";
import {
    ContributionRequest, ContributionResponse, AuthRequest, AuthResponse, TickerResponse, TransactionIdResponse,
    WinConfirmRequest, WinConfirmResponse, MiniGameRequest, MiniGameResponse, WinJackpotRequest, PoolDepositRequest,
    CheckWinRequest, CheckWinResponse, BaseRequest, DeferredContributionRequest, UpdatePoolRequest
} from "@skywind-group/sw-jpn-core";
import * as Errors from "../errors";
import { JackpotService, TransferPoolRequest } from "../services/jackpot.service";
import JackpotInstanceService from "../services/jackpot.instance";
import JackpotContextService from "../services/jackpot.context";
import wallet from "../services/wallet.service";
import { requestLogData } from "../utils/utils";
import logger from "../utils/logger";
import { Currencies } from "@skywind-group/sw-currency-exchange";

const log = logger("routes:jpn.router");

const jpnService: JackpotService = new JackpotService(
    JackpotInstanceService, wallet, JackpotContextService);

export interface JPNRequest extends Request {
    auth: AuthRequest;
}

const router: Router = Router();

/**
 * auth
 */
async function auth(req: Request, res: Response, next: NextFunction) {
    const request: AuthRequest = req.body as AuthRequest;
    if (!request.playerCode) {
        return next(new Errors.ValidationError("playerCode is missing"));
    }
    if (!request.brandId || typeof request.brandId !== "number") {
        return next(new Errors.ValidationError("brandId is not valid"));
    }
    if (!request.jackpotIds || !Array.isArray(request.jackpotIds) || !request.jackpotIds.length) {
        return next(new Errors.ValidationError("jackpotIds is not valid"));
    }
    if (!request.currency) {
        return next(new Errors.ValidationError("currency is missing"));
    }
    if (!request.gameCode) {
        return next(new Errors.ValidationError("game code is missing"));
    }
    try {
        const result: AuthResponse = await jpnService.auth(request);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

async function authenticate(req: JPNRequest, res: Response, next: NextFunction) {
    const token: string = req.get("X-ACCESS-TOKEN");
    if (!token) {
        return next(new Errors.TokenIsMissing());
    }
    try {
        const auth: AuthRequest = await jpnService.authenticate(token);
        req.auth = auth;
        next();
    } catch (ex) {
        next(ex);
    }
}

function authenticateTransfer(req: JPNRequest, res: Response, next: NextFunction) {
    const token: string = req.get("X-ACCESS-TOKEN");
    if (!token) {
        return next(new Errors.TokenIsMissing());
    }
    try {
        const auth: AuthRequest = jpnService.authenticateTransfer(token);
        req.auth = auth;
        return next();
    } catch (error) {
        return next(error);
    }
}

export class Validators {

    public static isJackpotId(auth: AuthRequest, jpId: string): boolean {
        return jpId && (auth.jackpotIds.indexOf(jpId) !== -1);
    }

    public static isJackpotIds(auth: AuthRequest, jpIds: string[], unique?: boolean): boolean {
        return jpIds && Array.isArray(jpIds) &&
            jpIds.filter((jpId => (!Validators.isJackpotId(auth, jpId)))).length === 0 &&
            (!unique || (new Set(jpIds)).size === jpIds.length);
    }

    public static isExchangeRate(value): boolean {
        return value === undefined || (value > 0 && typeof value === "number");
    }

    public static isRoundId(value): boolean {
        return value && Number.isInteger(Number(value));
    }

    public static isTransactionId(value): boolean {
        return value && typeof value === "string" && value.length === 28;
    }

    public static isAmount(auth: AuthRequest, value) {
        if (typeof value === "number") {
            value = Currencies.format(auth.currency, value);
            return value > 0;
        }
        return false;
    }
}

function validateBaseRequest(request: BaseRequest) {
    if (!Validators.isTransactionId(request.transactionId)) {
        return new Errors.ValidationError("transactionId is not valid");
    }
    if (!Validators.isExchangeRate(request.exchangeRate)) {
        return new Errors.ValidationError("exchangeRate is not valid");
    }
    if (!Validators.isRoundId(request.roundId)) {
        return new Errors.ValidationError("roundId is not valid");
    }
}

async function validateExchangeRates(req: JPNRequest, res: Response, next: NextFunction) {
    let exchangeRates = req.body.exchangeRates || req.query.exchangeRates;
    if (exchangeRates) {
        if (typeof exchangeRates === "string") {
            try {
                exchangeRates = JSON.parse(exchangeRates);
            } catch (err) {
                return next(new Errors.ValidationError("exchangeRates are not valid"));
            }
        }
        if (typeof exchangeRates !== "object" || Array.isArray(exchangeRates)) {
            return next(new Errors.ValidationError("exchangeRates are not valid"));
        }
        for (const rate of Object.values(exchangeRates)) {
            if (!Validators.isExchangeRate(rate)) {
                return next(new Errors.ValidationError("exchangeRates are not valid"));
            }
        }
    } else {
        const exchangeRate = req.body.exchangeRate ||
            (!isNaN(+req.query.exchangeRate) ? +req.query.exchangeRate : req.query.exchangeRate);
        if (exchangeRate) {
            if (!Validators.isExchangeRate(exchangeRate)) {
                return next(new Errors.ValidationError("exchangeRate is not valid"));
            }
            const jp = await JackpotInstanceService.findInternal(req.auth.jackpotIds[0]);
            exchangeRates = {
                [jp.definition.currency]: exchangeRate
            };
        }
    }

    req.body.exchangeRates = exchangeRates;
    req.query.exchangeRates = exchangeRates;

    next();
}

async function getTicker(req: JPNRequest, res: Response, next: NextFunction) {
    try {
        log.debug({ "reqData": requestLogData(req) }, "Http Request");
        const result: TickerResponse = await jpnService.getTicker(req.auth, { exchangeRates: req.query.exchangeRates as any });
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

async function generateContributionTrxId(req: JPNRequest, res: Response, next: NextFunction) {
    try {
        const trxId: TransactionIdResponse = await jpnService.generateContributionTrxId();
        res.json(trxId);
    } catch (err) {
        next(err);
    }
}

/**
 * contribute
 */
async function contribute(req: JPNRequest, res: Response, next: NextFunction) {
    const request: ContributionRequest = req.body as ContributionRequest;
    const validationError = validateBaseRequest(request);
    if (validationError) {
        return next(validationError);
    }

    if ((request as DeferredContributionRequest).deferredContribution) {
        try {
            log.debug({ "reqData": requestLogData(req) }, "Http Request");
            const result: ContributionResponse = await jpnService.continueDeferredContribute(req.auth, {
                ...request, type: request.type || "contribution"
            });
            res.json(result);
        } catch (ex) {
            next(ex);
        }
    } else {
        if (!Validators.isAmount(req.auth, request.amount)) {
            return next(new Errors.ValidationError("amount is not valid"));
        }
        if (request.jackpotIds && !Validators.isJackpotIds(req.auth, request.jackpotIds, true)) {
            return next(new Errors.NotAuthorizedJackpotError(JSON.stringify(request.jackpotIds)));
        }
        try {
            log.debug({ "reqData": requestLogData(req) }, "Http Request");
            const result: ContributionResponse = await jpnService.contribute(req.auth, {
                ...request, type: request.type || "contribution"
            });
            res.json(result);
        } catch (ex) {
            next(ex);
        }
    }
}

/**
 * check jackpot win probability
 */
async function checkWin(req: JPNRequest, res: Response, next: NextFunction) {
    const request: CheckWinRequest = req.body as CheckWinRequest;
    const validationError = validateBaseRequest(request);
    if (validationError) {
        return next(validationError);
    }
    try {
        log.debug({ "reqData": requestLogData(req) }, "Http Request");
        const result: CheckWinResponse = await jpnService.checkWin(req.auth, request);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

/**
 * Update mini-game status
 */
async function updateMiniGame(req: JPNRequest, res: Response, next: NextFunction) {
    const request: MiniGameRequest = req.body;
    const validationError = validateBaseRequest(request);
    if (validationError) {
        return next(validationError);
    }
    if (!Validators.isJackpotId(req.auth, request.jackpotId)) {
        return next(new Errors.NotAuthorizedJackpotError(request.jackpotId));
    }
    try {
        const result: MiniGameResponse = await jpnService.updateMiniGame(req.auth, request);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

/**
 * Confirms that jackpot win amount was paid to player
 */
async function confirmWin(req: JPNRequest, res: Response, next: NextFunction) {
    const request: WinConfirmRequest = req.body as WinConfirmRequest;
    const jpIds: string[] = Array.isArray(request.jackpotId) ? request.jackpotId : [request.jackpotId];
    if (!Validators.isJackpotIds(req.auth, jpIds)) {
        return next(new Errors.NotAuthorizedJackpotError(JSON.stringify(jpIds)));
    }
    if (!Validators.isTransactionId(request.transactionId)) {
        return next(new Errors.ValidationError("transactionId is missing"));
    }
    if (!Validators.isExchangeRate(request.exchangeRate)) {
        return next(new Errors.ValidationError("exchangeRate is not valid"));
    }
    try {
        const result: WinConfirmResponse = await jpnService.confirmWin(req.auth, request);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

/**
 * Rollbacks jackpot win amount for player
 */
async function rollbackWin(req: JPNRequest, res: Response, next: NextFunction) {
    const request: WinConfirmRequest = req.body as WinConfirmRequest;
    try {
        const result = await jpnService.rollbackWin(req.auth, request);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

async function winJackpot(req: JPNRequest, res: Response, next: NextFunction) {
    const request: WinJackpotRequest = req.body;
    const validationError = validateBaseRequest(request);
    if (validationError) {
        return next(validationError);
    }
    if (!Validators.isJackpotId(req.auth, request.jackpotId)) {
        return next(new Errors.NotAuthorizedJackpotError(request.jackpotId));
    }
    if (request.amount && !Validators.isAmount(req.auth, request.amount)) {
        return next(new Errors.ValidationError("amount is not valid"));
    }
    try {
        log.debug({ "reqData": requestLogData(req) }, "Http Request");
        const result = await jpnService.winJackpot(req.auth, request);
        res.json(result);
    } catch (err) {
        next(err);
    }
}

async function getTransferTransactionId(req: JPNRequest, res: Response, next: NextFunction) {
    try {
        const trxId: TransactionIdResponse = await jpnService.getTransferTransactionId();
        res.json(trxId);
    } catch (err) {
        next(err);
    }
}

async function transferProgressive(req: JPNRequest, res: Response, next: NextFunction) {
    const request: TransferPoolRequest = req.body;
    if (!Validators.isTransactionId(request.transactionId)) {
        return next(new Errors.ValidationError("transactionId is not valid"));
    }
    if (!request.fromPoolId) {
        return next(new Errors.ValidationError("fromPoolId is missing"));
    }
    if (!request.toPoolId) {
        return next(new Errors.ValidationError("toPoolId is missing"));
    }
    if (request.amount && typeof request.amount !== "number") {
        return next(new Errors.ValidationError("amount is not valid"));
    }
    try {
        await jpnService.transferProgressive(req.auth, request);
        res.status(201).json({});
        next();
    } catch (error) {
        if (error instanceof Errors.DuplicateTransactionError) {
            res.status(200).json({});
            return next();
        } else {
            return next(error);
        }
    }
}

async function getPoolState(req: JPNRequest, res: Response, next: NextFunction) {
    const poolId = req.params.id;
    if (!poolId) {
        return next(new Errors.ValidationError("id is missing"));
    }
    try {
        const result = await jpnService.getPoolState(req.auth, poolId);
        res.json(result);
        next();
    } catch (error) {
        return next(error);
    }
}

async function getAllPoolsState(req: JPNRequest, res: Response, next: NextFunction) {
    try {
        const result = await jpnService.getAllPoolsState(req.auth);
        res.json(result);
        next();
    } catch (error) {
        return next(error);
    }
}

async function poolDeposit(req: JPNRequest, res: Response, next: NextFunction) {
    const poolId = req.params.id;
    const request: PoolDepositRequest = req.body;
    if (!Validators.isTransactionId(request.transactionId)) {
        return next(new Errors.ValidationError("transactionId is not valid"));
    }
    if (request.seed && typeof request.seed !== "number") {
        return next(new Errors.ValidationError("seed is not valid"));
    }
    if (request.progressive && typeof request.progressive !== "number") {
        return next(new Errors.ValidationError("seed is not valid"));
    }
    try {
        await jpnService.poolDeposit(req.auth, poolId, req.body);
        res.status(201).json({});
        next();
    } catch (error) {
        if (error instanceof Errors.DuplicateTransactionError) {
            res.status(200).json({});
            return next();
        } else {
            return next(error);
        }
    }
}

async function updatePoolState(req: JPNRequest, res: Response, next: NextFunction) {
    const poolId = req.params.id;
    const request: UpdatePoolRequest = req.body;
    if (request.seed && typeof request.seed !== "number") {
        return next(new Errors.ValidationError("seed is not valid"));
    }
    if (request.progressive && typeof request.progressive !== "number") {
        return next(new Errors.ValidationError("seed is not valid"));
    }
    try {
        await jpnService.updatePool(req.auth, poolId, req.body);
        res.status(201).json({});
        next();
    } catch (error) {
        if (error instanceof Errors.DuplicateTransactionError) {
            res.status(200).json({});
            return next();
        } else {
            return next(error);
        }
    }
}

async function deferredContribute(req: JPNRequest, res: Response, next: NextFunction) {
    const request = req.body as DeferredContributionRequest;

    if (!Validators.isExchangeRate(request.exchangeRate)) {
        return next(new Errors.ValidationError("exchangeRate is not valid"));
    }
    if (!Validators.isRoundId(request.roundId)) {
        return next(new Errors.ValidationError("roundId is not valid"));
    }

    if (!Validators.isAmount(req.auth, request.amount)) {
        return next(new Errors.ValidationError("amount is not valid"));
    }
    if (request.jackpotIds && !Validators.isJackpotIds(req.auth, request.jackpotIds, true)) {
        return next(new Errors.NotAuthorizedJackpotError(JSON.stringify(request.jackpotIds)));
    }
    try {
        log.debug({ "reqData": requestLogData(req) }, "Http Request");
        const result = await jpnService.deferredContribute(req.auth, request);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

router.post("/auth", auth);
router.get("/ticker", authenticate, validateExchangeRates, getTicker);
router.get("/contribute/transactionId", authenticate, generateContributionTrxId);
router.post("/contribute", authenticate, validateExchangeRates, contribute);
router.post("/checkWin", authenticate, validateExchangeRates, checkWin);
router.post("/minigame", authenticate, validateExchangeRates, updateMiniGame);
router.post("/win", authenticate, validateExchangeRates, winJackpot);
router.post("/win/confirm", authenticate, validateExchangeRates, confirmWin);
router.post("/win/rollback", authenticate, validateExchangeRates, rollbackWin);
router.post("/contribute/deferred", authenticate, deferredContribute);

router.get("/pools/transfer/transactionId", authenticateTransfer, getTransferTransactionId);
router.post("/pools/transfer/progressive", authenticateTransfer, transferProgressive);
router.get("/pools/:id", authenticateTransfer, getPoolState);
router.patch("/pools/:id", authenticateTransfer, updatePoolState);
router.get("/pools", authenticateTransfer, getAllPoolsState);
router.post("/pools/:id/deposit", authenticateTransfer, poolDeposit);

export default router;
