import { NextFunction, Request, Response, Router } from "express";
import { verifyInternalToken } from "../utils/security";
import JackpotInstanceService from "../services/jackpot.instance";
import wallet from "../services/wallet.service";
import { RemoteGameFlowRequest, RemoteJackpotService } from "../services/remoteJackpotService";
import * as Errors from "../errors";
import logger from "../utils/logger";
import { Validators } from "./jpn.router";

const log = logger("routes:remote");

const jpnService: RemoteJackpotService = new RemoteJackpotService(JackpotInstanceService, wallet);

async function authenticate(req: Request, res: Response, next: NextFunction) {
    const token: string = req.get("X-ACCESS-TOKEN");
    if (!token) {
        return next(new Errors.TokenIsMissing());
    }
    try {
        await verifyInternalToken(token);
        next();
    } catch (err) {
        next(err);
    }
}

async function findOrCreateJackpot(req: Request, res: Response, next: NextFunction) {
    try {
        const result = await jpnService.findOrCreateJackpot(req.body, req.query.autoCreate === "true");
        res.json(result);
    } catch (err) {
        next(err);
    }
}

async function generateTransactionIds(req: Request, res: Response, next: NextFunction) {
    const count = +req.query.count;
    if (!count || count <= 0 || typeof count !== "number" || Math.floor(count) !== count) {
        return next(new Errors.ValidationError("count is not valid"));
    }
    try {
        const result = await jpnService.generateTrxIds(count);
        res.json(result);
    } catch (err) {
        next(err);
    }
}

async function processRemoteGameFlow(req: Request, res: Response, next: NextFunction) {
    const request: RemoteGameFlowRequest = req.body;
    if (!Validators.isTransactionId(request.transactionId)) {
        return next(new Errors.ValidationError("transactionId is not valid"));
    }
    if (!request.playerInfo) {
        return next(new Errors.ValidationError("player info is empty"));
    }
    if (!request.request) {
        return next(new Errors.ValidationError("base request is empty"));
    }
    if (!request.results || request.results.length === 0) {
        return next(new Errors.ValidationError("Nothing to process"));
    }
    try {
        const result = await jpnService.processRemoteGameFlow(request);
        res.json(result);
    } catch (err) {
        next(err);
    }
}
async function getTicker(req: Request, res: Response, next: NextFunction) {
    try {
        const result = await jpnService.getTicker(req.query.jackpotId as string);
        res.json(result);
    } catch (err) {
        next(err);
    }
}

const router: Router = Router();

router.post("/remote/findOrCreateJackpot", authenticate, findOrCreateJackpot);
router.get("/remote/transactionId", authenticate, generateTransactionIds);
router.post("/remote/gameFlow", authenticate, processRemoteGameFlow);
router.get("/remote/ticker", authenticate, getTicker);

export default router;
