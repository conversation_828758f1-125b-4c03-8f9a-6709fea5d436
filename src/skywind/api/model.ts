import { JackpotDefinition } from "@skywind-group/sw-jpn-core";
import { SecureContextOptions } from "tls";

export interface JackpotType {
    name: string;
    baseType: string;
    jpGameId: string;
    definition: JackpotDefinition;
    configurable?: boolean;
    overridable?: boolean;
    canBeDisabled?: boolean;
    supportsWinCap?: boolean;
}

export enum JackpotDisableMode {
    IMMEDIATE = 0, NEXT_WIN = 1
}

export const DISABLE_MODES = [JackpotDisableMode.IMMEDIATE, JackpotDisableMode.NEXT_WIN];

export enum JackpotConfigurationLevel {
    SPECIFIC_BRAND_ONLY = 1,
    SHARED_BETWEEN_ONE_OPERATOR_FOR_ONE_JURISDICTION = 2,
    SHARED_BETWEEN_SEVERAL_OPERATORS_FOR_ONE_JURISDICTION = 3,
    SHARED_BETWEEN_SEVERAL_JURISDICTIONS_FOR_ONE_OPERATOR = 4,
    SHARED_BETWEEN_SEVERAL_OPERATORS = 5,
    SHARED_GLOBALLY = 6
}

export interface JackpotInstanceInfo {
    externalId?: string;
    externalStartDate?: string;
    [key: string]: any;
}

export interface JackpotInstance {
    id: string;
    type: string;
    baseType?: string;
    jpGameId?: string;
    definition?: JackpotDefinition;
    regionCode?: string;
    isTest?: boolean;
    disableMode?: JackpotDisableMode;
    isDisabled?: boolean;
    isGlobal?: boolean;
    isOwned?: boolean;
    isLocal?: boolean;
    createdAt?: Date;
    jackpotConfigurationLevel?: JackpotConfigurationLevel;
    entityId?: number;
    jurisdictionCode?: string;
    info?: JackpotInstanceInfo;
}

export interface JackpotInternalInstance extends JackpotInstance {
    internalId: number;
    region?: JackpotRegion;
    precision: number;
    migratedAt?: Date;
    createdFromId?: string;
}

export interface JackpotRegion {
    code: string;
    url: string;
    secureOptions?: SecureContextOptions;
}

export interface AuditInfo {
    initiatorType: JackpotAuditInitiator;
    initiatorName?: string;
    ip?: string;
    userAgent?: string;
    id?: string;
}

export enum JackpotAuditType {
    CREATE = "create",
    CLONE = "clone",
    UPDATE = "update",
    TYPE_UPDATE = "type-update",
    ARCHIVE = "archive",
    ENABLE = "enable",
    DISABLE = "disable",
    GAME_ACTION = "game-action",
    DISABLE_ON_WIN = "disable-on-win",
    APPLY_PENDING = "apply-pending",
}

export enum JackpotAuditInitiator {
    USER = "user",
    SYSTEM = "system"
}

export interface JackpotAudit extends AuditInfo {
    jackpotId: string;
    type: JackpotAuditType;
    history: any;
    ts: Date;
}

export type JackpotAuditCreateData = Omit<JackpotAudit, "id">;
