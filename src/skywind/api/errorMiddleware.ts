import { NextFunction, Request, Response } from "express";
import { requestLogData } from "../utils/utils";
import {
    RequiredFieldError as GameRequiredFieldError,
    ValidationError as GameValidationError,
    WinJackpotNotSupportedError,
    WinMiniGameNotSupportedError,
    WinWithoutContributeError
} from "@skywind-group/sw-jpn-games";
import {
    InternalServerError,
    isSWError,
    MalformedJsonError,
    RequiredFieldError,
    SWError,
    ValidationError,
    WinJackpotNotSupported,
    WinMiniGameNotSupported,
    WinWithoutContribution
} from "../errors";
import { measures } from "@skywind-group/sw-utils";

export function createErrorHandler(logger): (err: any, req: Request, res: Response, next: NextFunction) => any {
    return (err: any, req: Request, res: Response, next: NextFunction): any => {

        const reqData = requestLogData(req);

        measures.measureProvider.saveError(err);

        if (res.headersSent) {
            logger.warn(err, "HTTP headers have already been sent", reqData);
            return next(err);
        }

        if (err) {
            let error: SWError;

            if (isSWError(err)) {
                logger.warn(err, "SWError", reqData);
                error = (err as SWError);
            } else if (err instanceof SyntaxError) {
                const syntaxErrorReason = err.message ? err.message : "N/A";
                logger.debug(err, "Malformed json", syntaxErrorReason);
                error = new MalformedJsonError(syntaxErrorReason);
            } else if (err instanceof WinJackpotNotSupportedError) {
                logger.debug(err, "WinJackpotNotSupportedError", err);
                error = new WinJackpotNotSupported();
            } else if (err instanceof WinWithoutContributeError) {
                logger.debug(err, "WinWithoutContributeError", err);
                error = new WinWithoutContribution();
            } else if (err instanceof WinMiniGameNotSupportedError) {
                logger.debug(err, "WinMiniGameNotSupportedError", err);
                error = new WinMiniGameNotSupported();
            } else if (err instanceof GameRequiredFieldError) {
                logger.debug(err, "Required field error", err);
                error = new RequiredFieldError(err.message);
            } else if (err instanceof GameValidationError) {
                logger.debug(err, "Validation error", err);
                error = new ValidationError(err.message);
            } else {
                logger.error(err, "Internal error", reqData);
                error = new InternalServerError();
            }

            res.status(error.status).json({
                code: error.code,
                message: error.message,
            });
        }
    };
}
