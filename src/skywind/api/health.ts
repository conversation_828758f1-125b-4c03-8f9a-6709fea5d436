import * as express from "express";
import { getEnvironmentInfo } from "@skywind-group/sw-utils";
import config from "../config";
const router: express.Router = express.Router();

router.get("/health/", (req: express.Request, res: express.Response, next: express.NextFunction) => {
    res.send({
       serverName: config.server.getName(),
        ...getEnvironmentInfo()
    });
});

export default router;
