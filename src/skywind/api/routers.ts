import { Application, static as staticContent } from "express";
import * as path from "path";
import VersionRouter from "./version";
import HealthRouter from "./health";
import InternalRouter from "./internal.router";
import JPNRouter from "./jpn.router";
import ModelRouter from "./model.router";
import { createErrorHand<PERSON> } from "./errorMiddleware";
import logger from "../utils/logger";
import * as bodyParser from "body-parser";
import { apiSwagger, swaggerCspMiddleware } from "../utils/swagger";
import { setAuthContext } from "./middleware";
import config from "../config";

const log = logger("routers");

const swaggerTools = require("swagger-tools");
const swaggerUiConf = {
    swaggerUiDir: "node_modules/swagger-ui-dist",
    swaggerUi: "/docs",
};

export function defineRoutes(app: Application): Application {

    // Configure Express middleware.
    app.use(bodyParser.json());
    app.use(setAuthContext);

    // Configure API endpoints.
    // TODO remove v1 after release
    // Jackpot game api
    app.use("/api/v2/jpn", JPNRouter);
    app.use("/api/v2/jpn", InternalRouter);

    // Global
    app.use("/v1", VersionRouter);
    app.use("/v1", HealthRouter);

    swaggerTools.initializeMiddleware(apiSwagger.get(), (middleware) => {
        app.use(middleware.swaggerMetadata());
        app.use("/", middleware.swaggerUi(swaggerUiConf));
        // Jackpot model management api
        app.use("/api/v2/jpn", ModelRouter);
        app.use(createErrorHandler(log));
    });

    if (!config.disableSwagger) {
        app.use("/docs", swaggerCspMiddleware);
        app.use("/swagger-resources", swaggerCspMiddleware);
        app.use("/api-docs", swaggerCspMiddleware);

        app.use("/docs", staticContent(path.resolve(process.cwd(), "swagger-ui")));
        app.use("/swagger-resources", staticContent(path.resolve(process.cwd(), "swagger-resources")));
        app.route("/api-docs").get((req, resp) => resp.send(apiSwagger.get()));
    }
    app.use(createErrorHandler(log));

    return app;
}
