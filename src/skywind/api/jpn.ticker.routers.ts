import { Application, static as staticContent } from "express";
import VersionRouter from "./version";
import HealthRouter from "./health";
import TickerRouter from "./ticker";
import { createErrorHandler } from "./errorMiddleware";
import logger from "../utils/logger";
import { apiSwaggerTicker, swaggerCspMiddleware } from "../utils/swagger";
import * as path from "path";
import config from "../config";

const swaggerTools = require("swagger-tools");
const swaggerUiConf = {
    swaggerUiDir: "node_modules/swagger-ui-dist",
    swaggerUi: "/docs",
};

const log = logger("jpn-ticker-routers");

export function defineRoutes(app: Application): Application {

    app.use("/v1", TickerRouter);

    app.use("/v1", VersionRouter);
    app.use("/v1", HealthRouter);
    app.use(createErrorHandler(log));

    if (!config.disableSwagger) {
        app.use("/docs", swaggerCspMiddleware);
        app.use("/swagger-resources", swaggerCspMiddleware);
        app.use("/api-docs", swaggerCspMiddleware);

        swaggerTools.initializeMiddleware(apiSwaggerTicker.get(), (middleware) => {
            app.use(middleware.swaggerMetadata());
            app.use("/", middleware.swaggerUi(swaggerUiConf));
        });
        app.use("/docs", staticContent(path.resolve(process.cwd(), "swagger-ui")));
        app.use("/swagger-resources", staticContent(path.resolve(process.cwd(), "swagger-resources")));
        app.route("/api-docs").get((req, resp) => resp.send(apiSwaggerTicker.get()));
    }
    app.route("/").get((req, res, next) => {
        res.sendStatus(200);
    });
    return app;
}
