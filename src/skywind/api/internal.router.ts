import {
    Router,
    Response,
    NextFunction,
} from "express";
import { JPNRequest } from "./jpn.router";
import { currencyService } from "../services/currency.service";
import { CurrencyNotFoundError } from "../errors";
import { CurrencyNotFoundError as BadCurrencyCodeError } from "@skywind-group/sw-currency-exchange";

const router: Router = Router();

async function getExchangeRatesList(req: JPNRequest, res: Response, next: NextFunction) {
    try {
        const rates = currencyService.getExchangeRatesList();
        res.json(rates);
    } catch (err) {
        next(err);
    }
}

function getUnitaryExchangeRate(req: JPNRequest, res: Response, next: NextFunction) {
    const baseCurrency = req.params.base_currency;
    const targetCurrency = req.params.target_currency;
    try {
        const rate = currencyService.getExchangeRate(baseCurrency, targetCurrency);
        res.json({ rate: rate });
    } catch (err) {
        if (err instanceof BadCurrencyCodeError) {
            next(new CurrencyNotFoundError(err.currency));
        } else {
            next(err);
        }
    }
}

router.get("/exchange", getExchangeRatesList);
router.get("/exchange/:base_currency/:target_currency", getUnitaryExchangeRate);

export default router;
