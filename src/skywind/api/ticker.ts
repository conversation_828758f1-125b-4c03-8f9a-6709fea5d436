import { Request, Response, NextFunction, Router } from "express";
import { ValidationError } from "../errors";
import { JackpotService } from "../..";
import JackpotInstanceService from "../services/jackpot.instance";
import wallet from "../services/wallet.service";
import JackpotContextService from "../services/jackpot.context";
import { TickerOptions } from "../services/jackpotGameFlow";
import config from "../config";
import { TickerService } from "../services/tickerService";
import { Currencies } from "@skywind-group/sw-currency-exchange";

const router: Router = Router();

const jpnService: JackpotService = new JackpotService(
    JackpotInstanceService, wallet, JackpotContextService);
const tickerService = new TickerService(JackpotInstanceService, jpnService);

interface TickerRequestData {
    jackpotIds: string[];
    currency: string;
}

const whitelistedOrigins: string[] = config.corsWhitelist.game !== undefined ?
                                     config.corsWhitelist.game.split(",").map(item => item.trim()) : undefined;

function setHeaders(res: Response) {
    res.header("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    res.header("Access-Control-Allow-Headers", "User-Agent,Content-Type,Access-Control-Allow-Origin");
}

function isAllowedOrigin(req: Request) {
    if (whitelistedOrigins === undefined || whitelistedOrigins[0] === "*") {
        return true;
    }
    for (const origin of whitelistedOrigins) {
        if (origin === req.header("origin")) {
            return true;
        }
    }
    return false;
}

const cleanQueryParams = (query: {
    exchangeRate?: string,
    jackpotIds: string,
    currency: string,
    skipError: string }): [ TickerRequestData, boolean ] => {

    const errorMessages = [];
    if (!query.jackpotIds) {
        errorMessages.push("jackpotIds should be a non-empty comma-separated array of jp instances");
    }

    if (query.currency) {
        try {
            Currencies.verifyExists(query.currency);
        } catch (err) {
            errorMessages.push(`Currency ${query.currency} not found`);
        }
    }

    if (errorMessages.length) {
        throw new ValidationError(errorMessages);
    }

    const tickerRequest = {
        jackpotIds: query.jackpotIds.split(",").map(instanceId => instanceId.trim()),
        currency: query.currency
    };

    const skipError = query.skipError === "true" ;

    return [ tickerRequest, skipError ];

};

router.get("/ticker/", async (req: Request, res: Response, next: NextFunction) => {
    try {
        res.setHeader("Cache-Control", "no-transform");
        if (isAllowedOrigin(req)) {
            res.header("Access-Control-Allow-Origin", req.header("origin"));
        }
        const [ tickerRequest, skipError ] = cleanQueryParams(req.query as any);
        const opts: TickerOptions = {
            withPots: false,
            withVirtualSeed: true,
            skipError
        };
        res.send(await tickerService.getTicker(tickerRequest.jackpotIds, tickerRequest.currency, opts));
    } catch (err) {
        next(err);
    }

});

router.options("/ticker/*", (req: Request, res: Response, next: NextFunction) => {
    setHeaders(res);
    if (isAllowedOrigin(req)) {
        res.header("Access-Control-Allow-Origin", req.header("origin"));
    }
    res.sendStatus(200);
});

export default router;
