import http = require("http");
import * as express from "express";
import * as application from "./express";
import { setUpMetricHandlers } from "./express";

import { createErrorHandler } from "./api/errorMiddleware";

import logger from "./utils/logger";
import config from "./config";

const log = logger("sw-jpn-server-api-internal");

export async function startServer(): Promise<http.Server> {

    const app: express.Application = application.create(setUpMetricHandlers);
    const internalServer: http.Server = require("http").createServer(app);

    app.use(createErrorHandler(log));
    return new Promise<http.Server>((resolve, reject) => {
        const port = config.internalServer.port;
        internalServer.listen(port, null, () => {
            resolve(internalServer);
            log.info("JPN Internal API listening on: " + port);
        }).on("error", (error) => reject(error));
    });
}
