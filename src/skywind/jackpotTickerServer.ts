import { lazy } from "@skywind-group/sw-utils";
import { create } from "./express";
import { startApplicationServer } from "./server";
import { Server } from "http";
import { defineRoutes } from "./api/jpn.ticker.routers";
import config from "./config";

export const application = lazy(() => defineRoutes(create()));

export async function startServer(port = config.tickerServer.port): Promise<Server> {
    return startApplicationServer(application.get(), "JPN_TICKER", port);
}
