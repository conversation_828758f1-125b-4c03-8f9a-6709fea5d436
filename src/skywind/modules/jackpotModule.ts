import {
    Contribution, JackpotResult, Ticker,
    PlayerInformation, PoolDepositRequest, PoolState, UpdatePoolRequest, JackpotOperationStatus
} from "@skywind-group/sw-jpn-core";
import { JackpotWin } from "../../definition";
import { JackpotInternalInstance } from "../api/model";
import { JackpotGameFlow } from "../services/jackpotGameFlow";
import {
    ContributionPayout,
    LocalWalletContributionParams,
    LocalWalletWinParams,
    WalletWinPayout
} from "./walletParams";
import { JpWalletReleaseWinResult } from "./jackpotWallet";

export interface JackpotGameFlowResult {
    contributions?: ContributionPayout[];
    playerContributions?: Contribution[];
    gameResult?: JackpotResult;
    wins?: JackpotWin[];
    winPayouts?: WalletWinPayout[];
    contributionParams?: LocalWalletContributionParams;
    winParams?: LocalWalletWinParams;
}

/**
 * Implementation of jackpot wallet operations performed in scope of single player request.
 */
export interface JackpotModule {

    /**
     * player details
     */
    playerInfo: PlayerInformation;

    /**
     * jackpot instance
     */
    instance: JackpotInternalInstance;

    /**
     * Current exchange rate from player currency to jackpot currency
     */
    exchangeRate: number;

    /**
     * Get jackpot pools information
     */
    getTicker(forceRefresh?: boolean): Promise<Ticker>;

    /**
     * Process jackpot game flow results - contribute to jackpot and release win in necessary
     */
    processGameFlow(flow: JackpotGameFlow): Promise<JackpotGameFlowResult>;

    /**
     * Resolve JP Win by marking it either as "resolved" or "rejected"
     */
    resolveWin?(
        flow: JackpotGameFlow,
        winResolution: JackpotOperationStatus.RESOLVED | JackpotOperationStatus.REJECTED
    ): Promise<[JackpotResult, JpWalletReleaseWinResult]>;

    /**
     * Move fixed or all amount of progressive money from one pool to another within one jackpot.
     * If amount not present will charge all money from donor pool.
     *
     * @param {string} fromPoolId donor pool Id
     * @param {string} toPoolId recipient pool Id
     * @param {string} transactionId transactionId for this operation
     * @param {number} amount amount to transfer (in jackpot currency)
     *
     * @throws ValidationError, JackpotNotInitialized, DuplicateTransactionError, InsufficientJackpotBalance
     * @returns {Promise<void>}
     */
    transferProgressive(fromPoolId: string, toPoolId: string, transactionId: string, amount?: number): Promise<void>;

    /**
     * Increment amount of progressive and seed for the jackpot pool.
     */
    poolDeposit(poolId: string, request: PoolDepositRequest): Promise<void>;

    /**
     * Updates amount of progressive and seed for the jackpot pool
     */
    updatePool(poolId: string, transactionId: string, request: UpdatePoolRequest): Promise<void>;

    /**
     * Current state of jackpot pool
     */
    getPoolState(poolId: string): Promise<PoolState>;

    /**
     * Current state of all jackpot pools
     */
    getAllPoolsState(): Promise<PoolState[]>;
}
