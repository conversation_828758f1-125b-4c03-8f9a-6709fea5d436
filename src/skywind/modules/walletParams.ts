import { BaseRequest, JackpotResult, JackpotOperationStatus } from "@skywind-group/sw-jpn-core";
import { Contribution } from "@skywind-group/sw-jpn-core/src/index";
import { JackpotWin } from "../../definition";

export const META_INF_ACCOUNT = "$$metaInf";
export const META_INF_SEQ_ID = "seqId";
export const META_INF_DISABLED = "disabled";

export enum LOCAL_WALLET_OPERATION_NAME {
    contribute = "contribute",
    contributeConfirmation = "contributeConfirmation",
    contributeRollback = "contributeRollback",
    release = "release",
    releaseConfirmation = "releaseConfirmation",
    releaseRollback = "releaseRollback",
    internalTransfer = "internalTransfer"
}

export interface BaseWalletParams {
    brandId: number;
    region: string;
    gameId: string;
    gameCode: string;
    playerCode: string;
    playerCurrency: string;
    roundId: string;
    fromRemote?: boolean;
    remoteTrxId?: string;
    remoteTrxRegion?: string;
    nickname?: string;
}

export interface BaseWalletContributionParams extends BaseWalletParams {
    contributionAmount: number;
    currencyRate: number;
    gameData: BaseRequest;
    initialSeed: { [pool: string]: number };    // in major units
    contributions?: ContributionPayout[];
}

export interface LocalWalletContributionParams extends BaseWalletContributionParams {
    precision: number;
    gameResult: JackpotResult;
    playerContributions: Contribution[];
}

export interface BaseWalletWinParams extends BaseWalletParams {
    wins: WalletWinPayout[];
    betAmount?: number; // player bet amount which triggered win
}

export interface LocalWalletWinParams extends BaseWalletWinParams {
    precision: number;
}

export interface LocalWalletTransferParams extends BaseWalletParams {
    precision: number;
    transfers: InternalTransfer[];
}

export interface WalletWinPayout {
    pool: string;
    initialSeed: number;
    seed: number;
    progressive: number;
    totalSeed: number;
    totalProgressive: number;
    seedSinceLastWin: number;
    progressiveSinceLastWin: number;
    winAmount?: number;                 // player win amount
    currencyRate?: number;              // exchange rate to from player currency to jackpot
    transferPool?: string;              // win transfer to another pool or to void if null
    transferPoolSeed?: number;          // seed amount of transfer pool before transfer
    transferPoolProgressive?: number;   // progressive amount of transfer pool before transfer
    title?: string;                     // friendly pool name
    status?: JackpotOperationStatus;    // JP win status, can be either "pending", "resolved" or "rejected"
    info?: any;
}

export interface InternalTransfer {
    pool: string;
    seed: number;
    progressive: number;
    totalSeed: number;
    totalProgressive: number;
    transferPool: string;               // win transfer to another pool or to void if null
    transferPoolSeed?: number;          // seed amount of transfer pool before transfer
    transferPoolProgressive?: number;   // progressive amount of transfer pool before transfer
}

export interface ContributionPayout extends Contribution {
    totalSeed: number;
    totalProgressive: number;
    status?: JackpotOperationStatus;    // JP contribution status, can be either "pending", "resolved" or "rejected"
}

export enum REMOTE_WALLET_OPERATION_NAME {
    contribute = "remote-contribute",
    release = "remote-release"
}

export interface RemoteWalletContributionParams extends BaseWalletContributionParams {
    playerContributions?: Contribution[];
    gameResult?: JackpotResult;
    winResults?: JackpotWin[];
}

export interface RemoteWalletWinParams extends BaseWalletWinParams {
    winResults: JackpotWin[];
}

export function convertContributionsToContributionsPayout(contributions: Contribution[]): ContributionPayout[] {
    if (Array.isArray(contributions)) {
        return contributions.map(item => {
            return { ...item, totalSeed: 0, totalProgressive: 0 };
        });
    }
}

export const WIN_TRX_OPERATION_ID_MARKER = 1000;
export const CONTRIBUTION_RESOLVE_TRX_OPERATION_ID_MARKER = 10000;
export const WIN_RESOLVE_TRX_OPERATION_ID_MARKER = 100000;
export const MINI_GAME_TRX_OPERATION_ID_MARKER = 1000000;
export const TRANSFER_TRX_OPERATION_ID_MARKER = 1000000000;
