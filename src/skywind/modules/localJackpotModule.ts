import { JackpotGameFlowResult, JackpotModule } from "./jackpotModule";
import { JackpotWallet, JpWalletReleaseWinResult } from "./jackpotWallet";
import {
    PlayerInformation, PoolDepositRequest, PoolState, JackpotGameResult,
    Ticker, Contribution, JackpotResult, ContributionRequest, UpdatePoolRequest, JackpotOperationStatus
} from "@skywind-group/sw-jpn-core";
import { JackpotInternalInstance } from "../api/model";
import { JackpotGameFlow } from "../services/jackpotGameFlow";
import * as Errors from "../errors";
import config from "../config";
import { JackpotWin } from "../../definition";
import logger from "../utils/logger";
import {
    ContributionPayout,
    LocalWalletContributionParams,
    LocalWalletWinParams,
    WalletWinPayout
} from "./walletParams";

const log = logger("jackpot:module:local");

export class LocalJackpotModule implements JackpotModule {

    public playerInfo: PlayerInformation;
    public instance: JackpotInternalInstance;
    public exchangeRate: number;

    constructor(private jpWallet: JackpotWallet) {
        this.playerInfo = {
            playerCode: jpWallet.baseParams.playerCode,
            brandId: jpWallet.baseParams.brandId,
            currency: jpWallet.baseParams.playerCurrency,
            gameCode: jpWallet.baseParams.gameCode,
            region: jpWallet.baseParams.region
        };
        this.instance = jpWallet.instance;
        this.exchangeRate = jpWallet.exchangeRate;
    }

    public getTicker(forceRefresh?: boolean): Promise<Ticker> {
        return this.jpWallet.getTicker(forceRefresh);
    }

    public async processGameFlow(flow: JackpotGameFlow): Promise<JackpotGameFlowResult> {
        let gameResult: JackpotResult = flow.getGameResult();

        let contributions: ContributionPayout[];
        let playerContributions: Contribution[];
        let contributionParams: LocalWalletContributionParams;
        if (flow.hasContributions()) {
            const contributionResult = await this.jpWallet.contribute(
                flow.transactionId,
                flow.request as ContributionRequest,
                flow.getContributions(),
                flow.getPlayerContributions(),
                flow.getGameResult(),
                flow.request.deferredWinsEnabled
            );
            contributions = contributionResult.contributions;
            playerContributions = contributionResult.playerContributions;
            gameResult = contributionResult.gameResult;
            contributionParams = contributionResult.contributionParams;
        }

        await this.processPoolManipulations(flow);

        let wins: JackpotWin[];
        let winPayouts: WalletWinPayout[];
        let winParams: LocalWalletWinParams;

        const hasWonJackpot = (gameResult && gameResult[0] ? gameResult[0].type :
                               gameResult && (gameResult as JackpotGameResult).type) === "win";
        if (hasWonJackpot) {
            [gameResult, { wins, winPayouts, winParams }] = await this.releaseWin(flow);
        }

        return { contributions, playerContributions, gameResult, wins, winPayouts, contributionParams, winParams };
    }

    public async resolveWin(
        flow: JackpotGameFlow,
        resolution: JackpotOperationStatus.RESOLVED | JackpotOperationStatus.REJECTED
    ): Promise<[JackpotResult, JpWalletReleaseWinResult]> {
        const wins: JackpotWin[] = flow.getWins();

        log.info("Resolve deferred wins", flow.request, wins);

        try {
            const contributionParams = flow.getContributionParams();
            const winParams = flow.getWinParams();

            const result = await this.jpWallet.resolveWin(flow.transactionId, flow.request.externalId, winParams, resolution);
            await this.jpWallet.resolveContribution(flow.transactionId, flow.request.externalId, contributionParams, resolution);

            return [result.wins && flow.getGameResult(), result];
        } catch (err) {
            log.error(err, "Failed to resolve jackpot", flow.request, wins);
            return Promise.reject(err);
        }
    }

    private async releaseWin(flow: JackpotGameFlow,
                             retry?: number): Promise<[JackpotResult, JpWalletReleaseWinResult]> {

        let wins: JackpotWin[] = await flow.getWins();
        if (!wins || retry !== undefined) {
            await flow.refreshTicker();
            await flow.validateCheckWin();
            if (!flow.hasWonJackpot()) {
                log.info("Concurrent jackpot trigger for single-win jackpot. Rejected by jp game", flow.request);
                return [undefined, { winPayouts: undefined, wins: undefined, winParams: undefined }];
            }
            wins = await flow.calculateWins();
        }

        log.info("Release jackpot wins", flow.request, wins);

        try {
            const result = await this.jpWallet.releaseWin(
                flow.transactionId,
                flow.request.externalId,
                flow.request.roundId,
                wins,
                flow.getPlayerBetAmount(),
                flow.request.deferredWinsEnabled
            );
            return [result.wins && flow.getGameResult(), result];
        } catch (err) {

            // race conditions - someone won jackpot concurrently, need to retry win release process
            retry = retry !== undefined ? retry : config.maxWinRetry;
            if (err instanceof Errors.InsufficientJackpotBalance) {
                await flow.refreshTicker();
                await flow.validateCheckWin();
                if (!flow.hasWonJackpot()) {
                    log.info("Concurrent jackpot trigger for single-win jackpot. Rejected by jp game", flow.request);
                    return [undefined, { winPayouts: undefined, wins: undefined, winParams: undefined }];
                }

                if (retry > 0) {
                    log.warn("Concurrent jackpot win. Retry (%d) win release process...", retry, flow.request, wins);
                    return this.releaseWin(flow, retry - 1);
                } else {
                    log.error("Concurrent jackpot win after (%d) retries", retry, flow.request);
                    return Promise.reject(new Errors.ConcurrentJackpotWin());
                }
            }

            log.error(err, "Failed to release jackpot", flow.request, wins);

            return Promise.reject(err);
        }
    }

    public transferProgressive(fromPoolId: string, toPoolId: string,
                               transactionId: string, amount?: number): Promise<void> {
        return this.jpWallet.transferProgressive(fromPoolId, toPoolId, transactionId, amount);
    }

    public poolDeposit(poolId: string, request: PoolDepositRequest): Promise<void> {
        return this.jpWallet.poolDeposit(poolId, request);
    }

    public updatePool(poolId: string, transactionId: string, request: UpdatePoolRequest): Promise<void> {
        return this.jpWallet.updatePool(poolId, transactionId, request);
    }

    public getPoolState(poolId: string): Promise<PoolState> {
        return this.jpWallet.getPoolState(poolId);
    }

    public getAllPoolsState(): Promise<PoolState[]> {
        return this.jpWallet.getAllPoolsState();
    }

    private async processPoolManipulations(flow: JackpotGameFlow): Promise<void> {
        const manipulations = flow.getPoolManipulations();
        if (manipulations && manipulations.length) {
            log.info("Process pool manipulations", flow.request, manipulations);

            try {
                await this.jpWallet.processPoolManipulations(flow.transactionId,
                    flow.request.externalId,
                    flow.request.roundId,
                    manipulations);
            } catch (err) {
                if (err instanceof Errors.InsufficientJackpotBalance) {
                    log.info("Transfer skipped because somebody already did this transfer");
                    await flow.refreshTicker();
                } else {
                    return Promise.reject(err);
                }
            }
        }
    }
}
