import { JackpotGameFlowResult, JackpotModule } from "./jackpotModule";
import { PlayerInformation, PoolDepositRequest, PoolState, Ticker,
    Contribution, JackpotResult } from "@skywind-group/sw-jpn-core";
import { JackpotInternalInstance, JackpotRegion } from "../api/model";
import { JackpotGameFlow } from "../services/jackpotGameFlow";
import * as Errors from "../errors";
import {
    getWalletKey, JackpotLookup, JackpotPlayerWin, JackpotWin, JackpotWinTransfer, JackpotWinType,
    WalletService
} from "../../definition";
import {
    BAD_TRANSACTION_ID, IExternalTransaction,
    INSUFFICIENT_BALANCE, ITrxData,
    TRANSACTION_EXISTS, TRANSACTION_IS_PROCESSING
} from "@skywind-group/sw-wallet";
import { TrxIdPool } from "../utils/trxIdPool";
import { RemoteJackpotApiCache } from "../services/remoteJackpotApi";
import logger from "../utils/logger";
import {
    ContributionPayout,
    convertContributionsToContributionsPayout,
    LocalWalletContributionParams,
    LocalWalletWinParams,
    META_INF_ACCOUNT, MINI_GAME_TRX_OPERATION_ID_MARKER,
    REMOTE_WALLET_OPERATION_NAME,
    RemoteWalletContributionParams, RemoteWalletWinParams, WalletWinPayout, WIN_TRX_OPERATION_ID_MARKER
} from "./walletParams";
import {
    RemoteGameFlowRequest, RemoteGameFlowProcessResult,
} from "../services/remoteJackpotService";
import { RemoteTicker, RemoteTickerService } from "../services/remoteTicker.service";
import { getRedisPool, remoteTickerPool } from "../storage/redis";
import { RemoteRequestQueue, RemoteRequestScheduler } from "../services/remoteRequestQueue";
import config from "../config";

const log = logger("jackpot:module:remote");

const MIGRATION_TRX_RETRANSMISSION_ID = 0;
const MIGRATION_TRX_RETRANSMISSION = "migration";

export interface RemoteTrxIds {
    generateTrxId(region: JackpotRegion): Promise<string>;
}

class RemoteTrxIdsPool implements RemoteTrxIds {

    private pools: Map<string, TrxIdPool> = new Map();

    public async generateTrxId(region: JackpotRegion): Promise<string> {
        return this.doGenerateTrxId(region);
    }

    private async doGenerateTrxId(region: JackpotRegion, retried = false): Promise<string> {
        if (!this.pools.has(region.code)) {
            const remoteApi = RemoteJackpotApiCache.getRemoteJackpotApi(region);
            const pool = new TrxIdPool(remoteApi.generateTransactions.bind(remoteApi));
            this.pools.set(region.code, pool);
        }
        try {
            const trxId = await this.pools.get(region.code).getTransactionId();
            return trxId;
        } catch (err) {
            if (!retried) {
                log.error(err, "Failed to get remote transaction id from region %s. Recreate trx id pool and retry",
                    region.code);
                this.pools.delete(region.code);
                return this.doGenerateTrxId(region, true);
            }

            return Promise.reject(err);
        }
    }
}

export class RemoteServices {
    public trxIds: RemoteTrxIds;
    public tickerService: RemoteTicker;
    public requestQueue: RemoteRequestScheduler;
}

export function createRemoteServices(lookup: JackpotLookup): RemoteServices {
    const trxIds: RemoteTrxIds = new RemoteTrxIdsPool();
    const tickerService: RemoteTickerService = new RemoteTickerService(remoteTickerPool.get(), lookup);
    const requestQueue: RemoteRequestQueue = new RemoteRequestQueue(getRedisPool(), lookup, tickerService);

    return { trxIds, tickerService, requestQueue };
}

export class RemoteJackpotModule implements JackpotModule {

    private walletKey: string;

    constructor(public playerInfo: PlayerInformation,
                public instance: JackpotInternalInstance,
                public exchangeRate: number,
                private wallet: WalletService,
                private remoteServices: RemoteServices) {
        this.walletKey = getWalletKey(instance);
    }

    public getTicker(forceRefresh?: boolean): Promise<Ticker> {
        return this.remoteServices.tickerService.getTicker(this.instance.id);
    }

    public async processGameFlow(flow: JackpotGameFlow): Promise<JackpotGameFlowResult> {
        try {
            const migratedWin = await this.isMigratedWinTransaction(flow);
            if (migratedWin) {
                return await this.processMigratedTrxRetransmission(flow, undefined, migratedWin);
            }
            return await this.doProcessGameFlow(flow);
        } catch (err) {
            if (err instanceof Errors.MigratedJackpotWalletTrxError) {
                return await this.processMigratedTrxRetransmission(flow, err.trxData);
            }
            return Promise.reject(err);
        }
    }

    private async doProcessGameFlow(flow: JackpotGameFlow): Promise<JackpotGameFlowResult> {
        if (!flow.hasContributions() && !flow.hasWonJackpot()) {
            return {};
        }

        const [remoteTrxId, result] = await this.saveRemoteOperation(flow);

        const request: RemoteGameFlowRequest = {
            transactionId: remoteTrxId,
            playerInfo: this.playerInfo,
            requestRegion: config.region,
            request: flow.request,
            results: [{
                jackpotId: this.instance.id,
                contributions: result.contributions,
                playerContributions: result.playerContributions,
                gameResult: result.gameResult,
                wins: result.wins
            }]
        };

        if (!flow.hasWonJackpot()) {
            await this.remoteServices.requestQueue.schedule(request);
        } else {
            const response = await RemoteJackpotApiCache.getRemoteJackpotApi(this.instance.region)
                .processGameFlow(request);
            const remoteResult: RemoteGameFlowProcessResult = response.results[0];

            result.contributions = remoteResult.contributions;
            result.playerContributions = remoteResult.playerContributions;
            result.gameResult = remoteResult.gameResult;

            if (remoteResult.wins) {
                result.winPayouts = remoteResult.winPayouts;
                result.wins = await this.saveRemoteWin(flow, remoteTrxId, remoteResult.wins, remoteResult.winPayouts);
            }

            await this.remoteServices.tickerService.updateTicker(remoteResult.ticker);
        }

        return result;
    }

    public transferProgressive(fromPoolId: string, toPoolId: string,
                               transactionId: string, amount?: number): Promise<void> {
        return Promise.reject(new Errors.OperationNotSupported());
    }

    public poolDeposit(poolId: string, request: PoolDepositRequest): Promise<void> {
        return Promise.reject(new Errors.OperationNotSupported());
    }

    public updatePool(poolId: string, transactionId: string, request: PoolDepositRequest): Promise<void> {
        return Promise.reject(new Errors.OperationNotSupported());
    }

    public getPoolState(poolId: string): Promise<PoolState> {
        return Promise.reject(new Errors.OperationNotSupported());
    }

    public getAllPoolsState(): Promise<PoolState[]> {
        return Promise.reject(new Errors.OperationNotSupported());
    }

    private async saveRemoteOperation(flow: JackpotGameFlow): Promise<[ string, JackpotGameFlowResult ]> {
        const trxId: string = flow.request.transactionId;
        let remoteTrxId: string = await this.remoteServices.trxIds.generateTrxId(this.instance.region);

        const contributions: Contribution[] = flow.getContributions();
        let contributionsPayout: ContributionPayout[] = convertContributionsToContributionsPayout(contributions);
        if (Array.isArray(contributionsPayout)) {
            const ticker = await flow.getTicker();
            contributionsPayout.forEach(item => {
                item.totalProgressive = ticker.pools[item.pool].progressive || 0;
                item.totalSeed = ticker.pools[item.pool].seed || 0;
            });
        }
        let playerContributions: Contribution[] = flow.getPlayerContributions();
        let gameResult: JackpotResult = flow.getGameResult();
        let wins: JackpotWin[] = await flow.calculateWins();
        const initialSeed = this.instance.definition.list.reduce((result, pool) => {
            result[pool.id] = (pool.seed && pool.seed.amount) || 0;
            return result;
        }, {});
        const operationId: number = flow.hasWonJackpotFromMiniGame() ?
                                    this.instance.internalId + MINI_GAME_TRX_OPERATION_ID_MARKER :
                                    this.instance.internalId;

        const operationParams: RemoteWalletContributionParams = {
            remoteTrxId: remoteTrxId,
            remoteTrxRegion: this.instance.region.code,
            brandId: this.playerInfo.brandId,
            region: this.playerInfo.region,
            gameId: this.instance.jpGameId,
            playerCode: this.playerInfo.playerCode,
            playerCurrency: this.playerInfo.currency,
            gameCode: this.playerInfo.gameCode,
            roundId: flow.request.roundId,
            contributionAmount: flow.request.amount,
            currencyRate: this.exchangeRate,
            gameData: flow.request,
            initialSeed,
            contributions: contributionsPayout,
            playerContributions,
            gameResult,
            winResults: wins
        };

        if (this.playerInfo.nickname) {
            operationParams.nickname = this.playerInfo.nickname;
        }

        const extTransaction: IExternalTransaction = {
            trxId,
            operation: {
                operationId: operationId,
                operationName: REMOTE_WALLET_OPERATION_NAME.contribute,
                externalTrxId: flow.request.externalId,
                gameId: flow.request.roundId,
                params: operationParams
            },
            changes: [{
                account: META_INF_ACCOUNT,
                property: "remoteCall",
                amount: 1,
                trxType: "remote",
                walletKey: this.walletKey
            }],
        };

        try {
            await this.wallet.saveExternalTransaction(extTransaction);
        } catch (err) {
            if (err === TRANSACTION_EXISTS) {
                const trxData = await this.wallet.findCommittedTransaction(trxId, operationId);

                if (trxData.operation.operationName !== REMOTE_WALLET_OPERATION_NAME.contribute) {
                    return Promise.reject(new Errors.MigratedJackpotWalletTrxError(trxData));
                }
                log.warn("Transaction exists, using stored data");

                const params: RemoteWalletContributionParams = trxData.operation.params;

                remoteTrxId = params.remoteTrxId;
                contributionsPayout = params.contributions;
                playerContributions = params.playerContributions;
                gameResult = params.gameResult;
                wins = params.winResults;
            } else if (err === INSUFFICIENT_BALANCE) {
                log.warn("Transaction commit failed - insufficient balance");
                return Promise.reject(new Errors.InsufficientJackpotBalance());
            } else if (err === BAD_TRANSACTION_ID) {
                return Promise.reject(new Errors.ValidationError("invalid transactionId"));
            } else if (err === TRANSACTION_IS_PROCESSING) {
                return Promise.reject(new Errors.TransactionIsProcessing());
            } else {
                return Promise.reject(err);
            }
        }

        return [remoteTrxId, { contributions: contributionsPayout, playerContributions, gameResult, wins }];
    }

    private async saveRemoteWin(flow: JackpotGameFlow,
                                remoteTrxId: string,
                                winResults: JackpotWin[],
                                wins: WalletWinPayout[]): Promise<JackpotWin[]> {
        const trxId: string = flow.request.transactionId;

        const winParams: RemoteWalletWinParams = {
            remoteTrxId: remoteTrxId,
            remoteTrxRegion: this.instance.region.code,
            brandId: this.playerInfo.brandId,
            region: this.playerInfo.region,
            gameId: this.instance.jpGameId,
            playerCode: this.playerInfo.playerCode,
            playerCurrency: this.playerInfo.currency,
            gameCode: this.playerInfo.gameCode,
            roundId: flow.request.roundId,
            winResults: winResults,
            wins: wins,
            betAmount: flow.getPlayerBetAmount(),
        };

        if (this.playerInfo.nickname) {
            winParams.nickname = this.playerInfo.nickname;
        }

        const extTransaction: IExternalTransaction = {
            trxId,
            operation: {
                operationId: this.getWinOperationId(),
                operationName: REMOTE_WALLET_OPERATION_NAME.release,
                externalTrxId: flow.request.externalId,
                gameId: flow.request.roundId,
                params: winParams
            },
            changes: [{
                account: META_INF_ACCOUNT,
                property: "remoteCall",
                amount: 1,
                trxType: "remote",
                walletKey: this.walletKey
            }],
        };

        try {
            await this.wallet.saveExternalTransaction(extTransaction);
        } catch (err) {
            if (err === TRANSACTION_EXISTS) {
                const trxData = await this.wallet.findCommittedTransaction(trxId,
                    extTransaction.operation.operationId);
                const params: RemoteWalletWinParams = trxData.operation.params;

                winResults = params.winResults;
            } else if (err === INSUFFICIENT_BALANCE) {
                log.warn("Transaction commit failed - insufficient balance");
                return Promise.reject(new Errors.InsufficientJackpotBalance());
            } else if (err === BAD_TRANSACTION_ID) {
                return Promise.reject(new Errors.ValidationError("invalid transactionId"));
            } else if (err === TRANSACTION_IS_PROCESSING) {
                return Promise.reject(new Errors.TransactionIsProcessing());
            } else {
                return Promise.reject(err);
            }
        }

        return winResults;
    }

    private getWinOperationId() {
        return this.instance.internalId + WIN_TRX_OPERATION_ID_MARKER;
    }

    private async isMigratedWinTransaction(flow: JackpotGameFlow): Promise<ITrxData> {
        if (!flow.hasContributions() && flow.hasWonJackpot() && this.instance.migratedAt) {
            const trxId = await this.wallet.parseTransactionId(flow.transactionId);
            if (trxId.timestamp <= this.instance.migratedAt.getTime()) {
                return this.wallet.findCommittedTransaction(flow.transactionId, this.getWinOperationId());
            }
        }
    }

    /**
     * Handle transaction retransmission in case if jackpot has been migrated to another region.
     */
    private async processMigratedTrxRetransmission(flow: JackpotGameFlow,
                                                   contributionTrx?: ITrxData,
                                                   winTrx?: ITrxData): Promise<JackpotGameFlowResult> {
        const params = contributionTrx?.operation?.params as LocalWalletContributionParams;
        const contributionsPayout: ContributionPayout[] =
            convertContributionsToContributionsPayout(flow.getContributions());
        const result: JackpotGameFlowResult = {
            contributions: contributionsPayout,
            playerContributions: params ? params.playerContributions : flow.getPlayerContributions(),
            gameResult: params ? params.gameResult : flow.getGameResult()
        };
        if (flow.hasWonJackpot()) {
            const data = winTrx || (await this.wallet.findCommittedTransaction(
                flow.transactionId, this.getWinOperationId()));
            if (data) {
                result.winPayouts = (data.operation.params as LocalWalletWinParams).wins;
                result.wins = result.winPayouts.map((win) => {
                    const item: JackpotWin = {
                        type: JackpotWinType.PLAYER,
                        pool: win.pool,
                        amount: win.seed + win.progressive,
                        seed: win.seed,
                        progressive: win.progressive,
                    };
                    if (win.transferPool !== undefined) {
                        item.type = JackpotWinType.TRANSFER;
                        (item as JackpotWinTransfer).transferPool = win.transferPool;
                    } else {
                        (item as JackpotPlayerWin).playerAmount = win.winAmount;
                        (item as JackpotPlayerWin).exchangeRate = win.currencyRate;
                    }
                    return item;
                });
            } else {
                const remoteTrxId: string = await this.saveMigrationTransaction(flow.transactionId);
                const request: RemoteGameFlowRequest = {
                    transactionId: remoteTrxId,
                    playerInfo: this.playerInfo,
                    requestRegion: config.region,
                    request: flow.request,
                    results: [{
                        jackpotId: this.instance.id,
                        gameResult: flow.getGameResult(),
                        wins: await flow.calculateWins()
                    }]
                };

                const response = await RemoteJackpotApiCache.getRemoteJackpotApi(this.instance.region)
                    .processGameFlow(request);
                const remoteResult: RemoteGameFlowProcessResult = response.results[0];

                result.gameResult = remoteResult.gameResult;

                if (remoteResult.wins) {
                    result.winPayouts = remoteResult.winPayouts;
                    result.wins = await this.saveRemoteWin(flow, remoteTrxId,
                        remoteResult.wins, remoteResult.winPayouts);
                }

                await this.remoteServices.tickerService.updateTicker(remoteResult.ticker);
            }
        }

        return result;
    }

    private async saveMigrationTransaction(trxId: string): Promise<string> {
        let remoteTrxId: string = await this.remoteServices.trxIds.generateTrxId(this.instance.region);

        const migratedTrx: IExternalTransaction = {
            trxId,
            operation: {
                operationId: MIGRATION_TRX_RETRANSMISSION_ID,
                operationName: MIGRATION_TRX_RETRANSMISSION,
                params: {
                    remoteTrxId: remoteTrxId
                }
            },
            changes: [{
                account: META_INF_ACCOUNT,
                property: "remoteCall",
                amount: 1,
                trxType: "remote",
                walletKey: this.walletKey
            }],
        };

        try {
            await this.wallet.saveExternalTransaction(migratedTrx);
        } catch (err) {
            if (err === TRANSACTION_EXISTS) {
                const trxData = await this.wallet.findCommittedTransaction(trxId, MIGRATION_TRX_RETRANSMISSION_ID);
                remoteTrxId = trxData.operation.params.remoteTrxId;
            } else if (err === INSUFFICIENT_BALANCE) {
                log.warn("Transaction commit failed - insufficient balance");
                return Promise.reject(new Errors.InsufficientJackpotBalance());
            } else if (err === BAD_TRANSACTION_ID) {
                return Promise.reject(new Errors.ValidationError("invalid transactionId"));
            } else if (err === TRANSACTION_IS_PROCESSING) {
                return Promise.reject(new Errors.TransactionIsProcessing());
            } else {
                return Promise.reject(err);
            }
        }

        return remoteTrxId;
    }
}
