import {
    BAD_TRANSACTION_ID,
    COMMIT_ATTEMPTS_EXCEEDED,
    IAccount,
    INSUFFICIENT_BALANCE,
    IOperation,
    ITransaction,
    IWallet,
    MAX_CAPACITY_REACHED,
    TRANSACTION_EXISTS,
    TRANSACTION_IS_PROCESSING
} from "@skywind-group/sw-wallet";
import * as Errors from "../errors";
import { DuplicateTransactionError, JpConfigError, JpFeatureForbidden, ValidationError } from "../errors";
import { measures } from "@skywind-group/sw-utils";
import { AuditInfo, JackpotAuditType, JackpotDisableMode, JackpotInternalInstance } from "../api/model";
import {
    Contribution,
    ContributionRequest,
    InternalTransferPoolManipulation,
    JackpotLoanPoolConfig,
    JackpotOperationStatus,
    JackpotPoolProperties,
    JackpotResult,
    Operation,
    PoolDepositRequest,
    PoolManipulation,
    PoolManipulationType,
    PoolManipulationTransferType as TransferType,
    PoolState,
    <PERSON>Change,
    Ticker,
    TransferBetweenPoolsManipulation,
    UpdatePoolRequest
} from "@skywind-group/sw-jpn-core";
import {
    getPoolTitle,
    getWalletKey,
    JackpotPlayerWin,
    JackpotWin,
    JackpotWinTransfer,
    JackpotWinType,
    WalletService
} from "../../definition";
import logger from "../utils/logger";
import {
    BaseWalletParams,
    CONTRIBUTION_RESOLVE_TRX_OPERATION_ID_MARKER,
    ContributionPayout,
    convertContributionsToContributionsPayout,
    InternalTransfer,
    LOCAL_WALLET_OPERATION_NAME,
    LocalWalletContributionParams,
    LocalWalletTransferParams,
    LocalWalletWinParams,
    META_INF_ACCOUNT,
    META_INF_DISABLED,
    META_INF_SEQ_ID,
    TRANSFER_TRX_OPERATION_ID_MARKER,
    WalletWinPayout,
    WIN_RESOLVE_TRX_OPERATION_ID_MARKER,
    WIN_TRX_OPERATION_ID_MARKER,
} from "./walletParams";
import { toMajorUnits, toMinorUnits } from "../utils/utils";
import config from "../config";
import JackpotAuditService from "../services/jackpotAudits";

const measure = measures.measure;

const log = logger("routes:jpn.module");

// Default precision of jackpot contributions to store in the wallet.
// E.g. if bet is 1 cent and contribution is 0.01 % we want to store 0.0001 cent in the wallet.
export const DEFAULT_PRECISION = **********;

const FULL_TRANSFER_TO_POOL_MAX_RETRIES = 20; // Max count to retry transfer all amount from one pool to another
const TRANSACTION_TYPE_TAKE = "take";
const TRANSACTION_TYPE_GIVE = "give";
const TRANSACTION_TYPE_DEPOSIT = "deposit";
const TRANSACTION_TYPE_UPDATE = "update";
const TRANSACTION_TYPE_TAKE_LOAN = "take_loan";
const TRANSACTION_TYPE_REFUND_LOAN = "refund_loan";

const JP_FEATURE_DEPOSIT = "deposit";  // Provides to accept deposit money operations to pools
const JP_FEATURE_TRANSFER = "transfer"; // Provides money transfer from one pool to another within one jackpot
const JP_FEATURE_LOAN = "loan"; // Provides money transfer with possibility to take loan money from seed value
                                // Applied to "transfer" feature
export const JP_FEATURE_OVERRIDE_POOLS_BY_INSTANCE = "overridableByInstance"; // Pools definition can be fully
// overridden by instance definition

const JP_WALLET_INITIAL_SEED = "initialSeed";
const JP_WALLET_SEED = "seed";
const JP_WALLET_PROGRESSIVE = "progressive";
const JP_WALLET_PRECISION = "precision";
const JP_WALLET_SEED_SINCE_LAST_WIN = "seedSinceLastWin";
const JP_WALLET_PROGRESSIVE_SINCE_LAST_WIN = "progressiveSinceLastWin";

// List of reserved properties in Jackpot wallet
const JP_WALLET_PROPERTIES = [
    JP_WALLET_INITIAL_SEED,   // Left for Backward Compatibility. Should not be reused, it is not in sync in the wallet.
    JP_WALLET_PRECISION,
    JP_WALLET_SEED,
    JP_WALLET_SEED_SINCE_LAST_WIN,
    JP_WALLET_PROGRESSIVE,
    JP_WALLET_PROGRESSIVE_SINCE_LAST_WIN
];

export interface ContributionResult {
    contributions: ContributionPayout[];
    playerContributions: Contribution[];
    gameResult: JackpotResult;
    contributionParams?: LocalWalletContributionParams;
}

export interface JpWalletReleaseWinResult {
    wins: JackpotWin[];
    winPayouts: WalletWinPayout[];
    winParams?: LocalWalletWinParams;
}

interface TransferBetweenPoolsParams {
    jpWallet: IWallet,
    manipulation: TransferBetweenPoolsManipulation,
    seed: number,
    progressive: number;
    releaseSeed?: number,
    releaseProgressive?: number
}

export class JackpotWallet {

    private walletKey: string;
    private currentTicker: Ticker;
    private readonly specialJackpot: boolean;

    constructor(public baseParams: BaseWalletParams,
                public instance: JackpotInternalInstance,
                public exchangeRate: number,
                private wallet: WalletService,
                private audit?: AuditInfo) {
        this.walletKey = getWalletKey(instance);
        this.specialJackpot = this.checkJackpotFeaturePermission(JP_FEATURE_DEPOSIT);
    }

    /**
     * getTicker - get jackpot pools information
     */
    @measure({ name: "jackpotModule.getTicker", isAsync: true, debugOnly: true })
    public async getTicker(force?: boolean): Promise<Ticker> {
        if (!this.currentTicker || force) {
            const jpWallet: IWallet = await this.wallet.get(this.walletKey);
            this.updateCurrentTicker(jpWallet);
        }

        return this.copyTicker(this.currentTicker);
    }

    /**
     * contribute - do the actual contribution to relevant jackpot pools
     */
    @measure({ name: "jackpotModule.contribute", isAsync: true, debugOnly: true })
    public async contribute(trxId: string,
                            request: ContributionRequest,
                            contributions: Contribution[],
                            playerContributions: Contribution[],
                            gameResult?: JackpotResult,
                            deferredWinsEnabled?: boolean): Promise<ContributionResult> {
        if (request.amount < 0) {
            return Promise.reject(new Errors.ValidationError("negative contribution amount"));
        }

        let contributionsPayout: ContributionPayout[] = convertContributionsToContributionsPayout(contributions);
        const contributionParams: LocalWalletContributionParams = {
            ...this.baseParams,
            contributionAmount: request.amount,
            currencyRate: this.exchangeRate,
            gameData: request,
            precision: undefined,        // will be defined later
            initialSeed: {},             // will be defined later
            gameResult,
            playerContributions,
            contributions: contributionsPayout
        };

        const trxOperation: IOperation = {
            operationId: this.instance.internalId,
            operationName: LOCAL_WALLET_OPERATION_NAME.contribute,
            externalTrxId: request.externalId,
            params: contributionParams
        };
        const transaction: ITransaction = await this.wallet.startTransaction(trxId, trxOperation);
        const jpWallet: IWallet = await transaction.getWallet(this.walletKey);

        for (const contribution of contributionsPayout) {
            const poolDef = this.instance.definition.list.find((def) => def.id === contribution.pool);

            // get account for a specific pool
            const account: IAccount = jpWallet.accounts.get(contribution.pool);
            const precision = Number(account.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);
            contributionParams.precision = precision;

            // contribute to seed pot if defined
            if (contribution.seed) {
                const seed = toMinorUnits(contribution.seed, precision);
                account.inc(JP_WALLET_SEED, seed, "contribution");
                if (!this.specialJackpot) {
                    account.inc(JP_WALLET_SEED_SINCE_LAST_WIN, seed, "contribution");
                }
            }
            contribution.totalSeed =
                toMajorUnits(Number(account.get(JP_WALLET_SEED) || 0), precision);

            // contribute to progressive pot if defined
            if (contribution.progressive) {
                const progressive = toMinorUnits(contribution.progressive, precision);
                account.inc(JP_WALLET_PROGRESSIVE, progressive, "contribution");
                if (!this.specialJackpot) {
                    account.inc(JP_WALLET_PROGRESSIVE_SINCE_LAST_WIN, progressive, "contribution");
                }
            }
            contribution.totalProgressive =
                toMajorUnits(Number(account.get(JP_WALLET_PROGRESSIVE) || 0), precision);

            this.setAccountProperties(account, contribution.properties);

            contributionParams.initialSeed[contribution.pool] = (poolDef.seed && poolDef.seed.amount) || 0;
            if (deferredWinsEnabled) {
                contribution.status = JackpotOperationStatus.PENDING;
            }
        }
        if (contributions.length) {
            this.incrementSeqId(jpWallet, "contribute");
            this.checkEnabled(jpWallet, "contribute");
        }
        try {
            await transaction.commit();
        } catch (err) {
            if (err === TRANSACTION_EXISTS) {
                const trxData = await this.wallet.findCommittedTransaction(trxId, trxOperation.operationId);
                gameResult = trxData.operation.params.gameResult || gameResult;
                const pools = new Map<string, ContributionPayout>();
                for (const change of trxData.data) {
                    const pool: string = change.account;
                    if (pool !== META_INF_ACCOUNT) {
                        const account: IAccount = jpWallet.accounts.get(pool);
                        const precision = Number(account.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);
                        if (!pools.has(pool)) {
                            pools.set(pool, {
                                pool,
                                totalSeed: 0,           // will be defined later
                                totalProgressive: 0     // will be defined later
                            });
                        }
                        if (change.property === JP_WALLET_SEED) {
                            pools.get(pool).seed = toMajorUnits(change.amount, precision);
                        }
                        if (change.property === JP_WALLET_PROGRESSIVE) {
                            pools.get(pool).progressive = toMajorUnits(change.amount, precision);
                        }
                        pools.get(pool).totalSeed = pools.get(pool).seed || 0;
                        pools.get(pool).totalProgressive = pools.get(pool).progressive || 0;
                    }
                }

                contributionsPayout = Array.from(pools.values());
                playerContributions = trxData.operation.params.playerContributions || playerContributions;
            } else if (err === COMMIT_ATTEMPTS_EXCEEDED) {
                return Promise.reject(new Errors.TooManyJackpotRequests());
            } else if (err === BAD_TRANSACTION_ID) {
                return Promise.reject(new Errors.ValidationError("invalid transactionId"));
            } else if (err === MAX_CAPACITY_REACHED) {
                const disabled = this.isWalletDisabled(jpWallet) || await this.isDisabled();
                if (disabled) {
                    log.info({ contributionParams }, "Skip contribution on disabled jackpot");
                    gameResult = undefined;
                    contributionsPayout = undefined;
                    playerContributions = undefined;
                } else {
                    return Promise.reject(err);
                }
            } else if (err === TRANSACTION_IS_PROCESSING) {
                return Promise.reject(new Errors.TransactionIsProcessing());
            } else {
                return Promise.reject(err);
            }
        }

        this.updateCurrentTicker(jpWallet);

        const result: ContributionResult = {
            contributions: contributionsPayout,
            playerContributions,
            gameResult
        };
        if (deferredWinsEnabled) {
            result.contributionParams = contributionParams;
        }

        return result;
    }

    @measure({ name: "jackpotModule.resolveContribution", isAsync: true, debugOnly: true })
    public async resolveContribution(
        trxId: string,
        externalId: string,
        params: LocalWalletContributionParams,
        contributionResolution: JackpotOperationStatus.RESOLVED | JackpotOperationStatus.REJECTED = JackpotOperationStatus.RESOLVED
    ): Promise<ContributionResult> {

        log.info({ contributionParams: params }, "Resolve contribution. Resolution: %s", contributionResolution);

        let gameResult = params.gameResult;
        const contributionParams = params;
        let contributions = contributionParams.contributions.map(contribution => ({
            ...contribution,
            status: contributionResolution
        }));
        contributionParams.contributions = contributions;

        const operationName = contributionResolution === JackpotOperationStatus.RESOLVED ?
                              LOCAL_WALLET_OPERATION_NAME.contributeConfirmation :
                              LOCAL_WALLET_OPERATION_NAME.contributeRollback;

        const operation: IOperation = {
            operationId: this.instance.internalId + CONTRIBUTION_RESOLVE_TRX_OPERATION_ID_MARKER,
            externalTrxId: externalId,
            operationName: operationName,
            force: true,
            params: contributionParams
        };

        const transaction: ITransaction = await this.wallet.startTransaction(trxId, operation);
        const jpWallet: IWallet = await transaction.getWallet(this.walletKey);

        // If the operation is rejected, rollback seed and progressive pots
        if (contributionResolution === JackpotOperationStatus.REJECTED) {
            for (const contribution of contributions) {
                const account: IAccount = jpWallet.accounts.get(contribution.pool);

                const precision = Number(account.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);
                // rollback seed pot if defined
                if (contribution.seed) {
                    const seed = toMinorUnits(contribution.seed, precision);
                    account.inc(JP_WALLET_SEED, -seed, "contributeRollback");

                    if (!this.specialJackpot) {
                        account.inc(JP_WALLET_SEED_SINCE_LAST_WIN, -seed, "contributeRollback");
                    }
                }
                contribution.totalSeed =
                    toMajorUnits(Number(account.get(JP_WALLET_SEED) || 0), precision);

                // rollback progressive pot if defined
                if (contribution.progressive) {
                    const progressive = toMinorUnits(contribution.progressive, precision);
                    account.inc(JP_WALLET_PROGRESSIVE, -progressive, "contributeRollback");
                    if (!this.specialJackpot) {
                        account.inc(JP_WALLET_PROGRESSIVE_SINCE_LAST_WIN, -progressive, "contributeRollback");
                    }
                }

                contribution.totalProgressive =
                    toMajorUnits(Number(account.get(JP_WALLET_PROGRESSIVE) || 0), precision);
            }
            if (contributions.length) {
                this.incrementSeqId(jpWallet, "rollback_contribute");
            }
        } else {
            for (const contribution of contributions) {
                const account: IAccount = jpWallet.accounts.get(contribution.pool);
                // Increment by 0 so that the transaction is parsed correctly when added to the redis transaction-list
                if (contribution.seed) {
                    account.inc(JP_WALLET_SEED, 0, "contributeConfirmation");
                }
            }
            if (contributions.length) {
                this.incrementSeqId(jpWallet, "resolve_contribute");
            }
        }

        try {
            await transaction.commit();
        } catch (err) {
            if (err === COMMIT_ATTEMPTS_EXCEEDED) {
                return Promise.reject(new Errors.TooManyJackpotRequests());
            } else if (err === BAD_TRANSACTION_ID) {
                return Promise.reject(new Errors.ValidationError("invalid transactionId"));
            } else if (err === MAX_CAPACITY_REACHED) {
                const disabled = this.isWalletDisabled(jpWallet) || await this.isDisabled();
                if (disabled) {
                    log.info({ contributionParams }, "Skip contribution on disabled jackpot");
                    gameResult = undefined;
                    contributions = undefined;
                    contributionParams.playerContributions = undefined;
                } else {
                    return Promise.reject(err);
                }
            } else if (err === TRANSACTION_IS_PROCESSING) {
                return Promise.reject(new Errors.TransactionIsProcessing());
            } else {
                return Promise.reject(err);
            }
        }

        this.updateCurrentTicker(jpWallet);

        return {
            contributions,
            playerContributions: contributionParams.playerContributions,
            gameResult,
            contributionParams
        };
    }

    @measure({ name: "jackpotModule.resolveWin", isAsync: true, debugOnly: true })
    public async resolveWin(
        trxId: string,
        externalId: string,
        params: LocalWalletWinParams,
        winResolution: JackpotOperationStatus.RESOLVED | JackpotOperationStatus.REJECTED = JackpotOperationStatus.RESOLVED
    ): Promise<JpWalletReleaseWinResult> {

        log.info({ winParams: params }, "Resolve win. Resolution: %s", winResolution);

        const winParams = params;
        let wins = winParams.wins.map(win => ({
            ...win,
            status: winResolution
        })) as any;
        winParams.wins = wins;
        const operationName = winResolution === JackpotOperationStatus.RESOLVED ?
                                            LOCAL_WALLET_OPERATION_NAME.releaseConfirmation :
                                            LOCAL_WALLET_OPERATION_NAME.releaseRollback;

        const operation: IOperation = {
            operationId: this.instance.internalId + WIN_RESOLVE_TRX_OPERATION_ID_MARKER,
            externalTrxId: externalId,
            operationName: operationName,
            force: true,
            params: winParams
        };

        const transaction: ITransaction = await this.wallet.startTransaction(trxId, operation);
        const jpWallet: IWallet = await transaction.getWallet(this.walletKey);

        if (winResolution === JackpotOperationStatus.REJECTED) {
            for (const win of wins) {
                const account: IAccount = jpWallet.accounts.get(win.pool);
                // Rollback seed and progressive fields

                const pool = this.instance.definition.list.find((definition) => definition.id === win.pool);

                const precision = Number(account.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);
                winParams.precision = precision;

                const lowerSeedLimit = toMinorUnits(pool.lowerSeedLimit !== undefined ?
                                                    pool.lowerSeedLimit :
                                                    Number.MIN_SAFE_INTEGER, precision);
                const releaseSeed = toMinorUnits(win.seed, precision);
                const releaseProgressive = toMinorUnits(win.progressive, precision);
                const seedSinceLastWin = toMinorUnits(win.seedSinceLastWin, precision);

                account.inc(JP_WALLET_SEED, releaseSeed, "releaseRollback", lowerSeedLimit);
                account.inc(JP_WALLET_PROGRESSIVE, releaseProgressive, "releaseRollback", 0);
                account.inc(JP_WALLET_PROGRESSIVE_SINCE_LAST_WIN, releaseProgressive, "releaseRollback", 0);
                account.inc(JP_WALLET_SEED_SINCE_LAST_WIN, seedSinceLastWin, "releaseRollback", 0);
            }
            if (wins.length) {
                this.incrementSeqId(jpWallet, "rollback_win");
            }
        } else {
            for (const win of wins) {
                const account: IAccount = jpWallet.accounts.get(win.pool);
                // Increment by 0 so that the transaction is parsed correctly when added to the redis transaction-list
                account.inc(JP_WALLET_SEED, 0, "releaseConfirmation");
                this.setAccountProperties(account, win.properties);
            }

            if (wins.length) {
                this.incrementSeqId(jpWallet, "resolve_win");
            }
        }

        let winPayouts: WalletWinPayout[];
        try {
            await transaction.commit();
            winPayouts = operation.params.wins;
        } catch (err) {
            if (err === COMMIT_ATTEMPTS_EXCEEDED) {
                return Promise.reject(new Errors.TooManyJackpotRequests());
            } else if (err === INSUFFICIENT_BALANCE) {
                log.warn({ winParams }, "Transaction commit failed - insufficient balance");
                return Promise.reject(new Errors.InsufficientJackpotBalance());
            } else if (err === BAD_TRANSACTION_ID) {
                return Promise.reject(new Errors.ValidationError("invalid transactionId"));
            } else if (err === MAX_CAPACITY_REACHED) {
                const disabled = this.isWalletDisabled(jpWallet) || await this.isDisabled();
                if (disabled) {
                    log.info({ winParams }, "Skip win on disabled jackpot");
                    wins = undefined;
                    winPayouts = undefined;
                } else {
                    return Promise.reject(err);
                }
            } else if (err === TRANSACTION_IS_PROCESSING) {
                return Promise.reject(new Errors.TransactionIsProcessing());
            } else {
                return Promise.reject(err);
            }
        }

        this.updateCurrentTicker(jpWallet);

        return { wins, winPayouts, winParams };
    }

    @measure({ name: "jackpotModule.releaseWin", isAsync: true, debugOnly: true })
    public async releaseWin(trxId: string,
                            externalId: string,
                            roundId: string,
                            wins: JackpotWin[],
                            betAmount?: number,
                            deferredWinsEnabled?: boolean): Promise<JpWalletReleaseWinResult> {
        const winParams: LocalWalletWinParams = {
            ...this.baseParams,
            precision: undefined,    // will be defined later
            betAmount,
            wins: []                 // will be defined later
        };
        const trxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: this.instance.internalId + WIN_TRX_OPERATION_ID_MARKER,
            operationName: LOCAL_WALLET_OPERATION_NAME.release,
            externalTrxId: externalId,
            params: winParams
        };
        const transaction: ITransaction = await this.wallet.startTransaction(trxId, trxOperation);
        const jpWallet: IWallet = await transaction.getWallet(this.walletKey);

        // in case of two or more wins from single pool we need to update seed value after win
        const processedPools: Set<string> = new Set();
        let winPayouts: WalletWinPayout[];
        for (const win of wins) {

            const pool = this.instance.definition.list.find((definition) => definition.id === win.pool);

            const account: IAccount = jpWallet.accounts.get(win.pool);
            const precision = Number(account.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);
            winParams.precision = precision;

            const lowerSeedLimit = toMinorUnits(pool.lowerSeedLimit !== undefined ?
                                                pool.lowerSeedLimit :
                                                Number.MIN_SAFE_INTEGER, precision);
            const winAmount = toMinorUnits(win.amount, precision);
            const initialSeed = toMinorUnits((pool.seed && pool.seed.amount) || 0, precision);
            const seed = Number(account.get(JP_WALLET_SEED) || 0);
            const progressive = Number(account.get(JP_WALLET_PROGRESSIVE) || 0);

            const releaseSeed = toMinorUnits(win.seed, precision);
            const releaseProgressive = toMinorUnits(win.progressive, precision);

            if ((initialSeed + progressive) < (releaseSeed + releaseProgressive)) {
                log.warn({ initialSeed, seed, progressive, releaseSeed, releaseProgressive },
                    "Cannot win because of insufficient jackpot balance");
                throw new Errors.InsufficientJackpotBalance();
            }
            const seedSinceLastWin: number = processedPools.has(win.pool) ? 0 :
                                             Number(account.get(JP_WALLET_SEED_SINCE_LAST_WIN) || 0);

            const progressiveSinceLastWin: number = processedPools.has(win.pool) ? 0 :
                                                    Number(account.get(JP_WALLET_PROGRESSIVE_SINCE_LAST_WIN) || 0);

            let external;
            if (this.instance?.info?.externalId && this.instance?.info?.externalStartDate) {
                external = {
                    externalId: this.instance.info.externalId,
                    externalStartDate: this.instance.info.externalStartDate
                };
            }

            const info = external ? { external } : null;

            log.info({
                releaseSeed: releaseSeed, releaseProgressive: releaseProgressive,
                seed, winAmount, initialSeed, progressive, precision,
                playerCode: this.baseParams.playerCode, seedSinceLastWin, progressiveSinceLastWin, info
            }, "Calculate win param");

            const walletPayout: WalletWinPayout = {
                pool: win.pool,
                initialSeed: toMajorUnits(initialSeed, precision),
                seed: win.seed,
                progressive: win.progressive,
                totalSeed: toMajorUnits(seed, precision),
                totalProgressive: toMajorUnits(progressive, precision),
                seedSinceLastWin: toMajorUnits(seedSinceLastWin, precision),
                progressiveSinceLastWin: toMajorUnits(progressiveSinceLastWin, precision),
                title: getPoolTitle(this.instance, win.pool),
                info,
            };

            if (deferredWinsEnabled) {
                walletPayout.status = JackpotOperationStatus.PENDING;
            }

            const trxType = win.type === JackpotWinType.TRANSFER ? "transfer" : "release";

            account.inc(JP_WALLET_SEED, -releaseSeed, trxType, lowerSeedLimit);
            account.inc(JP_WALLET_PROGRESSIVE, -releaseProgressive, trxType, 0);
            account.set(JP_WALLET_PROGRESSIVE_SINCE_LAST_WIN, 0, "reset", true);
            account.set(JP_WALLET_SEED_SINCE_LAST_WIN, 0, "reset", true);

            this.setAccountProperties(account, win.properties);

            if (win.type === JackpotWinType.TRANSFER) {
                const transferWin: JackpotWinTransfer = win as JackpotWinTransfer;
                if (transferWin.transferPool) {
                    const transferAccount: IAccount = jpWallet.accounts.get(transferWin.transferPool);
                    const transferPoolSeed = Number(transferAccount.get(JP_WALLET_SEED) || 0);
                    const transferPoolProgressive = Number(transferAccount.get(JP_WALLET_PROGRESSIVE) || 0);
                    transferAccount.inc(JP_WALLET_SEED, releaseSeed, trxType);
                    transferAccount.inc(JP_WALLET_PROGRESSIVE, releaseProgressive, trxType);
                    walletPayout.transferPoolSeed = toMajorUnits(transferPoolSeed, precision);
                    walletPayout.transferPoolProgressive = toMajorUnits(transferPoolProgressive, precision);
                }
                walletPayout.transferPool = transferWin.transferPool;
            } else {
                walletPayout.winAmount = (win as JackpotPlayerWin).playerAmount;
                walletPayout.currencyRate = (win as JackpotPlayerWin).exchangeRate;
            }

            winParams.wins.push(walletPayout);

            processedPools.add(win.pool);
        }

        if (wins.length) {
            this.incrementSeqId(jpWallet, "release_win");
            if (this.instance.isDisabled && this.instance.disableMode === JackpotDisableMode.NEXT_WIN) {
                const isDisabled = await this.isDisabled();
                if (!isDisabled || this.instance.baseType !== "sw-instant-jp") {
                    await this.markDisabledWithAudit(jpWallet, "release_win");
                }
            } else {
                this.checkEnabled(jpWallet, "release_win");
            }
        }

        try {
            await transaction.commit();
            winPayouts = winParams.wins;
        } catch (err) {
            if (err === TRANSACTION_EXISTS) {
                const trxData = await this.wallet.findCommittedTransaction(trxId, trxOperation.operationId);
                wins = trxData.operation.params.wins.map((win) => ({
                    type: win.transferPool !== undefined ? JackpotWinType.TRANSFER : JackpotWinType.PLAYER,
                    pool: win.pool,
                    amount: win.seed + win.progressive,
                    seed: win.seed,
                    progressive: win.progressive,
                    playerAmount: win.winAmount,
                    exchangeRate: win.currencyRate,
                    transferPool: win.transferPool,
                    status: win.status
                }));
                winPayouts = trxData.operation.params.wins;
            } else if (err === COMMIT_ATTEMPTS_EXCEEDED) {
                return Promise.reject(new Errors.TooManyJackpotRequests());
            } else if (err === INSUFFICIENT_BALANCE) {
                log.warn({ winParams }, "Transaction commit failed - insufficient balance");
                return Promise.reject(new Errors.InsufficientJackpotBalance());
            } else if (err === BAD_TRANSACTION_ID) {
                return Promise.reject(new Errors.ValidationError("invalid transactionId"));
            } else if (err === MAX_CAPACITY_REACHED) {
                const disabled = this.isWalletDisabled(jpWallet) || await this.isDisabled();
                if (disabled) {
                    log.info({ winParams }, "Skip win on disabled jackpot");
                    wins = undefined;
                    winPayouts = undefined;
                } else {
                    return Promise.reject(err);
                }
            } else if (err === TRANSACTION_IS_PROCESSING) {
                return Promise.reject(new Errors.TransactionIsProcessing());
            } else {
                return Promise.reject(err);
            }
        }

        this.updateCurrentTicker(jpWallet);

        const result: JpWalletReleaseWinResult = { wins, winPayouts };
        if (deferredWinsEnabled) {
            result.winParams = winParams;
        }

        return result;
    }

    /**
     * Move fixed or all amount of progressive money from one pool to another within one jackpot.
     * If amount not present will charge all money from donor pool.
     *
     * @param {string} fromPoolId donor pool Id
     * @param {string} toPoolId recipient pool Id
     * @param {string} transactionId transactionId for this operation
     * @param {number} amount amount to transfer (in jackpot currency)
     *
     * @throws ValidationError, JackpotNotInitialized, DuplicateTransactionError, InsufficientJackpotBalance
     * @returns {Promise<void>}
     */
    public async transferProgressive(fromPoolId: string, toPoolId: string, transactionId: string, amount?: number) {

        const transferFeatureExists = this.checkJackpotFeaturePermission(JP_FEATURE_TRANSFER);
        if (!transferFeatureExists) {
            return Promise.reject(new JpFeatureForbidden(JP_FEATURE_TRANSFER));
        }

        if (!fromPoolId || !toPoolId) {
            return Promise.reject(new ValidationError(`Invalid ${fromPoolId ? "toPoolId" : "fromPoolId"}`));
        }

        if (fromPoolId === toPoolId) {
            return Promise.reject(new ValidationError("Can't transfer to the same pool"));
        }

        const absentPoolId = this.checkPoolsInDefinition(fromPoolId, toPoolId);
        if (absentPoolId) {
            return Promise.reject(new ValidationError(`${absentPoolId} doesn't exist in this jackpot`));
        }

        const loanPool = this.validateLoanTransferConfig(fromPoolId, toPoolId);

        const transferablePropertyName = JP_WALLET_PROGRESSIVE;

        const trxOperation: IOperation = {
            operationId: 1,
            operationName: "pool_transfer",
            params: {
                gameId: this.instance.jpGameId
            }
        };
        const transaction: ITransaction = await this.wallet.startTransaction(transactionId, trxOperation);

        const jpWallet: IWallet = await transaction.getWallet(this.walletKey);
        transaction.operation.params.precision = Number(jpWallet.accounts.get(fromPoolId).get(JP_WALLET_PRECISION)
            || DEFAULT_PRECISION);

        try {
            if (Number.isFinite(amount)) {
                await this.transferPool(transaction, transferablePropertyName, fromPoolId, toPoolId, amount, loanPool);
            } else {
                await this.transferAllPool(transaction, transferablePropertyName, fromPoolId, toPoolId);
            }
        } catch (error) {
            if (error === TRANSACTION_EXISTS) {
                return Promise.reject(new DuplicateTransactionError());
            } else if (error === INSUFFICIENT_BALANCE) {
                return Promise.reject(new Errors.InsufficientJackpotBalance());
            } else if (error === COMMIT_ATTEMPTS_EXCEEDED) {
                return Promise.reject(new Errors.TooManyJackpotRequests());
            } else if (error === BAD_TRANSACTION_ID) {
                return Promise.reject(new Errors.ValidationError("Invalid transactionId"));
            } else if (error === TRANSACTION_IS_PROCESSING) {
                return Promise.reject(new Errors.TransactionIsProcessing());
            } else {
                return Promise.reject(error);
            }
        }
    }

    public async poolDeposit(poolId: string, request: PoolDepositRequest) {

        const ok = this.checkJackpotFeaturePermission(JP_FEATURE_DEPOSIT);
        if (!ok) {
            return Promise.reject(new JpFeatureForbidden(JP_FEATURE_DEPOSIT));
        }

        const absentPoolId = this.checkPoolsInDefinition(poolId);
        if (absentPoolId) {
            return Promise.reject(new ValidationError(`${absentPoolId} doesn't exist in this jackpot`));
        }

        const trxOperation: IOperation = { operationId: 1, operationName: "pool_deposit" };
        const transaction: ITransaction = await this.wallet.startTransaction(request.transactionId, trxOperation);

        const jpWallet: IWallet = await transaction.getWallet(this.walletKey);

        const jpAccount: IAccount = jpWallet.accounts.get(poolId);
        const precision = Number(jpAccount.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);

        if (request.seed) {
            const seedAmount = toMinorUnits(request.seed, precision);
            jpAccount.inc(JP_WALLET_SEED, seedAmount, TRANSACTION_TYPE_DEPOSIT);
        }

        if (request.progressive) {
            const progressiveAmount = toMinorUnits(request.progressive, precision);
            jpAccount.inc(JP_WALLET_PROGRESSIVE, progressiveAmount, TRANSACTION_TYPE_DEPOSIT);
        }

        this.incrementSeqId(jpWallet, "deposit");

        try {
            await transaction.commit();
        } catch (error) {
            return this.catchError(error);
        }
    }

    public async updatePool(poolId: string, transactionId, request: UpdatePoolRequest) {

        if (!config.isProduction() && this.instance.isTest) {

            const absentPoolId = this.checkPoolsInDefinition(poolId);
            if (absentPoolId) {
                return Promise.reject(new ValidationError(`${absentPoolId} doesn't exist in this jackpot`));
            }

            const trxOperation: IOperation = { operationId: 1, operationName: "pool_update" };
            const transaction: ITransaction = await this.wallet.startTransaction(transactionId, trxOperation);

            const jpWallet: IWallet = await transaction.getWallet(this.walletKey);

            const jpAccount: IAccount = jpWallet.accounts.get(poolId);
            const precision = Number(jpAccount.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);

            if (Number.isFinite(request.seed)) {
                const seedAmount = toMinorUnits(request.seed, precision);
                jpAccount.set(JP_WALLET_SEED, seedAmount, TRANSACTION_TYPE_UPDATE, true);
            }

            if (Number.isFinite(request.progressive)) {
                const progressiveAmount = toMinorUnits(request.progressive, precision);
                jpAccount.set(JP_WALLET_PROGRESSIVE, progressiveAmount, TRANSACTION_TYPE_UPDATE, true);
            }

            this.incrementSeqId(jpWallet, "update");

            try {
                await transaction.commit();
            } catch (error) {
                return this.catchError(error);
            }

        } else {
            throw new ValidationError("Production environment OR not test jackpot instance");
        }
    }

    private catchError(error) {
        if (error === TRANSACTION_EXISTS) {
            return Promise.reject(new DuplicateTransactionError());
        } else if (error === INSUFFICIENT_BALANCE) {
            return Promise.reject(new Errors.InsufficientJackpotBalance());
        } else if (error === COMMIT_ATTEMPTS_EXCEEDED) {
            return Promise.reject(new Errors.TooManyJackpotRequests());
        } else if (error === BAD_TRANSACTION_ID) {
            return Promise.reject(new Errors.ValidationError("Invalid transactionId"));
        } else if (error === TRANSACTION_IS_PROCESSING) {
            return Promise.reject(new Errors.TransactionIsProcessing());
        } else {
            return Promise.reject(error);
        }
    }

    private checkJackpotFeaturePermission(...features: string[]): boolean {

        if (features.length === 0) {
            return;
        }

        if (!this.instance.definition.features || this.instance.definition.features.length === 0) {
            return false;
        }

        for (const requiredFeature of features) {
            const found = this.instance.definition.features
                .find((supportedFeature) => requiredFeature === supportedFeature);
            if (!found) {
                return false;
            }
        }
        return true;
    }

    /**
     * Searches requested poolIds in jackpot definition, return the first not found poolId or undefined if all pools in.
     *
     * @param {string} poolIds
     * @returns string
     */
    private checkPoolsInDefinition(...poolIds: string[]): string {
        const allPoolIds = this.instance.definition.list.map((def) => def.id);
        const allPoolsSet = new Set(allPoolIds);

        for (const poolId of poolIds) {
            if (!allPoolsSet.has(poolId)) {
                return poolId;
            }
        }
        return undefined;
    }

    /**
     * Validates and returns LoanPool for transfer pools operations
     *
     * @throws JpConfigError
     * @returns {JackpotLoanPoolConfig}
     */
    private validateLoanTransferConfig(...validPoolIds: string[]): JackpotLoanPoolConfig {
        const loadFeatureExists = this.checkJackpotFeaturePermission(JP_FEATURE_LOAN);

        if (!loadFeatureExists) {
            return undefined;
        }

        const loanPool = this.instance.definition.loanPool;

        if (!loanPool) {
            throw new JpConfigError(`Jackpot with "loan" feature must declare loanPool in definitions config`);
        }

        const absentLoanPool = this.checkPoolsInDefinition(loanPool.poolId);
        if (absentLoanPool) {
            throw new JpConfigError(`Can't fined ${absentLoanPool} as loanPool`);
        }

        if (loanPool.property !== JP_WALLET_PROGRESSIVE && loanPool.property !== JP_WALLET_SEED) {
            throw new JpConfigError(`Invalid "poolProperty" = ${loanPool.property} value in loanPool config`);
        }

        if (validPoolIds) {
            const found = validPoolIds.find((poolId) => poolId === loanPool.poolId);

            if (!found) {
                throw new JpConfigError(`Loan pool (${loanPool.poolId}) must be one of ${validPoolIds.join(",")}`);
            }
        }

        return loanPool;
    }

    /**
     * Transfer fixed amount of money from one pool to another within one jackpot by property name.
     * Fast fail on commit.
     *
     * @param {ITransaction} trx one-off transaction for this operation.
     * @param {string} property propertyKey for amount storage to transfer from-to
     * @param {string} fromPool donor pool Id
     * @param {string} toPool recipient pool Id
     * @param {number} amount positive amount to transfer from-to
     * @param {JackpotLoanPoolConfig} loanPool loan (pool + property) for transfer ops with debts.
     *
     * @throws ValidationError, INSUFFICIENT_BALANCE, TRANSACTION_EXISTS, BAD_TRANSACTION_ID
     * @returns {Promise<void>}
     */
    private async transferPool(trx: ITransaction, property: string, fromPool: string, toPool: string, amount: number,
                               loanPool?: JackpotLoanPoolConfig) {

        if (amount < 0) {
            return Promise.reject(new ValidationError("Negative amount"));
        }

        const jpWallet: IWallet = await trx.getWallet(this.walletKey);

        const jackpotAccounts = jpWallet.accounts;

        const donorAccount: IAccount = jackpotAccounts.get(fromPool);
        const recipientAccount: IAccount = jackpotAccounts.get(toPool);

        const donorPrecision = Number(donorAccount.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);
        const recipientPrecision = Number(recipientAccount.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);

        // Calculate finally charge amount according precision
        const amountWithDonorPrecision = toMinorUnits(amount, donorPrecision);
        const amountWithRecipientPrecision = toMinorUnits(amount, recipientPrecision);

        let actualDonorAmountToCharge = amountWithDonorPrecision;
        let actualRecipientAmountToCharge = amountWithRecipientPrecision;

        if (loanPool) {

            const minLoanVal = (loanPool.lowerValue === undefined) ?
                               Number.MIN_SAFE_INTEGER : toMinorUnits(loanPool.lowerValue, donorPrecision);

            const loanProperty = loanPool.property;

            switch (loanPool.poolId) {
                case fromPool: // In this case we can take from loan pool

                    const availableDonorMoney = +donorAccount.get(property) || 0;
                    if (availableDonorMoney - amountWithDonorPrecision < 0) {
                        // We have no available money in donor pool. so take insufficient part from loan pool.
                        actualDonorAmountToCharge = availableDonorMoney;
                        const loanAmount = amountWithDonorPrecision - availableDonorMoney;
                        donorAccount.inc(loanProperty, -loanAmount, TRANSACTION_TYPE_TAKE_LOAN, minLoanVal);
                    }

                    break;
                case toPool: // In this case we can fill up loan pool

                    const currentLoanMoney = +recipientAccount.get(loanProperty) || 0;

                    if (currentLoanMoney < 0) {
                        // We have debt in loan pool. so we need fill up loan pool first
                        const recipientMoneyRemainder = amountWithRecipientPrecision + currentLoanMoney;
                        let loanRefundAmount = 0;
                        if (recipientMoneyRemainder > 0) { // We still have money to transfer back after loan refund
                            loanRefundAmount = -currentLoanMoney;
                            actualRecipientAmountToCharge = recipientMoneyRemainder;
                        } else { // All money goes to loan refund
                            loanRefundAmount = amountWithRecipientPrecision;
                            actualRecipientAmountToCharge = 0;
                        }

                        recipientAccount.inc(loanProperty, loanRefundAmount, TRANSACTION_TYPE_REFUND_LOAN, 0);
                    }

                    break;
                default:
                    return Promise.reject(new JpConfigError("Check loanPool config in jp definitions"));
            }

        }

        donorAccount.inc(property, -actualDonorAmountToCharge, TRANSACTION_TYPE_TAKE, 0);
        recipientAccount.inc(property, actualRecipientAmountToCharge, TRANSACTION_TYPE_GIVE);
        this.incrementSeqId(jpWallet, "transfer");

        await trx.commit();
    }

    /**
     * Transfer all amount from one pool to another within one jackpot by property name.
     * Will be repeated until full charge is processed or zero balance has been reached in donor pool.
     * Also it will throw INSUFFICIENT_BALANCE if depth of retries has been reached FULL_TRANSFER_TO_POOL_MAX_RETRIES
     *
     * @param {ITransaction} trx one-off transaction for this operation.
     * @param {string} property propertyKey for amount storage to transfer from-to
     * @param {string} fromPool donor pool Id
     * @param {string} toPool recipient pool Id
     * @param {number} depth depth of recursion ie. retry count
     *
     * @throws INSUFFICIENT_BALANCE, TRANSACTION_EXISTS, BAD_TRANSACTION_ID
     * @returns {Promise<void>}
     */
    private async transferAllPool(trx: ITransaction, property: string, fromPool: string, toPool: string, depth = 0) {

        if (++depth > FULL_TRANSFER_TO_POOL_MAX_RETRIES) {
            return Promise.reject(INSUFFICIENT_BALANCE);
        }

        const jpWallet: IWallet = await trx.getWallet(this.walletKey);

        const jackpotAccounts = jpWallet.accounts;
        const donorAccount: IAccount = jackpotAccounts.get(fromPool);
        const recipientAccount: IAccount = jackpotAccounts.get(toPool);

        const donorAmount = Number(donorAccount.get(property) || 0);

        if (donorAmount === 0) {
            return;
        }

        const donorPrecision = Number(donorAccount.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);
        const recipientPrecision = Number(recipientAccount.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);

        // Calculate recipient amount according precisions
        const recipientAmount = (donorPrecision !== recipientPrecision) ?
                                toMinorUnits(toMajorUnits(donorAmount, donorPrecision), recipientPrecision) :
                                donorAmount;

        donorAccount.inc(property, -donorAmount, TRANSACTION_TYPE_TAKE, 0);
        recipientAccount.inc(property, recipientAmount, TRANSACTION_TYPE_GIVE);
        this.incrementSeqId(jpWallet, "transfer");

        try {
            return await trx.commit();
        } catch (error) {
            if (error === INSUFFICIENT_BALANCE) {
                return await this.transferAllPool(trx, property, fromPool, toPool, depth);
            } else {
                return Promise.reject(error);
            }
        }
    }

    @measure({ name: "jackpotModule.getPoolState", isAsync: true, debugOnly: true })
    public async getPoolState(poolId: string): Promise<PoolState> {
        const jpWallet: IWallet = await this.wallet.get(this.walletKey);
        return this.getPoolInfo(jpWallet, poolId);
    }

    @measure({ name: "jackpotModule.getAllPoolsState", isAsync: true, debugOnly: true })
    public async getAllPoolsState(): Promise<PoolState[]> {
        const jpWallet: IWallet = await this.wallet.get(this.walletKey);
        return this.instance.definition.list.map((definition) => this.getPoolInfo(jpWallet, definition.id));
    }

    private getPoolInfo(wallet: IWallet, poolId: string): PoolState {
        const account: IAccount = wallet.accounts.get(poolId);
        const poolDef = this.instance.definition.list.find((definition) => definition.id === poolId);

        const precision = Number(account.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);
        const seedRaw = Number(account.get(JP_WALLET_SEED) || 0);
        const progressiveRaw = Number(account.get(JP_WALLET_PROGRESSIVE) || 0);

        const initialSeed = (poolDef.seed && poolDef.seed.amount) || 0;
        const seed = toMajorUnits(seedRaw, precision);
        const progressive = toMajorUnits(progressiveRaw, precision);

        return { poolId, initialSeed, seed, progressive };
    }

    private setAccountProperties(account: IAccount, properties: JackpotPoolProperties): void {
        if (!properties) {
            return;
        }

        for (const prop of Object.keys(properties)) {
            if (JP_WALLET_PROPERTIES.includes(prop)) {
                throw new Errors.InvalidJackpotResult();
            }
            const value = properties[prop];
            account.set(prop, value, "set", true);
        }
    }

    private updateCurrentTicker(jpWallet: IWallet) {
        this.currentTicker = this.getTickerFromWallet(jpWallet);
    }

    private getTickerFromWallet(jpWallet: IWallet, majorUnits = true): Ticker {
        const result: Ticker = {
            id: this.instance.id,
            currency: this.instance.definition.currency,
            pools: {},
            seqId: +jpWallet.accounts.get(META_INF_ACCOUNT).get(META_INF_SEQ_ID) || 0
        };

        const disabled = +jpWallet.accounts.get(META_INF_ACCOUNT).get(META_INF_DISABLED) || 0;
        if (disabled) {
            result.isDisabled = true;
        }

        // run on each of the jackpot pools
        for (const item of this.instance.definition.list) {
            // get account for a specific pool
            const account: IAccount = jpWallet.accounts.get(item.id);

            // add the current balance to the pools result
            const precision = Number(account.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);
            let seed = Number(account.get(JP_WALLET_SEED) || 0);
            let progressive = Number(account.get(JP_WALLET_PROGRESSIVE) || 0);

            if (majorUnits) {
                seed = toMajorUnits(seed, precision);
                progressive = toMajorUnits(progressive, precision);
            }

            result.pools[item.id] = {
                seed: seed,
                progressive: progressive
            };

            for (const prop of Object.keys(account.data)) {
                if (!JP_WALLET_PROPERTIES.includes(prop)) {
                    result.pools[item.id][prop] = +account.data[prop];
                }
            }
        }

        return result;
    }

    private copyTicker(ticker: Ticker): Ticker {
        const copy = { ...ticker };
        // deep copy
        Object.keys(ticker.pools).forEach(pool => {
            copy.pools[pool] = { ...(ticker.pools[pool]) };
        });
        return copy;
    }

    public async disable(): Promise<boolean> {
        const transaction: ITransaction = await this.wallet.startTransaction(await this.wallet.generateTransactionId());
        const jpWallet: IWallet = await transaction.getWallet(this.walletKey);
        this.markDisabled(jpWallet, "disable");
        try {
            await transaction.commit();
        } catch (err) {
            if (err === TRANSACTION_EXISTS) {
                return Promise.reject(new Errors.ValidationError("transaction exists"));
            } else if (err === COMMIT_ATTEMPTS_EXCEEDED) {
                return Promise.reject(new Errors.TooManyJackpotRequests());
            } else if (err === BAD_TRANSACTION_ID) {
                return Promise.reject(new Errors.ValidationError("invalid transactionId"));
            } else if (err === MAX_CAPACITY_REACHED) {
                return true;
            } else if (err === TRANSACTION_IS_PROCESSING) {
                return Promise.reject(new Errors.TransactionIsProcessing());
            } else {
                return Promise.reject(err);
            }
        }

        this.updateCurrentTicker(jpWallet);

        return true;
    }

    public async enable(): Promise<boolean> {
        const transaction: ITransaction = await this.wallet.startTransaction(await this.wallet.generateTransactionId());
        const jpWallet: IWallet = await transaction.getWallet(this.walletKey);
        this.markEnabled(jpWallet, "enable");
        try {
            await transaction.commit();
        } catch (err) {
            if (err === TRANSACTION_EXISTS) {
                return Promise.reject(new Errors.ValidationError("transaction exists"));
            } else if (err === COMMIT_ATTEMPTS_EXCEEDED) {
                return Promise.reject(new Errors.TooManyJackpotRequests());
            } else if (err === BAD_TRANSACTION_ID) {
                return Promise.reject(new Errors.ValidationError("invalid transactionId"));
            } else if (err === INSUFFICIENT_BALANCE) {
                return true;
            } else if (err === MAX_CAPACITY_REACHED) {
                return true;
            } else if (err === TRANSACTION_IS_PROCESSING) {
                return Promise.reject(new Errors.TransactionIsProcessing());
            } else {
                return Promise.reject(err);
            }
        }

        this.updateCurrentTicker(jpWallet);

        return true;
    }

    public async isDisabled(): Promise<boolean> {
        const jpWallet: IWallet = await this.wallet.get(this.walletKey);
        return this.isWalletDisabled(jpWallet);
    }

    // Race conditions covered by seed min value in wallet operation
    @measure({ name: "jackpotModule.processPoolManipulations", isAsync: true, debugOnly: true })
    public async processPoolManipulations(trxId: string, externalId: string, roundId: string,
                                          manipulations: PoolManipulation[]): Promise<void> {
        const transferParams: LocalWalletTransferParams = {
            ...this.baseParams,
            precision: undefined,    // will be defined later
            transfers: []            // will be defined later
        };
        const trxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: this.instance.internalId + TRANSFER_TRX_OPERATION_ID_MARKER,
            operationName: LOCAL_WALLET_OPERATION_NAME.internalTransfer,
            externalTrxId: externalId,
            params: transferParams
        };
        const transaction: ITransaction = await this.wallet.startTransaction(trxId, trxOperation);
        const jpWallet: IWallet = await transaction.getWallet(this.walletKey);
        const trxType = "transfer";

        for (const m of manipulations) {
            const account: IAccount = jpWallet.accounts.get(m.pool);
            const precision = Number(account.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);
            transferParams.precision = precision;
            if (m.type === PoolManipulationType.InternalTransferFromSeed) {
                const manipulation: InternalTransferPoolManipulation = m as InternalTransferPoolManipulation;
                const restInSeed = toMinorUnits(manipulation.payload.restValue, precision);
                const seed = Number(account.get(JP_WALLET_SEED) || 0);
                if (seed > restInSeed) {
                    const progressive = Number(account.get(JP_WALLET_PROGRESSIVE) || 0);
                    const releaseSeed = seed - restInSeed;
                    account.inc(JP_WALLET_SEED, -releaseSeed, trxType, restInSeed);
                    account.inc(JP_WALLET_PROGRESSIVE, releaseSeed, trxType, 0);

                    log.info({
                        releaseSeed: releaseSeed,
                        seed,
                        progressive,
                        precision,
                        playerCode: this.baseParams.playerCode
                    }, "Internal transfer to progressive");

                    const transfer: InternalTransfer = {
                        pool: m.pool,
                        seed: toMajorUnits(releaseSeed, precision),
                        progressive: 0,
                        totalSeed: toMajorUnits(seed, precision),
                        totalProgressive: toMajorUnits(progressive, precision),
                        transferPool: m.pool
                    };
                    transferParams.transfers.push(transfer);
                }
            }
            if (m.type === PoolManipulationType.TransferBetweenPools) {
                const manipulation = m as TransferBetweenPoolsManipulation;
                const seed = Number(account.get(JP_WALLET_SEED) || 0);
                const progressive = Number(account.get(JP_WALLET_PROGRESSIVE) || 0);

                let params: TransferBetweenPoolsParams = { jpWallet, manipulation, seed, progressive };

                switch (manipulation.transferType) {
                    case TransferType.Everything:
                        // we don't support this type yet.
                        break;
                    case TransferType.Positive:
                        if (seed > 0 || progressive > 0) {
                            const releaseSeed = seed > 0 ? seed : 0;
                            const releaseProgressive = progressive > 0 ? progressive : 0;
                            params = { ...params, releaseSeed, releaseProgressive };
                            const transfer = this.doTransferBetweenPools(params);
                            transferParams.transfers.push(transfer);
                        }
                        break;
                    case TransferType.AboveInitial:
                        const initialSeed = toMinorUnits(manipulation?.payload?.initialSeed || 0, precision);
                        if (seed > initialSeed || progressive > 0) {
                            const releaseSeed = seed > initialSeed ? seed - initialSeed : 0;
                            const releaseProgressive = progressive > 0 ? progressive : 0;
                            params = { ...params, releaseSeed, releaseProgressive };
                            const transfer = this.doTransferBetweenPools(params);
                            transferParams.transfers.push(transfer);
                        }
                        break;
                }
            }
        }
        // Additional check for concurrency, when nothing to transfer just we will not commit transaction
        if (transferParams.transfers.length) {
            this.incrementSeqId(jpWallet, trxType);
            this.checkEnabled(jpWallet, trxType);
            try {
                await transaction.commit();
            } catch (err) {
                if (err === TRANSACTION_EXISTS) {
                    log.info("Transfer skipped because transaction exists");
                } else if (err === COMMIT_ATTEMPTS_EXCEEDED) {
                    return Promise.reject(new Errors.TooManyJackpotRequests());
                } else if (err === BAD_TRANSACTION_ID) {
                    return Promise.reject(new Errors.ValidationError("invalid transactionId"));
                } else if (err === MAX_CAPACITY_REACHED) {
                    const disabled = this.isWalletDisabled(jpWallet) || await this.isDisabled();
                    if (disabled) {
                        log.info({ manipulations }, "Skip win on disabled jackpot");
                    } else {
                        return Promise.reject(err);
                    }
                } else if (err === TRANSACTION_IS_PROCESSING) {
                    return Promise.reject(new Errors.TransactionIsProcessing());
                } else if (err === INSUFFICIENT_BALANCE) {
                    return Promise.reject(new Errors.InsufficientJackpotBalance());
                } else {
                    return Promise.reject(err);
                }
            }
            this.updateCurrentTicker(jpWallet);
        } else {
            log.info("Internal transfer skipped because it's already done by other player");
        }
    }

    private doTransferBetweenPools({
                                       jpWallet,
                                       manipulation,
                                       seed,
                                       progressive,
                                       releaseSeed,
                                       releaseProgressive
                                   }: TransferBetweenPoolsParams): InternalTransfer {

        const account: IAccount = jpWallet.accounts.get(manipulation.pool);
        const precision = Number(account.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);

        const toAccount: IAccount = jpWallet.accounts.get(manipulation.toPool);
        const toPrecision = Number(toAccount.get(JP_WALLET_PRECISION) || DEFAULT_PRECISION);

        const toProgressive = Number(toAccount.get(JP_WALLET_PROGRESSIVE) || 0);

        const trxType = "transfer";
        if (releaseSeed > 0) {
            account.inc(JP_WALLET_SEED, -releaseSeed, trxType, 0);
        }
        if (releaseProgressive > 0) {
            account.inc(JP_WALLET_PROGRESSIVE, -releaseProgressive, trxType, 0);
        }
        if ((releaseSeed + releaseProgressive) > 0) {
            toAccount.inc(JP_WALLET_PROGRESSIVE, releaseSeed + releaseProgressive, trxType, 0);
        }

        log.info({
            releaseSeed,
            releaseProgressive,
            seed,
            progressive,
            precision,
            playerCode: this.baseParams.playerCode
        }, "Transfer between pools");

        const transfer: InternalTransfer = {
            pool: manipulation.pool,
            seed: toMajorUnits(releaseSeed, precision),
            progressive: toMajorUnits(releaseProgressive, precision),
            totalSeed: toMajorUnits(seed, precision),
            totalProgressive: toMajorUnits(progressive, precision),
            transferPool: manipulation.toPool,
            transferPoolProgressive: toMajorUnits(toProgressive, toPrecision)
        };

        return transfer;
    }

    public async applyPropertyChanges(changes: PropertyChange[]): Promise<string[]> {
        this.validateChanges(changes);
        const transaction: ITransaction = await this.wallet.startTransaction(await this.wallet.generateTransactionId());
        const jpWallet: IWallet = await transaction.getWallet(this.walletKey);
        const result = [];
        const trxType = "game-action";
        changes.forEach(c => {
            const account: IAccount = jpWallet.accounts.get(c.pool);
            const currentValue: number = Number(account.get(c.property)) || 0;
            const change: number = c.amount;
            if (c.operation.operation === Operation.INC && currentValue + change <= c.operation.max) {
                account.inc(c.property, change, trxType, Number.MIN_SAFE_INTEGER, c.operation.max);
                result.push(`${c.pool}.${c.property}`);
            } else if (c.operation.operation === Operation.DEC && currentValue - change >= c.operation.min) {
                account.inc(c.property, -change, trxType, c.operation.min);
                result.push(`${c.pool}.${c.property}`);
            }
        });

        if (result.length) {
            this.incrementSeqId(jpWallet, trxType);
            this.checkEnabled(jpWallet, trxType);
        }
        try {
            await transaction.commit();
        } catch (err) {
            log.error("Error during processing game action", err);
            return Promise.reject(err);
        }

        this.updateCurrentTicker(jpWallet);
        return result;
    }

    private validateChanges(changes: PropertyChange[]): void {
        changes.forEach(c => {
            if (!c.pool || !c.property || !c.operation || !c.amount) {
                throw new Errors.ValidationError("Missing required properties in changes");
            }
            if (!Number.isInteger(c.amount) || c.amount <= 0) {
                throw new Errors.ValidationError("change.amount should be positive");
            }
            if (JP_WALLET_PROPERTIES.includes(c.property)) {
                throw new Errors.ValidationError(`Modification of ${c.property} is not allowed`);
            }
            if (c.operation.operation === Operation.DEC && !Number.isInteger(c.operation.min)
                || c.operation.operation === Operation.INC && !Number.isInteger(c.operation.max)) {
                throw new Errors.ValidationError(`Wrong operation ${c.operation}`);
            }
        });
    }

    private incrementSeqId(jpWallet: IWallet, change: string) {
        jpWallet.accounts.get(META_INF_ACCOUNT).inc(META_INF_SEQ_ID, 1, change);
    }

    private checkEnabled(jpWallet: IWallet, change: string) {
        jpWallet.accounts.get(META_INF_ACCOUNT).inc(META_INF_DISABLED, 0, change, 0, 0);
    }

    private markDisabled(jpWallet: IWallet, change: string) {
        jpWallet.accounts.get(META_INF_ACCOUNT).inc(META_INF_DISABLED, 1, change, 1, 1);
    }

    private markEnabled(jpWallet: IWallet, change: string) {
        jpWallet.accounts.get(META_INF_ACCOUNT).inc(META_INF_DISABLED, -1, change, 0, 0);
    }

    private isWalletDisabled(jpWallet: IWallet): boolean {
        const value = +jpWallet.accounts.get(META_INF_ACCOUNT).get(META_INF_DISABLED) || 0;
        return value === 1;
    }

    private async markDisabledWithAudit(jpWallet: IWallet, change: string) {
        this.markDisabled(jpWallet, change);
        try {
            await JackpotAuditService.auditJackpot(this.instance,
                JackpotAuditType.DISABLE_ON_WIN,
                this.audit);
        } catch (err) {
            log.error(err, `Error during audit disabling jackpot instance after ${change}`);
        }
    }
}
