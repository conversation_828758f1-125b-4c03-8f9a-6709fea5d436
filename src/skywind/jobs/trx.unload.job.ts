// this should be the first line
import { kafka, logging, measures } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
// this should be the third line
import config from "../config";
logging.setUpOutput({ type: config.loggingOutput, logLevel: config.logLevel });

import { startTrxConsuming } from "@skywind-group/sw-wallet";
import { getWalletConfig } from "../services/wallet.service";
import logger from "../utils/logger";
import * as fs from "fs";

import * as ContributionModel from "../history/contribution.log";
import * as WinModel from "../history/win.log";
import * as TransferModel from "../history/transfer.log";
import { ContributionConsumer } from "../history/contribution.consumer";
import { WinConsumer } from "../history/win.consumer";
import { BaseConsumer } from "../history/base.consumer";
import { KafkaConsumer } from "../history/kafka/kafka.consumer";
import { startServer as startInternalServer } from "../serverInternal";
import { TransferConsumer } from "../history/transfer.consumer";

const version = fs.readFileSync(__dirname + "/../version", "utf8");
const log = logger("trx-unloader");

process.on("uncaughtException", (err) => {
    log.error(err, "uncaughtException occurred. Server will die...");
    process.exit(1);
});

process.on("unhandledRejection", (err, promise) => {
    log.error(err, "unhandledRejection", promise);
});

export async function start() {
    log.info("Start trx unloader");
    log.info("AppVersion: ", version);
    if (!config.isProduction()) {
        await ContributionModel.contributionLogDb.sync();
        await ContributionModel.remoteContributionLogDb.sync();
        await WinModel.winLogDb.sync();
        await WinModel.remoteWinLogDb.sync();
        await TransferModel.transferLogDb.sync();
        await TransferModel.remoteTransferLogDb.sync();
    }

    const consumers: BaseConsumer[] = [new ContributionConsumer(), new WinConsumer(), new TransferConsumer()];

    const kafkaUnloader = config.kafkaUnloader;

    if (kafkaUnloader.enabled) {
        try {
            consumers.push(new KafkaConsumer(await kafka.createWriter(kafkaUnloader, logger("kafka-writer"))));
            log.info("KafkaConsumer was successfully added.");
        } catch (err) {
            log.warn(err);
        }
    }

    (async () => startTrxConsuming(getWalletConfig(), consumers))();
}

(async () => {
    await start();
    await startInternalServer();
})();
