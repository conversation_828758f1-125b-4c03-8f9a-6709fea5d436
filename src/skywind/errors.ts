import { ITrxData } from "@skywind-group/sw-wallet";

export interface SWError {
    status: number;
    code: number;
    message: string;
}

export class SWBaseError extends Error implements SWError {
    constructor(public status: number, public code: number, message: string) {
        super(message);
    }
}

export class InternalServerError extends SWBaseError {
    constructor() {
        super(500, 1, "JPN internal server error");
    }
}

export class OperationForbidden extends SWBaseError {
    constructor() {
        super(403, 2, "Operation forbidden");
    }
}

export class ValidationError extends SWBaseError {
    constructor(reason: string | string[]) {
        super(400, 3, `Validation error: ${reason}`);
    }
}

export class BadContextState extends SWBaseError {
    constructor() {
        super(409, 4, "Bad jackpot context state");
    }
}

export class TooManyJackpotRequests extends SWBaseError {
    constructor() {
        super(500, 5, "Too many jackpot requests");
    }
}

export class RegionNotFoundError extends SWBaseError {
    constructor() {
        super(404, 6, "Region not found");
    }
}

export class OperationNotSupported extends SWBaseError {
    constructor() {
        super(400, 7, "Operation is not supported");
    }
}

export class TransactionIsProcessing extends SWBaseError {
    constructor() {
        super(500, 8, "Transaction is processing");
    }
}

export class TokenIsMissing extends SWBaseError {
    constructor() {
        super(400, 11, "Token is missing");
    }
}

export class TokenNotValid extends SWBaseError {
    constructor() {
        super(400, 12, "Token is not valid");
    }
}

export class TokenExpired extends SWBaseError {
    constructor() {
        super(400, 13, "Token has expired");
    }
}

export class MiniGameNotFound extends SWBaseError {
    constructor() {
        super(404, 15, "Mini-game not found");
    }
}

export class JackpotGameNotFound extends SWBaseError {
    constructor(gameId: string) {
        super(404, 18, "Jackpot game not found: " + gameId);
    }
}

export class InsufficientJackpotBalance extends SWBaseError {
    constructor() {
        super(400, 19, "Insufficient jackpot balance");
    }
}

export class ConcurrentJackpotWin extends SWBaseError {
    constructor() {
        super(500, 20, "Concurrent jackpot win");
    }
}

export class JackpotTypeNotFound extends SWBaseError {
    constructor(name: string) {
        super(404, 23, "Jackpot type not found: " + name);
    }
}

export class JackpotInstanceNotFound extends SWBaseError {
    constructor(id: string) {
        super(404, 24, "Jackpot instance not found: " + id);
    }
}

export class InvalidJackpotResult extends SWBaseError {
    constructor() {
        super(409, 25, "Invalid jackpot result");
    }
}

export class JackpotTypeAlreadyExist extends SWBaseError {
    constructor() {
        super(400, 26, "Jackpot type already exists");
    }
}

export class JackpotInstanceAlreadyExist extends SWBaseError {
    constructor() {
        super(400, 27, "Jackpot instance already exists");
    }
}

export class CurrencyNotFoundError extends SWBaseError {
    constructor(currency: string) {
        super(400, 28, `Currency not found : ${currency}`);
    }
}

export class NotAuthorizedJackpotError extends SWBaseError {
    constructor(jpId: string) {
        super(400, 29, `Jackpot with id: ${jpId} is not authorized`);
    }
}

export class MalformedJsonError extends SWBaseError {
    constructor(reason: string) {
        // code 674 - For compatibility with management-api
        super(400, 674, `Malformed JSON : ${reason}`);
    }
}

export class WinJackpotNotSupported extends SWBaseError {
    constructor() {
        super(400, 30, `Win Jackpot is not supported for this game.`);
    }
}

export class WinMiniGameNotSupported extends SWBaseError {
    constructor() {
        super(400, 31, `Win MiniGame is not supported for this game`);
    }
}

export class RequiredFieldError extends SWBaseError {
    constructor(reason: string) {
        super (400, 32, reason);
    }
}

export class DuplicateTransactionError extends SWBaseError {
    constructor() {
        super (208, 33, "Duplicate transaction error");
    }
}

export class JpFeatureForbidden extends SWBaseError {
    constructor(feature: string) {
        super(400, 34, `This jackpot doesn't support this feature: ${feature}`);
    }
}

export class WinWithoutContribution extends SWBaseError {
    constructor() {
        super(400, 35, "Cannot win without contribution");
    }
}

export class JpTypeDistinguishError extends SWBaseError {
    constructor(reasons: string[]) {
        super(409, 36, "Jp type distinguish :" + reasons.join(",\n\n"));
    }
}

export class UnableToLoadFileError extends SWBaseError {
    constructor(path: string, err: any) {
        super(404, 37, `Unable to load file :${path} ; ${err ? err : ""}`);
    }
}

export class NoJackpotTypeConfigError extends SWBaseError {
    constructor(typeName: string) {
        super(404, 38, `Jackpot type configuration does not exist in jpn-games for ${typeName}`);
    }
}

export class JpConfigError extends SWBaseError {
    constructor(reason: string) {
        super(400, 39, `Configuration Error: ${reason}`);
    }
}

export class InternalServerTokenError extends SWBaseError {
    constructor() {
        super(401, 40, "Internal server token error");
    }
}

export class InternalServerTokenExpiredError extends SWBaseError {
    constructor() {
        super(400, 41, "Internal server token expired");
    }
}

export class RemoteJackpotWalletConnectionError extends SWBaseError {
    constructor() {
        super(500, 42, "Can't connect to remote jackpot wallet");
    }
}

export class JackpotDisabledError extends SWBaseError {
    constructor() {
        super(400, 43, "Jackpot already disabled");
    }
}

export class JackpotCantBeDisabledError extends SWBaseError {
    constructor() {
        super(400, 44, "Jackpot cannot be enabled/disabled");
    }
}

export class RemoteJackpotWalletError extends SWBaseError {
    constructor(status: number, response) {
        super(status || 500,
            response && response.code || 43,
            response && response.message || "Can't connect to remote jackpot wallet");
    }
}

export class MigratedJackpotWalletTrxError extends SWBaseError {
    constructor(public trxData: ITrxData) {
        super(500, 44, "Jackpot wallet migrated");
    }
}

export class DeferredContributeInvalidDataError extends SWBaseError {
    constructor() {
        super(500, 45, "Invalid data for deferred contribution");
    }
}

export class TestJackpotError extends SWBaseError {
    constructor() {
        super(400, 46, "Test jackpot is not valid");
    }
}

export class RealPlayerUsesTestJackpotError extends SWBaseError {
    constructor() {
        super(400, 47, "Real player uses the test jackpot");
    }
}

export class TestPlayerUsesRealJackpotError extends SWBaseError {
    constructor() {
        super(400, 48, "Test player uses the real jackpot");
    }
}

export class UnsupportedPoolManipulation extends SWBaseError {
    constructor() {
        super(409, 27, "Unsupported pool manipulation");
    }
}

export function isSWError(err) {
    return err.status && err.code && err.message;
}
