import { default as logger } from "./utils/logger";
import { Application } from "express";
import * as http from "http";
import { create as createApplication } from "./express";
import { defineRoutes } from "./api/routers";
import { lazy, logging } from "@skywind-group/sw-utils";
import { loadGames } from "./services/game.service";
import { init as initCurrencyService } from "./services/currency.service";
import JackpotTypeService from "./services/jackpot.type";
import db from "./services/db.service";
import config from "./config";
import * as fs from "fs";
import { RemoteRequestQueue } from "./services/remoteRequestQueue";
import { getRedisPool, remoteTickerPool } from "./storage/redis";
import { RemoteTickerService } from "./services/remoteTicker.service";
import JackpotInstanceService from "./services/jackpot.instance";
import * as v8 from "v8";
import Logger = logging.Logger;

const version = fs.readFileSync(__dirname + "/version", "utf8");

export const application = lazy(() => defineRoutes(createApplication()));

export async function startServer(port = 5000): Promise<http.Server> {
    return startApplicationServer(application.get(), "JPN", port);
}

export async function startApplicationServer(app: Application,
                                             name: string,
                                             port: number): Promise<http.Server> {
    setUpServerName(name);
    installProcessHandlers(logger());

    const log = logger("start-up");

    if (!config.isProduction() && config.db.syncOnStart) {
        log.info("Sync on start DB");
        await db.sync();
    }

    log.info("Load jackpot games");
    loadGames();

    JackpotInstanceService.initCache();

    if (config.createJpTypesOnStart) {
        log.info("Lookup jackpot types");
        await JackpotTypeService.createAllDefined();
    }

    log.info("Init currency rates");
    await initCurrencyService();

    startRemoteRequestQueue();

    return new Promise<http.Server>((resolve) => {
        const server: http.Server = http.createServer(app);
        server.listen(port, null, () => {
            log.info(`${name} API listening on: ${port}`);
            log.info(`${name} AppVersion: ${version}`);
            server.timeout = config.server.timeout;
            server.keepAliveTimeout = config.server.keepAliveTimeout;
            resolve(server);
        });
    });
}

function installProcessHandlers(log: Logger) {

    // omit console.log for production environment
    if (config.isProduction()) {
        // eslint-disable-next-line no-console
        console.log = () => {
        };
    }

    process.on("uncaughtException", (err) => {
        log.error(err, "uncaughtException occurred. Server continuing to work");
    });

    process.on("unhandledRejection", (err, promise) => {
        log.error(err, "unhandledRejection", promise);
    });

    process.on("SIGUSR1", () => {
        v8.writeHeapSnapshot(`/tmp/${Date.now()}.heapsnapshot`);
    });
}

function setUpServerName(name: string) {
    config.server.setName(name);
}

function startRemoteRequestQueue() {
    const tickerService = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);
    const queue = new RemoteRequestQueue(getRedisPool(), JackpotInstanceService, tickerService);
    return queue.retransmit();
}
