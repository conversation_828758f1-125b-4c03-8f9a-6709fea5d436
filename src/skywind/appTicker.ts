// this should be the first line
import { measures, logging } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
// this should be the third line
import config from "./config";
logging.setUpOutput({ type: config.loggingOutput, logLevel: config.logLevel });
logging.setRootLogger("sw-jpn-ticker-app");

import { startServer } from "./jackpotTickerServer";
import { startServer as startInternalServer } from "./serverInternal";

(async () => {
    await startServer();
    await startInternalServer();
})();
