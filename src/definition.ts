import {
    IChangeInfo,
    IExternalTransaction,
    IOperation,
    ITransaction,
    ITrxData,
    ITrxId,
    IWallet,
} from "@skywind-group/sw-wallet";
import { JackpotInstance, JackpotInstanceInfo, JackpotInternalInstance } from "./skywind/api/model";
import { JackpotResult, PlayerInformation } from "@skywind-group/sw-jpn-core";
import { EGPJackpotTickerResponse, EGPJackpotInfoResponse } from "@skywind-group/sw-gameprovider-adapter-core";
import { LocalWalletContributionParams, LocalWalletWinParams, } from "./skywind/modules/walletParams";

export interface JackpotLookup {
    findInternal(id: string): Promise<JackpotInternalInstance>;
    findOrCreateTest(id: string): Promise<JackpotInternalInstance>;
    create(info: JackpotInstance): Promise<JackpotInstance>;
}

export interface ExternalJackpotLookup {
    getExternalTickers(gameProviderCode: string, ids: string[], currency: string): Promise<EGPJackpotTickerResponse>
    getExternalJackpots(gameProviderCode: string, ids: string[]): Promise<EGPJackpotInfoResponse>
}

export interface JackpotApplyPendingPool {
    applyPendingPool(id: string, poolId: string): Promise<JackpotInstance>;
}

export interface JackpotUpdateInfo {
    updateInfo(id: string, update: JackpotInstanceInfo): Promise<JackpotInstance>;
}

export interface WalletService {
    get(key: string): Promise<IWallet>;
    startTransaction(id: string, operation?: IOperation): Promise<ITransaction>;
    generateTransactionId(): Promise<string>;
    parseTransactionId(trxId: string): Promise<ITrxId>;
    findCommittedTransaction(trxId: string, operationId?: number): Promise<ITrxData>;
    saveExternalTransaction(transaction: IExternalTransaction): Promise<IChangeInfo[]>;
}

export function getWalletKey(instance: JackpotInternalInstance): string {
    return `jackpot:${instance.id}:${instance.definition.currency}`;
}

export function getPoolTitle(instance: JackpotInternalInstance, poolId: string): string {
    for (const pool of instance.definition.list) {
        if (pool.id === poolId) {
            return pool.title || poolId;
        }
    }
    return poolId;
}

export enum JackpotStatus {
    MINI_GAME = 1, WON = 2, PAID = 3
}

export enum JackpotWinType {
    PLAYER = "player", TRANSFER = "transfer"
}

export interface JackpotWin {
    type: JackpotWinType;
    pool: string;           // jackpot pool
    amount: number;         // win amount in jackpot currency
    seed: number;           // amount to be decremented from seed pot
    progressive: number;    // amount to be decremented from progressive pot
    properties?: { [prop: string]: number }; // batch of values to set on jackpot pool
    endedPool?: string;     // initial pool that was ended
}

export interface JackpotPlayerWin extends JackpotWin {
    type: JackpotWinType.PLAYER;
    playerAmount: number;   // win amount in player currency
    exchangeRate: number;   // exchange rate
}

export interface JackpotWinTransfer extends JackpotWin {
    type: JackpotWinType.TRANSFER;
    transferPool: string;  // jackpot pool where to transfer seed/progressive
}

export interface JackpotMiniGameContext {
}

export interface JackpotContext {
    jackpotId: string;
    transactionId: string;
    externalId?: string;
    playerInfo: PlayerInformation;
    roundId: string;
    win?: JackpotWin | JackpotWin[];
    miniGame?: JackpotMiniGameContext;
    status: JackpotStatus;
    result: JackpotResult;
    version: number;
    betAmount?: number;
    contributionParams?: LocalWalletContributionParams;
    winParams?: LocalWalletWinParams;
}

export const CURRENT_JACKPOT_CONTEXT_VERSION: number = 2;

export interface JackpotContextService {
    find(jpId: string, playerInfo: PlayerInformation, transactionId: string): Promise<JackpotContext>;
    update(context: JackpotContext): Promise<void>;
    remove(context: JackpotContext): Promise<void>;
}
