import {
    IWallet,
    ITransaction,
    IAccount,
    IAccounts,
    IMultiIncChange,
    IIncrementalChange,
    IChanges,
    ITrxId,
    IOperation,
    IChangeInfo, ITrxData, IExternalTransaction, ISetChange,
} from "@skywind-group/sw-wallet";
import { WalletService } from "../../definition";
import { logContribution, logWin, PovWalletChanges } from "./wallet.history";

const wallets = {};

class WalletTest implements IWallet {
    public data: any = {
        accounts: {},
    };
    public accounts: IAccounts;
    public changes: IChanges = new PovWalletChanges();
    private newWallet: boolean = false;

    constructor(public key: string) {
        this.newWallet = true;
        this.accounts = new AccountsTest(this.data.accounts, this.changes);
    }

    public isNew(): boolean {
        return this.newWallet;
    }

    public multi(name: string,
                 amount: number,
                 trxType: string,
                 allowReminders?: boolean): IMultiIncChange {
        return null;
    }

    public update(): Promise<any> {
        this.newWallet = false;
        return Promise.resolve();
    }
}

class AccountsTest implements IAccounts {
    public accounts: Array<IAccount> = [];

    constructor(private data: any, private changes: IChanges) {}

    public get(name: string, initial?: any): IAccount {
        for (const item of this.accounts) {
            if (item.name === name) {
                return item;
            }
        }
        const account = new AccountTest(name, initial, this.changes);
        this.data[name] = account.data;
        this.accounts.push(account);
        return account;
    }

    public find(regex: RegExp): Array<IAccount> {
        return null;
    }
}

class AccountTest implements IAccount {
    public data: any;

    constructor(public name: string, initial: any, private changes: IChanges) {
        this.data = initial ? initial : {};
    }

    public inc(property: string,
               amount: number,
               trxType: string,
               minValue?: number,
               maxValue?: number): IIncrementalChange {
        const value = (this.data[property] || 0) + amount;
        this.data[property] = value;
        this.changes.add({
            walletKey: null,
            type: null,
            amount: amount,
            property: property,
            trxType: trxType,
            accounts: [{
                name: this.name,
                minValue: minValue,
                maxValue: maxValue,
            }],
        });
        return null;
    }

    public get(property: string): string|number {
        return this.data[property];
    }

    public set(property: string, value: string | number, trxType: string, ignoreValueChange?: boolean): ISetChange {
        this.data[property] = value;
        return null;
    }
}

class TransactionTest implements ITransaction {
    public trxId: ITrxId;
    public wallets: any;
    public changes: IChanges;

    constructor(public id: string, public operation: IOperation) {
        this.wallets = {};
    }

    public getWallet(key: string): any {
        let wallet: IWallet = this.wallets[key];
        if (!wallet) {
            wallet = wallets[key];
            this.wallets[key] = wallet;
        }
        if (!wallet) {
            wallet = new WalletTest(key);
            wallets[key] = wallet;
            this.wallets[key] = wallet;
        }

        return wallet;
    }

    public setWallet(key: string, wallet: IWallet): any {
        this.wallets[key] = wallet;
    }

    public commit(): Promise<IChangeInfo[]> {
        for (const key of Object.keys(this.wallets)) {
            this.wallets[key].update();
            const changes = this.wallets[key].changes.reset();
            if (this.operation.operationName === "contribute") {
                logContribution(key, this.operation.params, changes);
            } else if (this.operation.operationName === "release") {
                logWin(key, this.operation.params, changes);
            }
        }
        return;
    }
}

export function getWallet(key: string): any {
    let wallet: IWallet = wallets[key];
    if (!wallet) {
        wallet = new WalletTest(key);
        wallets[key] = wallet;
    }
    return wallet;
}

export class PovWallet implements WalletService {

    public get(key: string): Promise<IWallet> {
        return getWallet(key);
    }

    public startTransaction(id: string, operation?: IOperation): any {
        return new TransactionTest(id, operation);
    }

    public generateTransactionId(): any {
        return "pov_trx";
    }

    public parseTransactionId(trxId: string): Promise<ITrxId> {
        return Promise.resolve({
            serialId: 0,
            timestamp: Date.now(),
            publicId: trxId
        });
    }

    public async findCommittedTransaction(trxId: string): Promise<ITrxData> {
        return undefined;
    }

    public async saveExternalTransaction(transaction: IExternalTransaction): Promise<IChangeInfo[]> {
        return undefined;
    }
}

export default new PovWallet();
