import { IChanges, IIncrementalChange } from "@skywind-group/sw-wallet";
import HistoryService from "../history/history.inmemory";
import { PovContributionLog, PovWinLog } from "../history/history";
import { DEFAULT_PRECISION } from "../../skywind/modules/jackpotWallet";
import { META_INF_ACCOUNT } from "../../skywind/modules/walletParams";

export const history = HistoryService;

export class PovWalletChanges implements IChanges {

    private changes: IIncrementalChange[] = [];
    private log = {};

    public add(item: IIncrementalChange) {
        this.changes.push(item);
        const account = item.accounts[0].name;
        if (!this.log[account]) {
            this.log[account] = {
                account: account
            };
        }
        this.log[account][item.property] = item.amount;
    }

    public get(): any {
        return this.changes;
    }

    public reset() {
        const changes = Object.keys(this.log).map(account => this.log[account]);
        this.log = {};
        this.changes = [];
        return changes;
    }
}

function parseJackpotId(walletKey: string): string {
    const items = walletKey.split(":");
    return items[1];
}

export function logContribution(walletKey: string, params: any, changes: any) {
    const jackpotId = parseJackpotId(walletKey);
    const logs: PovContributionLog[] = [];
    for (const change of changes) {
        if (change.account !== META_INF_ACCOUNT) {
            logs.push({
                jackpotId: jackpotId,
                pool: change.account,
                seed: change.seed ? change.seed / DEFAULT_PRECISION : 0,
                progressive: change.progressive ? change.progressive / DEFAULT_PRECISION : 0,
            });
        }
    }
    history.logContributions(logs);
}

export function logWin(walletKey: string, params: any, changes: any, collectFullJpLog?: boolean) {
    const jackpotId = parseJackpotId(walletKey);
    for (const win of params.wins) {
        if (win.account !== META_INF_ACCOUNT) {
            const log: PovWinLog = {
                jackpotId: jackpotId,
                pool: win.pool,
                initialSeed: win.initialSeed,
                seed: win.seed,
                progressive: win.progressive,
                totalSeed: win.totalSeed,
                totalProgressive: win.totalProgressive
            };
            history.logWin(log, collectFullJpLog);
        }
    }
}
