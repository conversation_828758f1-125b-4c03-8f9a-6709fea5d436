import {
    IWallet,
    ITransaction,
    IAccount,
    IAccounts,
    IMultiIncChange,
    IIncrementalChange,
    IChanges,
    ITrxId,
    IOperation,
    IChangeInfo,
    IChangeAccount,
    INSUFFICIENT_BALANCE, ITrxData, IExternalTransaction, ISetChange,
} from "@skywind-group/sw-wallet";
import * as redis from "../../skywind/storage/redis";
import * as path from "path";
import { WalletService } from "../../definition";
import { logContribution, logWin, PovWalletChanges } from "./wallet.history";

let walletClient;
function getWalletClient() {
    if (!walletClient) {
        walletClient = redis.create();
    }
    return walletClient;
}

const walletTrxScript = path.resolve(__dirname, "../../../resources/lua/wallet_trx.lua");

class TestWallet implements IWallet {
    public accounts: IAccounts;
    public changes: IChanges = new PovWalletChanges();
    public sets: { property: string; value: string | number, name: string }[] = [];

    constructor(public key: string, data: any, private newWallet?: boolean) {
        this.accounts = new TestAccounts(data, this.changes, this.sets);
    }

    public get data() {
        const accounts = {};
        for (const account of (this.accounts as TestAccounts).accounts) {
            accounts[account.name] = account.data;
        }
        return {
            accounts: accounts,
        };
    }

    public isNew(): boolean {
        return this.newWallet;
    }

    public multi(name: string,
                 amount: number,
                 trxType: string,
                 allowReminders?: boolean): IMultiIncChange {
        return null;
    }

    public async update(): Promise<any> {
        const increments = [];

        for (const change of this.changes.get() as IIncrementalChange[]) {
            const account = change.accounts[0] as IChangeAccount;
            increments.push("inc", account.name + ":" + change.property, change.amount, account.minValue || 0);
        }

        const sets = [];
        for (const set of this.sets) {
            sets.push("set", `${set.name}:${set.property}`, set.value, 0);
        }

        const reply = await redis.execScript(getWalletClient(), walletTrxScript, [this.key], [...increments, ...sets]);

        if (reply === "insufficient balance") {
            return Promise.reject(INSUFFICIENT_BALANCE);
        }
    }
}

class TestAccounts implements IAccounts {
    public accounts: Array<IAccount> = [];

    constructor(data: any,
                private changes: IChanges,
                private sets: { property: string; value: string | number; name: string }[]) {
        const accountsMap = {};
        for (const key of Object.keys(data)) {
            const parts = key.split(":");
            const account = parts[0];
            if (!accountsMap[account]) {
                accountsMap[account] = new TestAccount(account, 0, changes, sets);
                this.accounts.push(accountsMap[account]);
            }
            accountsMap[account].data[parts[1]] = Number(data[key]);
        }
    }

    public get(name: string, initial?: any): IAccount {
        for (const item of this.accounts) {
            if (item.name === name) {
                return item;
            }
        }
        const account = new TestAccount(name, initial, this.changes, this.sets);
        this.accounts.push(account);
        return account;
    }

    public find(regex: RegExp): Array<IAccount> {
        return null;
    }
}

class TestAccount implements IAccount {
    public data: any;

    constructor(public name: string,
                initial: any,
                private changes: IChanges,
                private sets: { property: string; value: string | number, name: string }[]) {
        this.data = initial ? initial : {};
    }

    public inc(property: string,
               amount: number,
               trxType: string,
               minValue?: number,
               maxValue?: number): IIncrementalChange {
        this.changes.add({
            walletKey: null,
            type: null,
            amount: amount,
            property: property,
            trxType: null,
            accounts: [
                {
                    name: this.name,
                    minValue: minValue,
                    maxValue: maxValue,
                }
            ],
        });
        return null;
    }

    public get(property: string): string | number {
        return this.data[property];
    }

    public set(property: string, value: string | number, trxType: string, ignoreValueChange?: boolean): ISetChange {
        this.sets.push({
            name: this.name,
            property,
            value
        });
        return null;
    }
}

class TestTransaction implements ITransaction {
    public trxId: ITrxId;
    public wallets: Map<string, IWallet>;
    public changes: IChanges;

    constructor(public id: string, public operation: IOperation, private collectFullJpLog: boolean) {
        this.wallets = new Map();
    }

    public async getWallet(key: string): Promise<IWallet> {
        let wallet: IWallet = this.wallets.get(key);
        if (!wallet) {
            wallet = await getWallet(key);
            this.wallets.set(key, wallet);
        }
        return Promise.resolve(wallet);
    }

    public async setWallet(key: string, wallet: IWallet): Promise<void> {
        this.wallets.set(key, wallet);
    }

    public async commit(): Promise<IChangeInfo[]> {
        for (const wallet of this.wallets.values()) {
            await wallet.update();
            const changes = (wallet.changes as PovWalletChanges).reset();
            if (this.operation.operationName === "contribute") {
                logContribution(wallet.key, this.operation.params, changes);
            } else if (this.operation.operationName === "release") {
                logWin(wallet.key, this.operation.params, changes, this.collectFullJpLog);
            }
        }
        return Promise.resolve(undefined);
    }
}

export function getWallet(key: string): Promise<IWallet> {
    return new Promise<IWallet>((resolve, reject) => {
        getWalletClient().hgetall(key, (err, data) => {
            if (err) {
                reject(err);
            } else if (!data || !Object.keys(data).length) {
                resolve(new TestWallet(key, {}, true));
            } else {
                resolve(new TestWallet(key, data, false));
            }
        });
    });
}

export class PovSharedWallet implements WalletService {

    public collectJpFullLog: boolean;

    public get(key: string): Promise<IWallet> {
        return getWallet(key);
    }

    public startTransaction(id: string, operation?: IOperation): Promise<ITransaction> {
        return Promise.resolve(new TestTransaction(id, operation, this.collectJpFullLog));
    }

    public generateTransactionId(): Promise<string> {
        return Promise.resolve("pov_trx");
    }

    public parseTransactionId(trxId: string): Promise<ITrxId> {
        return Promise.resolve({
            serialId: 0,
            timestamp: Date.now(),
            publicId: trxId
        });
    }

    public async findCommittedTransaction(trxId: string): Promise<ITrxData> {
        return undefined;
    }

    public async saveExternalTransaction(transaction: IExternalTransaction): Promise<IChangeInfo[]> {
        return undefined;
    }
}

export default new PovSharedWallet();
