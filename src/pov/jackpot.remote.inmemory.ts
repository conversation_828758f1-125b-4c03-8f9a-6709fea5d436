import { JackpotRegion } from "../skywind/api/model";
import { Ticker } from "@skywind-group/sw-jpn-core";
import {
    RemoteGameFlowRequest,
    RemoteJackpotService
} from "../skywind/services/remoteJackpotService";
import { RemoteServices, RemoteTrxIds } from "../skywind/modules/remoteJackpotModule";
import { RemoteTicker } from "../skywind/services/remoteTicker.service";
import { RemoteRequestScheduler } from "../skywind/services/remoteRequestQueue";
import { JackpotLookup } from "../definition";

export class PovRemoteTrxIds implements RemoteTrxIds {

    public async generateTrxId(region: JackpotRegion): Promise<string> {
        return "global-trx-id";
    }
}

export class PovRemoteTickerService implements RemoteTicker {

    private currencyTicker: Ticker;

    constructor(private jackpotService: RemoteJackpotService) {}

    public async getTicker(jackpotId: string): Promise<Ticker> {
        return this.currencyTicker || this.jackpotService.getTicker(jackpotId);
    }

    public async updateTicker(remoteTicker: Ticker): Promise<Ticker> {
        this.currencyTicker = remoteTicker;
        return this.currencyTicker;
    }
}

export class PovRemoteRequestQueue implements RemoteRequestScheduler {

    constructor(private jackpotService: RemoteJackpotService) {}

    public async schedule(request: RemoteGameFlowRequest): Promise<void> {
        this.jackpotService.processRemoteGameFlow(request);
    }
}

export function createRemoteServices(wallet, jackpotLookup: JackpotLookup): RemoteServices {
    const jackpotService = new RemoteJackpotService(jackpotLookup, wallet);
    return {
        trxIds: new PovRemoteTrxIds(),
        tickerService: new PovRemoteTickerService(jackpotService),
        requestQueue: new PovRemoteRequestQueue(jackpotService)
    };
}
