import { PlayerInformation } from "@skywind-group/sw-jpn-core";
import { JackpotContext, JackpotContextService } from "../definition";

export class JackpotContextPovService implements JackpotContextService {

    private contexts = {};

    public find(jpId: string, playerInfo: PlayerInformation, transactionId: string): Promise<JackpotContext> {
        const contextKey = this.contextKey(jpId, playerInfo, transactionId);
        return this.contexts[contextKey];
    }

    public update(context: JackpotContext): any {
        const contextKey = this.contextKey(context.jackpotId, context.playerInfo, context.transactionId);
        this.contexts[contextKey] = context;
    }

    public remove(context: JackpotContext): any {
        const contextKey = this.contextKey(context.jackpotId, context.playerInfo, context.transactionId);
        this.contexts[contextKey] = undefined;
    }

    private contextKey(jpId: string, player: PlayerInformation, transactionId: string): string {
        return `jackpot:context:${jpId}:${player.brandId}:${player.playerCode}:${transactionId}`;
    }
}

const service: JackpotContextPovService = new JackpotContextPovService();
export default service;
