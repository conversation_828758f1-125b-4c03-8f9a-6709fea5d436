// Override global Promise
import { ExternalJackpotLookup, Jack<PERSON>Apply<PERSON>ending<PERSON><PERSON>, <PERSON><PERSON><PERSON>ookup, JackpotUpdateInfo } from "../definition";

import { PovHistory, PovJackpotHistory } from "./history/history";
import {
    AuthRequest, AuthResponse, ContributionRequest, ContributionResponse, PlayerInformation,
    MiniGameRequest, MiniGameResponse, WinConfirmRequest, WinConfirmResponse, TransactionIdResponse,
    WinJackpotRequest, WinJackpotResponse, CheckWinRequest, CheckWinResponse
} from "@skywind-group/sw-jpn-core";
import { JackpotInstancePovService } from "./jackpot.instance.inmemory";
import * as Errors from "../skywind/errors";
import { loadGames } from "../skywind/services/game.service";
import { init as initCurrencyService } from "../skywind/services/currency.service";
import ContextService from "./jackpot.context.inmemory";
import walletShared from "./wallet/wallet.redis";
import walletInMemory from "./wallet/wallet.inmemory";
import { history as HistoryService } from "./wallet/wallet.history";
import { JackpotService } from "../skywind/services/jackpot.service";
import { getGlobalRandomGenerator } from "../skywind/utils/random";
import * as cluster from "node:cluster";
import { createRemoteServices } from "./jackpot.remote.inmemory";
import { JackpotInstanceInfo } from "../skywind/api/model";
import { EGPJackpotInfoResponse, EGPJackpotTickerResponse } from "@skywind-group/sw-gameprovider-adapter-core";
import * as superagent from "superagent";

export interface JpnSettings {
    api: string;
    auth: PlayerInformation;
    jackpots: { id: string, type: string }[];
    seed?: number;
    collectJpFullLog?: boolean;
    globalJp?: boolean;
}

export interface JPNServer extends PovHistory {
    createJackpot(jackpotId: string, type: string, globalJp?: boolean): Promise<void>;
    auth(req: AuthRequest): Promise<AuthResponse>;
    getTicker(token: string): Promise<any>;
    generateTransactionId(): Promise<TransactionIdResponse>;
    contribute(req: ContributionRequest): Promise<ContributionResponse>;
    checkWin(req: CheckWinRequest): Promise<CheckWinResponse>;
    updateMiniGame(req: MiniGameRequest): Promise<MiniGameResponse>;
    confirmWin(req: WinConfirmRequest): Promise<WinConfirmResponse>;
    winJackpot(req: WinJackpotRequest): Promise<WinJackpotResponse>;
}

class APIServer implements JPNServer {

    private static CREATE_JACKPOT_REQUEST = "/api/v2/jpn/jackpots";
    private static AUTH_REQUEST = "/api/v2/jpn/auth";
    private static TICKER_REQUEST = "/api/v2/jpn/ticker";
    private static CONTRIBUTION_REQUEST = "/api/v2/jpn/contribute";
    private static CHECK_WIN_REQUEST = "/api/v2/jpn/checkWin";
    private static TRX_ID_REQUEST = "/api/v2/jpn/contribute/transactionId";
    private static MINI_GAME_REQUEST = "/api/v2/jpn/minigame";
    private static WIN_CONFIRMATION_REQUEST = "/api/v2/jpn/win/confirm";
    private static WIN_REQUEST = "/api/v2/jpn/win";

    private token: string;

    constructor(private baseUrl?) {}

    public async createJackpot(jackpotId: string, type: string): Promise<void> {
        try {
            await this.post(APIServer.CREATE_JACKPOT_REQUEST, {
                id: jackpotId,
                type: type
            }, process.env.JPN_TOKEN);
        } catch (err) {
            // ignore if jackpot already exist
            if (!(err.code === 27)) {
                throw err;
            }
        }
    }

    public async auth(req: AuthRequest): Promise<AuthResponse> {
        const response = await this.post<AuthResponse>(APIServer.AUTH_REQUEST, req);
        this.token = response.token;
        return response;
    }

    public getTicker(token: string): Promise<any> {
        return this.get<any>(APIServer.TICKER_REQUEST, this.token);
    }

    public generateTransactionId(): Promise<TransactionIdResponse> {
        return this.get<TransactionIdResponse>(APIServer.TRX_ID_REQUEST, this.token);
    }

    public contribute(req: ContributionRequest): Promise<ContributionResponse> {
        return this.post<ContributionResponse>(APIServer.CONTRIBUTION_REQUEST, req, this.token);
    }

    public checkWin(req: CheckWinRequest): Promise<CheckWinResponse> {
        return this.post<CheckWinResponse>(APIServer.CHECK_WIN_REQUEST, req, this.token);
    }

    public updateMiniGame(req: MiniGameRequest): Promise<MiniGameResponse> {
        return this.post<MiniGameResponse>(APIServer.MINI_GAME_REQUEST, req, this.token);
    }

    public confirmWin(req: WinConfirmRequest): Promise<WinConfirmResponse> {
        return this.post<WinConfirmResponse>(APIServer.WIN_CONFIRMATION_REQUEST, req, this.token);
    }

    public winJackpot(req: WinJackpotRequest): Promise<WinJackpotResponse> {
        return this.post<WinJackpotResponse>(APIServer.WIN_REQUEST, req, this.token);
    }

    public getHistory(id: string): Promise<PovJackpotHistory> {
        return undefined;
    }

    private post<T>(url: string, req, token?: string): Promise<T> {
        return this.request<T>("post", url, req, token);
    }

    private get<T>(url: string, token: string): Promise<T> {
        return this.request<T>("get", url, undefined, token);
    }

    private request<T>(method: string, url: string, req?: any, token?: string): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            const headers = {};
            if (token) {
                headers["X-Access-Token"] = token;
            }

            const requestUrl = `${this.baseUrl}${url}`;
            const request = superagent(method, requestUrl)
                .set(headers)
                .send(req || {});

            request.end((error, response) => {
                if (error) {
                    return reject(error);
                } else if (response.status !== 200 && response.status !== 201) {
                    const err: any = new Error("Request to JPN failed");
                    err.code = response.body.code;
                    return reject(err);
                } else {
                    return resolve(response.body);
                }
            });
        });
    }
}

class StubJPNServer implements JPNServer {
    public createJackpot(jackpotId: string, type: string): Promise<void> {
        return Promise.resolve();
    }

    public auth(req: AuthRequest): Promise<AuthResponse> {
        return Promise.resolve({ token: "test_token", jackpots: undefined });
    }

    public getTicker(token: string): Promise<any> {
        return Promise.resolve({});
    }

    public generateTransactionId(): Promise<TransactionIdResponse> {
        return Promise.resolve({ transactionId: "test_trx_id" });
    }

    public contribute(req: ContributionRequest): Promise<ContributionResponse> {
        return Promise.resolve({ events: [] });
    }

    public checkWin(req: CheckWinRequest): Promise<CheckWinResponse> {
        return Promise.resolve({ events: [] });
    }

    public updateMiniGame(req: MiniGameRequest): Promise<MiniGameResponse> {
        return Promise.resolve({ events: [] });
    }

    public confirmWin(req: WinConfirmRequest): Promise<WinConfirmResponse> {
        return Promise.resolve({ events: [] });
    }

    public winJackpot(req: WinJackpotRequest): Promise<WinJackpotResponse> {
        return Promise.resolve({ events: [] });
    }

    public getHistory(id: string): Promise<PovJackpotHistory> {
        return undefined;
    }
}

class DirectJPNServer implements JPNServer {
    private info: AuthRequest;
    private jackpotInstanceService = new JackpotInstancePovService();
    private jackpotService: JackpotService;

    constructor(seed?: number, private collectJpFullLog?: boolean) {
        const wallet = (cluster as any).isWorker ? walletShared : walletInMemory;

        const jackpotLookup: JackpotLookup & ExternalJackpotLookup & JackpotApplyPendingPool & JackpotUpdateInfo = {
            findInternal: async (id) => {
                const instance = await this.jackpotInstanceService.findInternal(id);
                return { ...instance, region: undefined };
            },
            findOrCreateTest: (id) => jackpotLookup.findInternal(id),
            create: (info) => jackpotLookup.findInternal(info.id),
            applyPendingPool: (id: string, poolId: string) => jackpotLookup.findInternal(id),
            updateInfo: (id: string, update: JackpotInstanceInfo) => jackpotLookup.findInternal(id),
            getExternalJackpots(gameProviderCode: string, ids: string[]): Promise<EGPJackpotInfoResponse> {
                throw new Error("Not implemented");
            },
            getExternalTickers(gameProviderCode: string, ids: string[], currency): Promise<EGPJackpotTickerResponse> {
                throw new Error("Not implemented");
            }
        };
        this.jackpotService = new JackpotService(jackpotLookup, wallet, ContextService,
            createRemoteServices(wallet, jackpotLookup));
        if (seed !== undefined) {
            getGlobalRandomGenerator().initSeed(seed);
        }
        walletShared.collectJpFullLog = collectJpFullLog;
    }

    public async createJackpot(jackpotId: string, type: string, globalJp: boolean): Promise<void> {
        loadGames();
        await initCurrencyService();
        const wallet = (cluster as any).isWorker ? walletShared : walletInMemory;
        try {
            await this.jackpotInstanceService.create({ id: jackpotId, type: type }, wallet, globalJp);
        } catch (err) {
            if (!(err instanceof Errors.JackpotInstanceAlreadyExist)) {
                throw err;
            }
        }
    }

    public auth(req: AuthRequest): Promise<AuthResponse> {
        this.info = req;
        return this.jackpotService.auth(req);
    }

    public getTicker(token: string): Promise<any> {
        return this.jackpotService.getTicker(this.info, {});
    }

    public generateTransactionId(): Promise<TransactionIdResponse> {
        return this.jackpotService.generateContributionTrxId();
    }

    public contribute(req: ContributionRequest): Promise<ContributionResponse> {
        return this.jackpotService.contribute(this.info, req);
    }

    public checkWin(req: CheckWinRequest): Promise<CheckWinResponse> {
        return this.jackpotService.checkWin(this.info, req);
    }

    public updateMiniGame(req: MiniGameRequest): Promise<MiniGameResponse> {
        return this.jackpotService.updateMiniGame(this.info, req);
    }

    public confirmWin(req: WinConfirmRequest): Promise<WinConfirmResponse> {
        return this.jackpotService.confirmWin(this.info, req);
    }

    public winJackpot(req: WinJackpotRequest): Promise<WinJackpotResponse> {
        return this.jackpotService.winJackpot(this.info, req);
    }

    public getHistory(id: string): Promise<PovJackpotHistory> {
        return HistoryService.getHistory(id);
    }
}

export async function createJPNServer(settings: JpnSettings): Promise<JPNServer> {
    let server;
    if (settings.api === "stub") {
        server = new StubJPNServer();
    } else if (settings.api === "direct") {
        server = new DirectJPNServer(settings.seed, settings.collectJpFullLog);
    } else {
        server = new APIServer(settings.api);
    }

    // create jackpots
    for (const jp of settings.jackpots) {
        await server.createJackpot(jp.id, jp.type, settings.globalJp);
    }

    // auth jackpot service
    const auth: AuthRequest = settings.auth as any;
    auth.jackpotIds = settings.jackpots.map(jp => jp.id);
    await server.auth(auth);

    return server;
}
