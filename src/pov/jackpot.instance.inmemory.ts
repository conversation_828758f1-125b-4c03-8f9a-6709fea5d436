import { JackpotInstance, JackpotInternalInstance, JackpotRegion } from "../skywind/api/model";
import * as Errors from "../skywind/errors";
import { lookupJackpotType } from "../skywind/services/game.service";
import { DEFAULT_PRECISION } from "../skywind/modules/jackpotWallet";
import { JackpotLookup } from "../definition";

export const POV_REGION: JackpotRegion = {
    code: "pov",
    url: "http://pov.tests"
};

export class JackpotInstancePovService implements JackpotLookup {

    private jackpots = {};

    public async create(info: JackpotInstance, wallet?, globalJp?: boolean): Promise<JackpotInstance> {
        const jackpotType = await lookupJackpotType(info.type);
        const jackpot: JackpotInternalInstance = {
            internalId: Object.keys(this.jackpots).length,
            precision: DEFAULT_PRECISION,
            id: info.id,
            type: jackpotType.name,
            jpGameId: jackpotType.jpGameId,
            definition: info.definition || jackpotType.definition,
            region: globalJp ? POV_REGION : undefined
        };
        this.jackpots[info.id] = jackpot;
        return jackpot;
    }

    public findInternal(id: string): Promise<JackpotInternalInstance> {
        const jackpot = this.jackpots[id];
        if (!jackpot) {
            return Promise.reject(new Errors.JackpotInstanceNotFound(id));
        }
        return jackpot;
    }

    public findOrCreateTest(id: string): Promise<JackpotInternalInstance> {
        return this.findInternal(id);
    }
}
