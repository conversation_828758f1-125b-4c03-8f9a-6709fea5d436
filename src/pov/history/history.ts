export interface PovContributionHistory {
    [pool: string]: PoolPovContributionHistory;
}

export interface PoolPovContributionHistory {
    count: number;
    seedSum: number;
    progressiveSum: number;
}

export interface PovWinHistory {
    [pool: string]: PoolPovWinHistory;
}

export interface PoolPovWinHistory {
    initialSeed: number;
    count: number;
    seedSum: number;
    seedMin: number;
    seedMax: number;
    progressiveSum: number;
    progressiveMin: number;
    progressiveMax: number;
}

export interface PovJackpotHistory {
    historyId: string;
    contributions: PovContributionHistory;
    wins: PovWinHistory;
    fullLog?: WinLogRecord[];
}

export interface PovHistory {
    getHistory(id: string): Promise<PovJackpotHistory>;
}

export interface PovContributionLog {
    jackpotId: string;
    pool: string;
    seed: number;
    progressive: number;
}

export interface PovWinLog {
    jackpotId: string;
    pool: string;
    initialSeed: number;
    seed: number;
    progressive: number;
    totalSeed: number;
    totalProgressive: number;
}

export interface WinLogRecord extends PovWinLog {
    ts: Date;
}

export interface PovHistoryLogger {
    logContributions(logs: PovContributionLog[]): Promise<void>;
    logWin(log: PovWinLog, collectFullJpLog?: boolean): Promise<void>;
}
