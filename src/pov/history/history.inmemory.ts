import {
    PovContributionHistory,
    PovHistory,
    PovWinHistory,
    PovHistoryLogger,
    PovContributionLog,
    PovWinLog,
    WinLogRecord,
    PovJackpotHistory
} from "./history";

export class InMemoryPovHistory implements PovHistory, PovHistoryLogger {

    private contributionLogs: { [jp: string]: PovContributionHistory } = {};
    private winLogs: { [jp: string]: PovWinHistory } = {};
    private fullWinLog: { [jp: string]: WinLogRecord[] } = {};

    public logContributions(logs: PovContributionLog[]): any {
        for (const log of logs) {
            if (!this.contributionLogs[log.jackpotId]) {
                this.contributionLogs[log.jackpotId] = {};
            }
            if (!this.contributionLogs[log.jackpotId][log.pool]) {
                this.contributionLogs[log.jackpotId][log.pool] = {
                    count: 0,
                    seedSum: 0,
                    progressiveSum: 0
                };
            }
            const poolLog = this.contributionLogs[log.jackpotId][log.pool];
            poolLog.count += 1;
            poolLog.seedSum += log.seed;
            poolLog.progressiveSum += log.progressive;
        }
    }

    public logWin(log: PovWinLog, collectFullJpLog: boolean): any {
        if (!this.winLogs[log.jackpotId]) {
            this.winLogs[log.jackpotId] = {};
        }
        if (!this.winLogs[log.jackpotId][log.pool]) {
            this.winLogs[log.jackpotId][log.pool] = {
                count: 0,
                seedSum: 0,
                progressiveSum: 0
            } as any;
        }
        if (collectFullJpLog) {
            if (!this.fullWinLog[log.jackpotId]) {
                this.fullWinLog[log.jackpotId] = [{ ts: new Date(), ...log }];
            } else {
                this.fullWinLog[log.jackpotId].push({ ts: new Date(), ...log });
            }
        }
        const jpLog = this.winLogs[log.jackpotId][log.pool];
        jpLog.initialSeed = log.initialSeed;
        jpLog.count += 1;
        jpLog.seedSum += log.seed;
        jpLog.seedMin = jpLog.seedMin === undefined || jpLog.seedMin > log.totalSeed ? log.totalSeed : jpLog.seedMin;
        jpLog.seedMax = jpLog.seedMax === undefined || jpLog.seedMax < log.totalSeed ? log.totalSeed : jpLog.seedMax;
        jpLog.progressiveSum += log.progressive;
        jpLog.progressiveMin = jpLog.progressiveMin === undefined || jpLog.progressiveMin > log.progressive ?
                               log.progressive : jpLog.progressiveMin;
        jpLog.progressiveMax = jpLog.progressiveMax === undefined || jpLog.progressiveMax < log.progressive ?
                               log.progressive : jpLog.progressiveMax;
        return;
    }

    public async getHistory(id: string): Promise<PovJackpotHistory> {
        return {
            historyId: process.pid.toString(),
            contributions: this.contributionLogs[id],
            wins: this.winLogs[id],
            fullLog: this.fullWinLog[id]
        };
    }
}

export default new InMemoryPovHistory();
