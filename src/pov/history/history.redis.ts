import * as redis from "../../skywind/storage/redis";
import * as path from "path";
import { PovHistory, PovJackpotHistory, PovHistoryLogger, PovContributionLog, PovWinLog } from "./history";

const contributionLogScript = path.resolve(__dirname, "../../../resources/lua/history_contribution_log.lua");
const winLogScript = path.resolve(__dirname, "../../../resources/lua/history_win_log.lua");

const client = redis.create();

export class RedisPovHistory implements PovHistory, PovHistoryLogger {

    private static CONTRIBUTION_LOG = "jp:contributions:log";
    private static WIN_LOG = "jp:win:log";

    public async logContributions(logs: PovContributionLog[]): Promise<void> {
        const updates = [];
        for (const log of logs) {
            updates.push(log.pool, log.seed, log.progressive);
        }
        await redis.execScript(
            client, contributionLogScript, [this.contributionLogName(logs[0].jackpotId)], updates);
    }

    public async logWin(log: PovWinLog): Promise<void> {
        await redis.execScript(client, winLogScript, [this.winLogName(log.jackpotId)],
            [log.pool, log.initialSeed, log.totalSeed, log.totalProgressive, log.seed , log.progressive]);
    }

    public async getHistory(id: string): Promise<PovJackpotHistory> {
        const contributions = await client.hgetall(this.contributionLogName(id));
        const wins = await client.hgetall(this.winLogName(id));
        return {
            historyId: "redis",
            contributions: this.parseHistory(contributions),
            wins: this.parseHistory(wins)
        };
    }

    private contributionLogName(id: string): string {
        return RedisPovHistory.CONTRIBUTION_LOG + ":" + id;
    }

    private winLogName(id: string): string {
        return RedisPovHistory.WIN_LOG + ":" + id;
    }

    /**
     * Parse value from Redis into an object. E.g.
     * {
     *   "pool_1:seed:sum": 10,
     *   "pool_1:progressive:sum": 20
     * }
     * ->
     * {
     *   pool_1: {
     *     seedSum: 10,
     *     progressiveSum: 20
     *   }
     * }
     */
    private parseHistory(values) {
        const result = {};
        for (const key of Object.keys(values)) {
            const parts = key.split(":");
            const pool = parts[0];
            if (!result[pool]) {
                result[pool] = {};
            }
            let prop = parts[1];
            for (let i = 2; i < parts.length; i += 1) {
                prop += parts[i][0].toUpperCase();
                prop += parts[i].substr(1);
            }
            result[pool][prop] = Number(values[key]);
        }
        return result;
    }
}

export default new RedisPovHistory();
