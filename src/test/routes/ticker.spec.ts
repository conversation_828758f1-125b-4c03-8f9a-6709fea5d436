import { expect, request, use } from "chai";
import { application } from "../../skywind/jackpotTickerServer";
import { Application } from "express";
import { TickerResponse } from "@skywind-group/sw-jpn-core";
import { JackpotWallet } from "../../skywind/modules/jackpotWallet";
import { createTestJPGame, flush, JP_WITH_DYNAMIC_CONTRIBUTION, TestJPGameWithCustomTicker } from "../helpers";
import { SinonStub, stub } from "sinon";
import JackpotInstanceService from "../../skywind/services/jackpot.instance";
import * as gameService from "../../skywind/services/game.service";
import { init as initCurrencyRates, multiplierCache } from "../../skywind/services/currency.service";
import wallet from "../../skywind/services/wallet.service";
import config from "../../skywind/config";

use(require("chai-http"));

describe("GET Ticker", () => {

    let app: Application;

    let getToEurMock: SinonStub;

    const jpInstance = JP_WITH_DYNAMIC_CONTRIBUTION;
    let lookup: SinonStub;
    let load: SinonStub;

    before(async () => {
        app = application.get();

        lookup = stub(JackpotInstanceService, "findInternal");
        lookup.returns(jpInstance);

        load = stub(gameService, "load");
        load.returns(await createTestJPGame(new JackpotWallet([] as any, jpInstance, 1, wallet)));

        await initCurrencyRates();
        getToEurMock = stub(multiplierCache as any, "load");

    });

    beforeEach(async () => {
        getToEurMock.resetBehavior();
        lookup.resetBehavior();
        lookup.returns(jpInstance);

        await flush();
    });

    after(() => {
        lookup.restore();
        load.restore();
        getToEurMock.restore();
    });

    function getTicker(query): Promise<TickerResponse> {
        return new Promise<TickerResponse>((resolve, reject) => {
            request(app)
                .get("/v1/ticker")
                .query(query)
                .then(res => {
                    expect(res.status).to.equal(200);
                    expect(res).to.be.json;
                    resolve(res.body);
                })
                .catch(reject);
        });
    }

    it("get ticker without currency", async () => {
        const ticker = await getTicker({
            jackpotIds: jpInstance.id
        });
        expect(ticker).deep.eq([{
            "jackpotId": jpInstance.id,
            "jackpotType": "test",
            "jackpotBaseType": "base",
            "currency": "EUR",
            "pools": {
                "small": {
                    "amount": 10,
                },
                "medium": {
                    "amount": 100,
                },
                "large": {
                    "amount": 1000,
                },
            },
        }]);
    });

    it("get ticker", async () => {
        const ticker = await getTicker({
            jackpotIds: jpInstance.id,
            currency: "CNY"
        });
        expect(ticker).deep.eq([{
            "jackpotId": jpInstance.id,
            "jackpotType": "test",
            "jackpotBaseType": "base",
            "currency": "CNY",
            "pools": {
                "small": {
                    "amount": 79.65,
                },
                "medium": {
                    "amount": 796.51,
                },
                "large": {
                    "amount": 7965.12,
                },
            },
        }]);
    });

    describe("custom ticker", async () => {

        before(async () => {
            config.toEURMultiplier.enableCustomTicker = true;
        });

        after(async () => {
            config.toEURMultiplier.enableCustomTicker = process.env.ENABLE_CUSTOM_TICKER === "true";
        });

        it("get ticker overridden by game, service unavailable", async () => {
            getToEurMock.throws(new Error());
            const jpWallet = new JackpotWallet([] as any, jpInstance, 1, wallet);
            const jackpot = {
                definition: jpWallet.instance.definition,
                getTicker: jpWallet.getTicker.bind(wallet)
            };
            const result = new TestJPGameWithCustomTicker(jackpot);
            load.returns(result);

            const ticker = await getTicker({
                jackpotIds: jpInstance.id,
                currency: "CNY"
            });
            expect(ticker).deep.eq([
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "currency": "CNY",
                    "pools": {
                        "small": {
                            "amount": 79.65,
                        },
                        "medium": {
                            "amount": 796.51,
                        },
                        "large": {
                            "amount": 7965.12,
                        },
                    }
                }
            ]);
        });

        it("get ticker overridden by game, currency EUR", async () => {
            const jpWallet = new JackpotWallet([] as any, jpInstance, 1, wallet);
            const jackpot = {
                definition: jpWallet.instance.definition,
                getTicker: jpWallet.getTicker.bind(wallet)
            };
            const result = new TestJPGameWithCustomTicker(jackpot);
            load.returns(result);

            const ticker = await getTicker({
                jackpotIds: jpInstance.id,
                currency: "EUR"
            });
            expect(ticker).deep.eq([
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "currency": "EUR",
                    "pools": {
                        "small": {
                            "amount": 1,
                        },
                        "medium": {
                            "amount": 1,
                        }
                    }
                }
            ]);
        });

        it("get ticker overridden by game, currency CNY", async () => {
            getToEurMock.returns({ toEURMultiplier: 2 });
            const jpWallet = new JackpotWallet([] as any, jpInstance, 1, wallet);
            const jackpot = {
                definition: jpWallet.instance.definition,
                getTicker: jpWallet.getTicker.bind(wallet)
            };
            const result = new TestJPGameWithCustomTicker(jackpot);
            load.returns(result);

            const ticker = await getTicker({
                jackpotIds: jpInstance.id,
                currency: "CNY"
            });
            expect(ticker).deep.eq([
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "currency": "CNY",
                    "pools": {
                        "small": {
                            "amount": 15.93,
                        },
                        "medium": {
                            "amount": 23.89
                        }
                    }
                }
            ]);
        });

        it("get ticker overridden by game, currency not found", async () => {
            getToEurMock.returns({});
            const jpWallet = new JackpotWallet([] as any, jpInstance, 1, wallet);
            const jackpot = {
                definition: jpWallet.instance.definition,
                getTicker: jpWallet.getTicker.bind(wallet)
            };
            const result = new TestJPGameWithCustomTicker(jackpot);
            load.returns(result);

            const ticker = await getTicker({
                jackpotIds: jpInstance.id,
                currency: "USD"
            });
            expect(ticker).deep.eq([
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "currency": "USD",
                    "pools": {
                        "small": {
                            "amount": 11.46,
                        },
                        "medium": {
                            "amount": 114.64,
                        },
                        "large": {
                            "amount": 1146.47
                        }
                    }
                }
            ]);
        });

        it("get ticker overridden by game, multiplier should be cached", async () => {
            getToEurMock.returns({ toEURMultiplier: 2 });
            const jpWallet = new JackpotWallet([] as any, jpInstance, 1, wallet);
            const jackpot = {
                definition: jpWallet.instance.definition,
                getTicker: jpWallet.getTicker.bind(wallet)
            };
            const result = new TestJPGameWithCustomTicker(jackpot);
            load.returns(result);

            await getTicker({
                jackpotIds: jpInstance.id,
                currency: "BYN"
            });

            getToEurMock.throws(new Error());
            const ticker = await getTicker({
                jackpotIds: jpInstance.id,
                currency: "BYN"
            });

            expect(ticker).deep.eq([
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "currency": "BYN",
                    "pools": {
                        "small": {
                            "amount": 4.81,
                        },
                        "medium": {
                            "amount": 7.21
                        }
                    }
                }
            ]);
        });
    });
});
