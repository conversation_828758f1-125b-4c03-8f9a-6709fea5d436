import { expect, use } from "chai";
import { application } from "../../skywind/server";
import * as JWT from "jsonwebtoken";
import { APISupport, flush, flushDb, TestJPGame } from "../helpers";
import { SinonStub, stub } from "sinon";
import * as gameService from "../../skywind/services/game.service";
import { JackpotInstance, JackpotRegion, JackpotType } from "../../skywind/api/model";
import { UserPermissions } from "../../skywind/utils/security";
import config from "../../skywind/config";
import { Jackpot } from "@skywind-group/sw-jpn-core";

use(require("chai-http"));
use(require("chai-as-promised"));

describe("Model API", () => {

    const userPermissions: UserPermissions = {
        username: "admin",
        grantedPermissions: ["jackpot"],
    };
    const type: JackpotType = {
        name: "test",
        baseType: "base",
        jpGameId: "test",
        definition: {
            currency: "EUR",
            list: [{
                id: "pool",
                seed: {
                    amount: 100,
                },
                contribution: [],
            }],
        },
        configurable: true,
        overridable: true,
        canBeDisabled: true,
        supportsWinCap: false
    };
    const instance: JackpotInstance = {
        id: "testid",
        type: "test"
    };
    const region: JackpotRegion = {
        code: "eu",
        url: "http://jpn.eu",
        secureOptions: { key: "key" }
    };
    let gameExists: SinonStub;
    let jpGameLoad: SinonStub;
    let api: APISupport;

    before(async() => {
        gameExists = stub(gameService, "exists");
        jpGameLoad = stub(gameService, "load").callsFake((jpGameId: string, jackpot: Jackpot) => {
            return new TestJPGame(jackpot);
        });
        const token = JWT.sign(userPermissions, config.accessToken.secret);
        api = new APISupport(application.get(), token);
    });

    beforeEach(async() => {
        gameExists.resetBehavior();
        gameExists.returns(true);

        await flushDb();
        await flush();
    });

    after(() => {
        gameExists.restore();
        jpGameLoad.restore();
    });

    it("fails to create jackpot type - validation error", async() => {
        try {
            await api.post("/api/v2/jpn/types", {} as JackpotType);
            expect.fail("should fail");
        } catch (err) {
            expect(err.status).to.equal(400);
            expect(err.response.body.code).to.be.equal(3);
        }
    });

    it("fails to create jackpot instance - validation error", async() => {
        try {
            await api.post("/api/v2/jpn/jackpots", {} as JackpotInstance);
            expect.fail("should fail");
        } catch (err) {
            expect(err.status).to.equal(400);
            expect(err.response.body.code).to.be.equal(3);
        }
    });

    it("creates jackpot type", async() => {
        const created = await api.post("/api/v2/jpn/types", type);
        expect(created).to.deep.equal(type);
    });

    describe("with jackpot type", async() => {

        beforeEach(async() => {
            await api.post("/api/v2/jpn/types", type);
        });

        it("gets all jackpot types", async() => {
            const read = await api.get("/api/v2/jpn/types");
            expect(read).to.deep.equal([type]);
        });

        it("updates jackpot type", async() => {
            const typeUpdate: JackpotType = {
                name: "test",
                baseType: "base",
                jpGameId: "test",
                definition: {
                    currency: "EUR",
                    list: [{
                        id: "pool",
                        seed: {
                            amount: 100,
                        },
                        contribution: [{ seed: 1, progressive: 1 }],
                    }],
                },
                configurable: false,
                overridable: false,
                canBeDisabled: false,
                supportsWinCap: false
            };
            const updated = await api.patch("/api/v2/jpn/types/" + type.name, typeUpdate);
            expect(updated).to.deep.equal(typeUpdate);
        });

        it("gets jackpot type", async() => {
            const read = await api.get("/api/v2/jpn/types/" + type.name);
            expect(read).to.deep.equal(type);
        });

        it("deletes jackpot type", async() => {
            await api.del("/api/v2/jpn/types/" + type.name);
        });

        it("creates jackpot instance", async() => {
            const created = await api.post("/api/v2/jpn/jackpots", instance);
            const expected = {
                id: instance.id,
                type: instance.type,
                baseType: type.baseType,
                jpGameId: type.jpGameId,
                isTest: null,
                definition: type.definition,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: (created as JackpotInstance).createdAt,
                jackpotConfigurationLevel: null,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            };
            expect(created).to.deep.equal(expected);
        });

        describe("with jackpot instance", async() => {

            const expectedInstance = {
                id: instance.id,
                type: instance.type,
                baseType: type.baseType,
                jpGameId: type.jpGameId,
                isTest: null,
                definition: type.definition,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                jackpotConfigurationLevel: null,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            };

            beforeEach(async() => {
                await api.post("/api/v2/jpn/jackpots", instance);
            });

            it("fails to create jackpot instance with invalid id", async() => {
                await expect(api.post("/api/v2/jpn/jackpots", {
                    id: "test:id",
                    type: "test"
                })).to.be.rejectedWith(Error);
            });

            it("gets all jackpot instances", async() => {
                const read = await api.get("/api/v2/jpn/jackpots");
                expectedInstance["createdAt"] = (read[0] as JackpotInstance).createdAt;
                expect([read[0]]).to.deep.equal([expectedInstance]);
            });

            it("updates jackpot instance", async() => {
                const update: JackpotInstance = {
                    id: "testid",
                    type: "test",
                    definition: {
                        currency: "EUR",
                        list: [{
                            id: "pool",
                            seed: {
                                amount: 100,
                            },
                            contribution: [{ seed: 1, progressive: 1 }],
                        }],
                    },
                };
                const updated = await api.patch("/api/v2/jpn/jackpots/" + instance.id, update);
                const expected = {
                    id: instance.id,
                    type: instance.type,
                    baseType: type.baseType,
                    jpGameId: type.jpGameId,
                    isTest: null,
                    definition: update.definition,
                    disableMode: 0,
                    isDisabled: false,
                    isGlobal: false,
                    isOwned: false,
                    isLocal: false,
                    createdAt: (updated as JackpotInstance).createdAt,
                    jackpotConfigurationLevel: null,
                    entityId: null,
                    jurisdictionCode: null,
                    info: null,
                };
                expect(updated).to.deep.equal(expected);
            });

            it("updates jackpot instance with info", async () => {
                const externalStartDate = new Date();
                const update: JackpotInstance = {
                    id: "testid",
                    type: "test",
                    info: {
                        externalId: "testid_HASH",
                        externalStartDate: externalStartDate.toISOString()
                    },
                };
                const updated = await api.patch("/api/v2/jpn/jackpots/" + instance.id, update);
                const expected = {
                    id: instance.id,
                    type: instance.type,
                    baseType: type.baseType,
                    jpGameId: type.jpGameId,
                    isTest: null,
                    definition: type.definition,
                    disableMode: 0,
                    isDisabled: false,
                    isGlobal: false,
                    isOwned: false,
                    isLocal: false,
                    createdAt: (updated as JackpotInstance).createdAt,
                    jackpotConfigurationLevel: null,
                    entityId: null,
                    jurisdictionCode: null,
                    info: update.info,
                };
                expect(updated).to.deep.equal(expected);
            });

            it("gets jackpot instance", async() => {
                const read = await api.get("/api/v2/jpn/jackpots/" + instance.id);
                expectedInstance["createdAt"] = (read as JackpotInstance).createdAt;
                expect(read).to.deep.equal(expectedInstance);
            });

            it("gets jackpot instance ticker", async() => {
                const read = await api.get("/api/v2/jpn/jackpots/" + instance.id + "/ticker");
                expect(read).to.deep.equal({
                    "currency": "EUR",
                    "id": "testid",
                    "pools": {
                        "pool": {
                            "progressive": 0,
                            "seed": 0,
                        },
                    },
                    "seqId": 0
                });
            });

            it("disables jackpot instance", async() => {
                const disabled = await api.put("/api/v2/jpn/jackpots/" + instance.id + "/disable", {},
                    { disableMode: 1 });
                const expected = {
                    ...expectedInstance,
                    disableMode: 1,
                    isDisabled: true,
                    createdAt: (disabled as JackpotInstance).createdAt
                };
                expect(disabled).to.deep.equal(expected);
            });

            it("deletes jackpot instance", async() => {
                await api.del("/api/v2/jpn/jackpots/" + instance.id);
            });

            it("gets audits", async () => {
                const expectedAudit: any = {
                    "history": {
                        "definition": {
                            "currency": "EUR",
                            "list": [
                                {
                                    "contribution": [],
                                    "id": "pool",
                                    "seed": {
                                        "amount": 100
                                    }
                                }
                            ]
                        },
                        "disableMode": 0,
                        "id": "testid",
                        "isDisabled": false,
                        "isTest": null,
                        "jpGameId": "test",
                        "baseType": "base",
                        "type": "test",
                        "isGlobal": false,
                        "isOwned": false,
                        "isLocal": false,
                        "jackpotConfigurationLevel": null,
                        "entityId": null,
                        "jurisdictionCode": null,
                        "info": null,
                    },
                    "initiatorName": "admin",
                    "initiatorType": "user",
                    "ip": "::ffff:127.0.0.1",
                    "jackpotId": "testid",
                    "type": "create",
                    "userAgent": "node-superagent/2.3.0"
                };
                let audits: any[] = await api.get("/api/v2/jpn/audits");
                expect(audits.length).to.deep.equal(1);
                expectedAudit.ts = audits[0].ts;
                expectedAudit.history.createdAt = audits[0].history.createdAt;
                expect(audits[0]).to.deep.equal(expectedAudit);

                audits = await api.get("/api/v2/jpn/audits", { jackpotId: instance.id });
                expect(audits.length).to.deep.equal(1);
                expect(audits[0]).to.deep.equal(expectedAudit);

                audits = await api.get("/api/v2/jpn/audits", { jackpotId: instance.id, type: "create" });
                expect(audits.length).to.deep.equal(1);
                expect(audits[0]).to.deep.equal(expectedAudit);
            });
        });
    });

    describe("not permitted", () => {

        const userWithoutPermissions: UserPermissions = {
            username: "nobody",
            grantedPermissions: [],
        };
        const noPermissions = JWT.sign(userWithoutPermissions, config.accessToken.secret);
        const apiNoPermissions = new APISupport(application.get(), noPermissions);

        it("fails to create jackpot type - not permitted", async() => {
            try {
                await apiNoPermissions.post("/api/v2/jpn/types", type);
                expect.fail("should fail");
            } catch (err) {
                expect(err.status).to.equal(403);
                expect(err.response.body.code).to.be.equal(2);
            }
        });

        it("fails to create jackpot instance - not permitted", async() => {
            try {
                await apiNoPermissions.post("/api/v2/jpn/jackpots", instance);
                expect.fail("should fail");
            } catch (err) {
                expect(err.status).to.equal(403);
                expect(err.response.body.code).to.be.equal(2);
            }
        });

    });

    describe("regions", () => {

        it("crud jackpot region", async() => {
            const created = await api.post("/api/v2/jpn/regions", region);
            expect(created).to.deep.equal(region);

            const list = await api.get("/api/v2/jpn/regions");
            expect(list).to.deep.equal([region]);

            const data = await api.get("/api/v2/jpn/regions/eu");
            expect(data).to.deep.equal(region);

            const update = { url: "http://jpn.eu.2" };
            const updated = await api.patch("/api/v2/jpn/regions/eu", update);
            expect(updated).to.deep.equal({ ...region, ...update });

            await api.del("/api/v2/jpn/regions/eu");
        });
    });
});
