import { expect, use, request } from "chai";
import { application } from "../../skywind/server";
import { Application } from "express";
import * as JWT from "jsonwebtoken";
import {
    AuthRequest, ContributionRequest, ContributionResponse, TickerResponse,
    WinConfirmRequest, WinConfirmResponse, MiniGameRequest, MiniGameResponse,
    WinJackpotRequest, WinJackpotResponse, CheckWinRequest, CheckWinResponse
} from "@skywind-group/sw-jpn-core";
import { JackpotWallet } from "../../skywind/modules/jackpotWallet";
import { createTestJPGame, flush, JP_WITH_DYNAMIC_CONTRIBUTION } from "../helpers";
import { stub, SinonStub } from "sinon";
import JackpotInstanceService from "../../skywind/services/jackpot.instance";
import * as gameService from "../../skywind/services/game.service";
import { init as initCurrencyRates } from "../../skywind/services/currency.service";
import wallet from "../../skywind/services/wallet.service";

use(require("chai-http"));

describe("GET api/v2/jpn", () => {

    let app: Application;

    const jpInstance = JP_WITH_DYNAMIC_CONTRIBUTION;
    const authData = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        jackpotIds: [jpInstance.id],
        currency: "EUR",
        gameCode: "test"
    };
    const params = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        playerCurrency: "EUR",
        gameCode: "test",
        gameId: "test",
        roundId: "1"
    };
    const jpModule = new JackpotWallet(params, jpInstance, 1, wallet);
    let jpGame;
    let lookup: SinonStub;
    let load: SinonStub;

    before(async() => {
        jpGame = await createTestJPGame(jpModule);

        app = application.get();
        lookup = stub(JackpotInstanceService, "findInternal");
        load = stub(gameService, "load");
        await initCurrencyRates();
    });

    beforeEach(async() => {
        lookup.resetBehavior();
        lookup.returns(jpInstance);
        load.resetBehavior();
        load.returns(jpGame);

        await flush();
    });

    after(() => {
        lookup.restore();
        load.restore();
    });

    function auth(info: AuthRequest): Promise<string> {
        return new Promise<string>(function (resolve, reject) {
            request(app)
                .post("/api/v2/jpn/auth")
                .send(info)
                .then(res => {
                    expect(res.status).to.equal(200);
                    expect(res).to.be.json;
                    const token: string = res.body.token;
                    resolve(token);
                })
                .catch(reject);
        });
    }

    function generateTrxId(token: string): Promise<string> {
        return new Promise<string>((resolve, reject) => {
            request(app)
                .get("/api/v2/jpn/contribute/transactionId")
                .set("X-ACCESS-TOKEN", token)
                .then(res => {
                    expect(res.status).to.equal(200);
                    expect(res).to.be.json;
                    expect(res.body.transactionId).to.exist;
                    resolve(res.body.transactionId);
                })
                .catch(reject);
        });
    }

    function contribute(token: string, contribution: ContributionRequest): Promise<ContributionResponse> {
        return new Promise<ContributionResponse>((resolve, reject) => {
            request(app)
                .post("/api/v2/jpn/contribute")
                .set("X-ACCESS-TOKEN", token)
                .send(contribution)
                .then(res => {
                    expect(res.status).to.equal(200);
                    expect(res).to.be.json;
                    resolve(res.body);
                })
                .catch(reject);
        });
    }

    function checkWin(token: string, data: CheckWinRequest): Promise<CheckWinResponse> {
        return new Promise<ContributionResponse>((resolve, reject) => {
            request(app)
                .post("/api/v2/jpn/checkWin")
                .set("X-ACCESS-TOKEN", token)
                .send(data)
                .then(res => {
                    expect(res.status).to.equal(200);
                    expect(res).to.be.json;
                    resolve(res.body);
                })
                .catch(reject);
        });
    }

    function getTicker(token: string, query: any): Promise<TickerResponse> {
        return new Promise<TickerResponse>((resolve, reject) => {
            request(app)
                .get("/api/v2/jpn/ticker")
                .set("X-ACCESS-TOKEN", token)
                .query(query)
                .then(res => {
                    expect(res.status).to.equal(200);
                    expect(res).to.be.json;
                    resolve(res.body);
                })
                .catch(reject);
        });
    }

    function confirm(token: string, confirm: WinConfirmRequest): Promise<WinConfirmResponse> {
        return new Promise<WinConfirmResponse>((resolve, reject) => {
            request(app)
                .post("/api/v2/jpn/win/confirm")
                .set("X-ACCESS-TOKEN", token)
                .send(confirm)
                .then(res => {
                    expect(res.status).to.equal(200);
                    expect(res).to.be.json;
                    resolve(res.body);
                })
                .catch(reject);
        });
    }

    function updateMiniGame(token: string, miniGame: MiniGameRequest): Promise<MiniGameResponse> {
        return new Promise<MiniGameResponse>((resolve, reject) => {
            request(app)
                .post("/api/v2/jpn/minigame")
                .set("X-ACCESS-TOKEN", token)
                .send(miniGame)
                .then(res => {
                    expect(res.status).to.equal(200);
                    expect(res).to.be.json;
                    resolve(res.body);
                })
                .catch(reject);
        });
    }

    function triggerJackpot(token: string, win: WinJackpotRequest): Promise<WinJackpotResponse> {
        return new Promise<WinJackpotResponse>((resolve, reject) => {
            request(app)
                .post("/api/v2/jpn/win")
                .set("X-ACCESS-TOKEN", token)
                .send(win)
                .then(res => {
                    expect(res.status).to.equal(200);
                    expect(res).to.be.json;
                    resolve(res.body);
                })
                .catch(reject);
        });
    }

    it("auth", async() => {
        const token = await auth(authData);
        const data: any = JWT.decode(token);
        expect(data.playerCode).to.equal("PL-CODE");
        expect(data.brandId).to.equal(1);
    });

    it("auth not valid", async() => {
        try {
            await auth({} as any);
            expect.fail("should fail");
        } catch (err) {
            expect(err.status).to.equal(400);
            expect(err.response.body.code).to.be.equal(3);
        }
    });

    it("generate contribution trx id", async() => {
        const token = await auth(authData);
        const trxId = await generateTrxId(token);
        expect(trxId).to.exist;
    });

    it("do simple contribution", async() => {
        const token = await auth(authData);
        const trxId = await generateTrxId(token);
        const response = await contribute(token, {
            externalId: "1",
            transactionId: trxId,
            amount: 6.02,
            roundId: "1",
            exchangeRates: {
                "EUR": 1
            }
        });
        expect(response).deep.eq({
            "events": [
                {
                    "type": "contribution",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": [
                        {
                            "pool": "small",
                            "progressive": 0.01806,
                            "seed": 0.00602
                        },
                        {
                            "pool": "medium",
                            "progressive": 0.02408,
                            "seed": 0.01204,
                        },
                        {
                            "pool": "large",
                            "progressive": 0.04214,
                            "seed": 0.01806
                        }
                    ],
                    "pools": {
                        "small": {
                            "amount": 10.01,
                            "progressive": 0.01806,
                            "seed": 0.00602
                        },
                        "medium": {
                            "amount": 100.02,
                            "progressive": 0.02408,
                            "seed": 0.01204
                        },
                        "large": {
                            "amount": 1000.04,
                            "progressive": 0.04214,
                            "seed": 0.01806
                        },
                    },
                    "totalContribution": 0.1204
                },
            ],
        });
    });

    it("contribution not valid", async() => {
        const token = await auth(authData);
        try {
            await contribute(token, {} as any);
            expect.fail("should fail");
        } catch (err) {
            expect(err.status).to.equal(400);
            expect(err.response.body.code).to.be.equal(3);
        }
    });

    it("contribution with negative amount", async() => {
        const token = await auth(authData);
        try {
            await contribute(token, {
                externalId: "1",
                transactionId: "xxx",
                amount: -1,
            } as any);
            expect.fail("should fail");
        } catch (err) {
            expect(err.status).to.equal(400);
            expect(err.response.body.code).to.be.equal(3);
        }
    });

    it("contribution with invalid amount", async() => {
        const token = await auth(authData);
        try {
            await contribute(token, {
                externalId: "1",
                transactionId: "xxx",
                amount: 0.00001,
            } as any);
            expect.fail("should fail");
        } catch (err) {
            expect(err.status).to.equal(400);
            expect(err.response.body.code).to.be.equal(3);
        }
    });

    it("confirm multiple jackpot win", async() => {
        const token = await auth(authData);
        const trxId = "Bjyr0wAAAr0AAAK9Bjyr02EPx8Y=";
        const confirmResponse = await confirm(token, {
            transactionId: trxId,
            jackpotId: [jpInstance.id, jpInstance.id],
            roundId: "1"
        });

        expect(confirmResponse).deep.eq({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });
    });

    it("updates mini game - not found", async() => {
        const token = await auth(authData);
        try {
            await updateMiniGame(token, {
                transactionId: "Bjyr0wAAAr0AAAK9Bjyr02EPx8Y=",
                jackpotId: jpInstance.id,
                roundId: "1"
            });
            expect.fail("should fail");
        } catch (err) {
            expect(err.status).to.equal(404);
            expect(err.response.body.code).to.be.equal(15);
        }
    });

    it("get ticker information", async() => {
        const token = await auth(authData);
        const trxId = await generateTrxId(token);
        await contribute(token, {
            externalId: "1",
            transactionId: trxId,
            amount: 6,
            roundId: "1"
        });
        const ticker = await getTicker(token, {
            exchangeRates: JSON.stringify({ "EUR": 1 })
        });
        expect(ticker).deep.eq([{
            "jackpotId": jpInstance.id,
            "jackpotType": "test",
            "jackpotBaseType": "base",
            "pools": {
                "small": {
                    "amount": 10.01,
                    "progressive": 0.018,
                    "seed": 0.006
                },
                "medium": {
                    "amount": 100.02,
                    "progressive": 0.024,
                    "seed": 0.012
                },
                "large": {
                    "amount": 1000.04,
                    "progressive": 0.042,
                    "seed": 0.018
                },
            },
        }]);
    });

    it("trigger jackpot win with empty JackpotId - not valid", async() => {
        const token = await auth(authData);
        try {
            await triggerJackpot(token, {
                transactionId: "Bjyr0wAAAr0AAAK9Bjyr02EPx8Y=",
                roundId: 1
            } as any);
            expect.fail("should fail");
        } catch (err) {
            expect(err.status).to.equal(400);
            expect(err.response.body.code).to.be.equal(29); // 29 - not authorized JackpotId
        }
    });

    it("trigger jackpot win with wrong jackpotId - not valid", async() => {
        const token = await auth(authData);
        try {
            await triggerJackpot(token, {
                transactionId: "Bjyr0wAAAr0AAAK9Bjyr02EPx8Y=",
                roundId: 1,
                jackpotId: "wrong_jp_id"
            } as any);
            expect.fail("should fail");
        } catch (err) {
            expect(err.status).to.equal(400);
            expect(err.response.body.code).to.be.equal(29); // 29 - not authorized JackpotId
        }
    });

    it("checkWin not valid", async() => {
        const token = await auth(authData);
        try {
            await checkWin(token, {} as any);
            expect.fail("should fail");
        } catch (err) {
            expect(err.status).to.equal(400);
            expect(err.response.body.code).to.be.equal(3);
        }
    });

    describe("do contribution and win", () => {

        let gameCheckWin: SinonStub;
        let winMiniGame: SinonStub;
        let winJackpot: SinonStub;

        before(() => {
            gameCheckWin = stub(jpGame, "checkWin");
            winMiniGame = stub(jpGame, "winMiniGame");
            winJackpot = stub(jpGame, "winJackpot");
        });

        beforeEach(() => {
            gameCheckWin.resetBehavior();
            winMiniGame.resetBehavior();
            winJackpot.resetBehavior();
        });

        after(() => {
            gameCheckWin.restore();
            winMiniGame.restore();
            winJackpot.restore();
        });

        it("wins jackpot", async() => {
            gameCheckWin.returns({
                type: "win",
                pool: "medium",
            });

            const token = await auth(authData);
            const trxId = await generateTrxId(token);
            const response = await contribute(token, {
                externalId: "1",
                transactionId: trxId,
                amount: 6,
                roundId: "1"
            });
            expect(response).deep.eq({
                "events": [
                    {
                        "type": "win",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                        "amount": 100.02,
                        "pool": "medium",
                        "title": "medium",
                        "progressive": 0.024,
                        "seed": 100
                    },
                    {
                        "type": "contribution",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "contributions": [
                            {
                                "progressive": 0.018,
                                "seed": 0.006000000000000001,
                                "pool": "small",
                            },
                            {
                                "progressive": 0.024000000000000004,
                                "seed": 0.012000000000000002,
                                "pool": "medium",
                            },
                            {
                                "progressive": 0.041999999999999996,
                                "seed": 0.018,
                                "pool": "large",
                            }
                        ],
                        "pools": {
                            "small": {
                                "amount": 10.01,
                                "progressive": 0.018,
                                "seed": 0.006
                            },
                            "medium": {
                                "amount": 100,
                                "progressive": 0,
                                "seed": -99.988
                            },
                            "large": {
                                "amount": 1000.04,
                                "progressive": 0.042,
                                "seed": 0.018
                            },
                        },
                        "totalContribution": 0.12000000000000001
                    },
                ],
            });

            const confirmResponse = await confirm(token, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
            expect(confirmResponse).deep.eq({
                "events": [
                    {
                        "type": "win-confirm",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                    },
                ],
            });
        });

        it("wins mini game", async() => {
            gameCheckWin.returns({
                type: "start-mini-game",
            });
            winMiniGame.returns({
                type: "win",
                pool: "medium",
            });

            const token = await auth(authData);
            const trxId = await generateTrxId(token);
            const response = await contribute(token, {
                externalId: "1",
                transactionId: trxId,
                amount: 6,
                roundId: "1"
            });
            expect(response).deep.eq({
                "events": [
                    {
                        "type": "start-mini-game",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                    },
                    {
                        "type": "contribution",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "contributions": [
                            {
                                "progressive": 0.018,
                                "seed": 0.006000000000000001,
                                "pool": "small",
                            },
                            {
                                "progressive": 0.024000000000000004,
                                "seed": 0.012000000000000002,
                                "pool": "medium",
                            },
                            {
                                "progressive": 0.041999999999999996,
                                "seed": 0.018,
                                "pool": "large",
                            }
                        ],
                        "pools": {
                            "small": {
                                "amount": 10.01,
                                "progressive": 0.018,
                                "seed": 0.006
                            },
                            "medium": {
                                "amount": 100.02,
                                "progressive": 0.024,
                                "seed": 0.012
                            },
                            "large": {
                                "amount": 1000.04,
                                "progressive": 0.042,
                                "seed": 0.018
                            },
                        },
                        "totalContribution": 0.12000000000000001
                    },
                ],
            });

            const mgResponse = await updateMiniGame(token, {
                transactionId: trxId,
                jackpotId: jpInstance.id,
                roundId: "1"
            });
            expect(mgResponse).deep.eq({
                "events": [
                    {
                        "type": "win",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                        "pool": "medium",
                        "title": "medium",
                        "amount": 100.02,
                        "progressive": 0.024,
                        "seed": 100
                    },
                    {
                        "type": "end-mini-game",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                    },
                ],
            });

            const confirmResponse = await confirm(token, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
            expect(confirmResponse).deep.eq({
                "events": [
                    {
                        "type": "win-confirm",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                    },
                ],
            });
        });

        it("triggers jackpot", async() => {
            winJackpot.returns({
                type: "win",
                pool: "medium",
            });

            const token = await auth(authData);
            const trxId = await generateTrxId(token);
            const response = await triggerJackpot(token, {
                jackpotId: jpInstance.id,
                transactionId: trxId,
                roundId: "1"
            });
            expect(response).deep.eq({
                "events": [
                    {
                        "type": "win",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                        "amount": 100,
                        "pool": "medium",
                        "title": "medium",
                        "progressive": 0,
                        "seed": 100
                    },
                ],
            });

            const confirmResponse = await confirm(token, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
            expect(confirmResponse).deep.eq({
                "events": [
                    {
                        "type": "win-confirm",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                    },
                ],
            });
        });

        it("wins jackpot on free game", async() => {
            gameCheckWin.returns({
                type: "win",
                pool: "medium",
            });

            const token = await auth(authData);
            const trxId = await generateTrxId(token);
            const response = await checkWin(token, {
                externalId: "1",
                transactionId: trxId,
                roundId: "1"
            });
            expect(response).deep.eq({
                "events": [
                    {
                        "type": "win",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                        "amount": 100,
                        "pool": "medium",
                        "title": "medium",
                        "progressive": 0,
                        "seed": 100
                    }
                ],
            });

            const confirmResponse = await confirm(token, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
            expect(confirmResponse).deep.eq({
                "events": [
                    {
                        "type": "win-confirm",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                    },
                ],
            });
        });
    });
});
