import { expect } from "chai";
import { ContributionConsumer } from "../../skywind/history/contribution.consumer";
import * as ContributionModel from "../../skywind/history/contribution.log";
import { flushDb } from "../helpers";

describe("Contribution Consumer", () => {

    const consumer = new ContributionConsumer();

    beforeEach(async () => {
        await flushDb();
        await ContributionModel.contributionLogDb.sync();
        await ContributionModel.remoteContributionLogDb.sync();
    });

    it("consumes contributes", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "id": "bSF\/rQAAArwAAAK9bSF\/rfp0X60=",
            "ts": new Date("2017-10-05T15:22:29.169Z"),
            "committedAt": new Date("2017-10-10T15:22:29.169Z"),
            "data": [
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "operatorSeed",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "operatorProgressive",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "operatorSeed",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "operatorProgressive",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "large"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "large"
                }
            ],
            "operation": {
                "operationName": "contribute",
                "params": {
                    "precision": **********,
                    "gameData": {
                        "externalId": "123",
                        "transactionId": "bSF\/rQAAArwAAAK9bSF\/rfp0X60=",
                        "amount": 10
                    },
                    "playerContributions": [
                        {
                            "seed": 0.01 / 0.1368,
                            "progressive":  0.03 / 0.1368,
                            "pool": "small"
                        },
                        {
                            "seed": 0.01 / 0.1368,
                            "progressive": 0.07 / 0.1368,
                            "pool": "large"
                        }
                    ],
                    "contributions": [
                        {
                            "totalSeed": 0.01 / 0.1368,
                            "totalProgressive":  0.03 / 0.1368,
                            "pool": "small"
                        },
                        {
                            "totalSeed": 0.01 / 0.1368,
                            "totalProgressive": 0.07 / 0.1368,
                            "pool": "large"
                        }
                    ],
                    "currencyRate": 0.1368,
                    "gameId": "test",
                    "brandId": 1,
                    "region": "eu",
                    "playerCurrency": "CNY",
                    "contributionAmount": 73.1,
                    "playerCode": "PL-CODE",
                    "gameCode": "test",
                    "roundId": 1
                },
                "externalTrxId": "123",
                "operationId": 1
            }
        };
        await consumer.process(undefined, [data]);

        const unloaded = await ContributionModel.contributionLogDb.findAll();
        expect(unloaded).to.deep.equal([
            {
                "brandId": 1,
                "region": "eu",
                "contributionAmount": "73.1",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameData": {
                    "amount": 10,
                    "externalId": "123",
                    "transactionId": "bSF/rQAAArwAAAK9bSF/rfp0X60="
                },
                "gameId": "test",
                "id": 1,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "gameCode": "test",
                "pool": "small",
                "progressive": "0.03",
                "seed": "0.01",
                "playerProgressive": "0.21929824561403508",
                "playerSeed": "0.07309941520467836",
                "trxId": "bSF/rQAAArwAAAK9bSF/rfp0X60=",
                "trxDate": new Date("2017-10-10T15:22:29.169Z"),
                "roundId": "1",
                "insertedAt": unloaded[0].insertedAt,
                "totalSeed": "0.07309941520467836",
                "totalProgressive": "0.21929824561403508",
                "status": null
            },
            {
                "brandId": 1,
                "region": "eu",
                "contributionAmount": "73.1",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameData": {
                    "amount": 10,
                    "externalId": "123",
                    "transactionId": "bSF/rQAAArwAAAK9bSF/rfp0X60="
                },
                "gameId": "test",
                "id": 2,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "gameCode": "test",
                "pool": "medium",
                "progressive": "0.04",
                "seed": "0.02",
                "playerProgressive": "0",
                "playerSeed": "0",
                "trxId": "bSF/rQAAArwAAAK9bSF/rfp0X60=",
                "trxDate": new Date("2017-10-10T15:22:29.169Z"),
                "roundId": "1",
                "insertedAt": unloaded[1].insertedAt,
                "totalSeed": "0",
                "totalProgressive": "0",
                "status": null
            },
            {
                "brandId": 1,
                "region": "eu",
                "contributionAmount": "73.1",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameData": {
                    "amount": 10,
                    "externalId": "123",
                    "transactionId": "bSF/rQAAArwAAAK9bSF/rfp0X60="
                },
                "gameId": "test",
                "id": 3,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "gameCode": "test",
                "pool": "large",
                "progressive": "0.07",
                "seed": "0.03",
                "playerProgressive": "0.5116959064327485",
                "playerSeed": "0.07309941520467836",
                "trxId": "bSF/rQAAArwAAAK9bSF/rfp0X60=",
                "trxDate": new Date("2017-10-10T15:22:29.169Z"),
                "roundId": "1",
                "insertedAt": unloaded[2].insertedAt,
                "totalSeed": "0.07309941520467836",
                "totalProgressive": "0.5116959064327485",
                "status": null
            }
        ]);
        const remoteUnloaded = await ContributionModel.remoteContributionLogDb.findAll();
        expect(remoteUnloaded).to.be.empty;
    });

    it("consumes contributes from remote", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "id": "bSF\/rQAAArwAAAK9bSF\/rfp0X60=",
            "ts": new Date("2017-10-05T15:22:29.169Z"),
            "committedAt": new Date("2017-10-10T15:22:29.169Z"),
            "data": [
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "operatorSeed",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "operatorProgressive",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "operatorSeed",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "operatorProgressive",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "large"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "large"
                }
            ],
            "operation": {
                "operationName": "contribute",
                "params": {
                    "fromRemote": true,
                    "remoteTrxId": "bSF/rQAAArwBBBK9bSF/rfp0X60=",
                    "remoteTrxRegion": "remote",
                    "precision": **********,
                    "gameData": {
                        "externalId": "123",
                        "transactionId": "bSF\/rQAAArwAAAK9bSF\/rfp0X60=",
                        "amount": 10
                    },
                    "playerContributions": [
                        {
                            "seed": 0.01 / 0.1368,
                            "progressive":  0.03 / 0.1368,
                            "pool": "small"
                        },
                        {
                            "seed": 0.01 / 0.1368,
                            "progressive": 0.07 / 0.1368,
                            "pool": "large"
                        }
                    ],
                    "contributions": [
                        {
                            "totalSeed": 0.01 / 0.1368,
                            "totalProgressive":  0.03 / 0.1368,
                            "pool": "small"
                        },
                        {
                            "totalSeed": 0.01 / 0.1368,
                            "totalProgressive": 0.07 / 0.1368,
                            "pool": "large"
                        }
                    ],
                    "currencyRate": 0.1368,
                    "gameId": "test",
                    "brandId": 1,
                    "region": "eu",
                    "playerCurrency": "CNY",
                    "contributionAmount": 73.1,
                    "playerCode": "PL-CODE",
                    "gameCode": "test",
                    "roundId": 1
                },
                "externalTrxId": "123",
                "operationId": 1
            }
        };
        await consumer.process(undefined, [data]);

        const unloaded = await ContributionModel.contributionLogDb.findAll();
        expect(unloaded).to.be.empty;
        const remoteUnloaded = await ContributionModel.remoteContributionLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([
            {
                "brandId": 1,
                "region": "eu",
                "contributionAmount": "73.1",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "externalId": "123",
                "remoteTrxId": "bSF/rQAAArwBBBK9bSF/rfp0X60=",
                "remoteTrxRegion": "remote",
                "gameData": {
                    "amount": 10,
                    "externalId": "123",
                    "transactionId": "bSF/rQAAArwAAAK9bSF/rfp0X60="
                },
                "gameId": "test",
                "id": 1,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "gameCode": "test",
                "pool": "small",
                "progressive": "0.03",
                "seed": "0.01",
                "playerProgressive": "0.21929824561403508",
                "playerSeed": "0.07309941520467836",
                "trxId": "bSF/rQAAArwAAAK9bSF/rfp0X60=",
                "trxDate": new Date("2017-10-10T15:22:29.169Z"),
                "roundId": "1",
                "insertedAt": remoteUnloaded[0].insertedAt,
                "totalSeed": "0.07309941520467836",
                "totalProgressive": "0.21929824561403508",
            },
            {
                "brandId": 1,
                "region": "eu",
                "contributionAmount": "73.1",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "externalId": "123",
                "remoteTrxId": "bSF/rQAAArwBBBK9bSF/rfp0X60=",
                "remoteTrxRegion": "remote",
                "gameData": {
                    "amount": 10,
                    "externalId": "123",
                    "transactionId": "bSF/rQAAArwAAAK9bSF/rfp0X60="
                },
                "gameId": "test",
                "id": 2,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "gameCode": "test",
                "pool": "medium",
                "progressive": "0.04",
                "seed": "0.02",
                "playerProgressive": "0",
                "playerSeed": "0",
                "trxId": "bSF/rQAAArwAAAK9bSF/rfp0X60=",
                "trxDate": new Date("2017-10-10T15:22:29.169Z"),
                "roundId": "1",
                "insertedAt": remoteUnloaded[1].insertedAt,
                "totalSeed": "0",
                "totalProgressive": "0",
            },
            {
                "brandId": 1,
                "region": "eu",
                "contributionAmount": "73.1",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "externalId": "123",
                "remoteTrxId": "bSF/rQAAArwBBBK9bSF/rfp0X60=",
                "remoteTrxRegion": "remote",
                "gameData": {
                    "amount": 10,
                    "externalId": "123",
                    "transactionId": "bSF/rQAAArwAAAK9bSF/rfp0X60="
                },
                "gameId": "test",
                "id": 3,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "gameCode": "test",
                "pool": "large",
                "progressive": "0.07",
                "seed": "0.03",
                "playerProgressive": "0.5116959064327485",
                "playerSeed": "0.07309941520467836",
                "trxId": "bSF/rQAAArwAAAK9bSF/rfp0X60=",
                "trxDate": new Date("2017-10-10T15:22:29.169Z"),
                "roundId": "1",
                "insertedAt": remoteUnloaded[2].insertedAt,
                "totalSeed": "0.07309941520467836",
                "totalProgressive": "0.5116959064327485",
            }
        ]);
    });

    it("consumes contributes with statuses", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "id": "bSF\/rQAAArwAAAK9bSF\/rfp0X60=",
            "ts": new Date("2017-10-05T15:22:29.169Z"),
            "committedAt": new Date("2017-10-10T15:22:29.169Z"),
            "data": [
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "operatorSeed",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "operatorProgressive",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "operatorSeed",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "operatorProgressive",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "large"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "large"
                }
            ],
            "operation": {
                "operationName": "contribute",
                "params": {
                    "precision": **********,
                    "gameData": {
                        "externalId": "123",
                        "transactionId": "bSF\/rQAAArwAAAK9bSF\/rfp0X60=",
                        "amount": 10
                    },
                    "playerContributions": [
                        {
                            "seed": 0.01 / 0.1368,
                            "progressive":  0.03 / 0.1368,
                            "pool": "small"
                        },
                        {
                            "seed": 0.01 / 0.1368,
                            "progressive": 0.07 / 0.1368,
                            "pool": "medium"
                        },
                        {
                            "seed": 0.01 / 0.1368,
                            "progressive": 0.07 / 0.1368,
                            "pool": "large"
                        }
                    ],
                    "contributions": [
                        {
                            "totalSeed": 0.01 / 0.1368,
                            "totalProgressive":  0.03 / 0.1368,
                            "pool": "small",
                            "status": "pending"
                        },
                        {
                            "totalSeed": 0.01 / 0.1368,
                            "totalProgressive": 0.07 / 0.1368,
                            "pool": "medium",
                            "status": "pending"
                        },
                        {
                            "totalSeed": 0.01 / 0.1368,
                            "totalProgressive": 0.07 / 0.1368,
                            "pool": "large",
                            "status": "pending"
                        }
                    ],
                    "currencyRate": 0.1368,
                    "gameId": "test",
                    "brandId": 1,
                    "region": "eu",
                    "playerCurrency": "CNY",
                    "contributionAmount": 73.1,
                    "playerCode": "PL-CODE",
                    "gameCode": "test",
                    "roundId": 1
                },
                "externalTrxId": "123",
                "operationId": 1
            }
        };
        await consumer.process(undefined, [data]);

        const unloaded = await ContributionModel.contributionLogDb.findAll();
        expect(unloaded).to.deep.equal([
            {
                "brandId": 1,
                "region": "eu",
                "contributionAmount": "73.1",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameData": {
                    "amount": 10,
                    "externalId": "123",
                    "transactionId": "bSF/rQAAArwAAAK9bSF/rfp0X60="
                },
                "gameId": "test",
                "id": 1,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "gameCode": "test",
                "pool": "small",
                "progressive": "0.03",
                "seed": "0.01",
                "playerProgressive": "0.21929824561403508",
                "playerSeed": "0.07309941520467836",
                "trxId": "bSF/rQAAArwAAAK9bSF/rfp0X60=",
                "trxDate": new Date("2017-10-10T15:22:29.169Z"),
                "roundId": "1",
                "insertedAt": unloaded[0].insertedAt,
                "totalSeed": "0.07309941520467836",
                "totalProgressive": "0.21929824561403508",
                "status": "pending"
            },
            {
                "brandId": 1,
                "region": "eu",
                "contributionAmount": "73.1",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameData": {
                    "amount": 10,
                    "externalId": "123",
                    "transactionId": "bSF/rQAAArwAAAK9bSF/rfp0X60="
                },
                "gameId": "test",
                "id": 2,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "gameCode": "test",
                "pool": "medium",
                "progressive": "0.04",
                "seed": "0.02",
                "playerProgressive": "0.5116959064327485",
                "playerSeed": "0.07309941520467836",
                "trxId": "bSF/rQAAArwAAAK9bSF/rfp0X60=",
                "trxDate": new Date("2017-10-10T15:22:29.169Z"),
                "roundId": "1",
                "insertedAt": unloaded[1].insertedAt,
                "totalProgressive": "0.5116959064327485",
                "totalSeed": "0.07309941520467836",
                "status": "pending"
            },
            {
                "brandId": 1,
                "region": "eu",
                "contributionAmount": "73.1",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameData": {
                    "amount": 10,
                    "externalId": "123",
                    "transactionId": "bSF/rQAAArwAAAK9bSF/rfp0X60="
                },
                "gameId": "test",
                "id": 3,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "gameCode": "test",
                "pool": "large",
                "progressive": "0.07",
                "seed": "0.03",
                "playerProgressive": "0.5116959064327485",
                "playerSeed": "0.07309941520467836",
                "trxId": "bSF/rQAAArwAAAK9bSF/rfp0X60=",
                "trxDate": new Date("2017-10-10T15:22:29.169Z"),
                "roundId": "1",
                "insertedAt": unloaded[2].insertedAt,
                "totalSeed": "0.07309941520467836",
                "totalProgressive": "0.5116959064327485",
                "status": "pending"
            }
        ]);
        const remoteUnloaded = await ContributionModel.remoteContributionLogDb.findAll();
        expect(remoteUnloaded).to.be.empty;
    });

    it("skips wins", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "id": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
            "ts": new Date("2017-10-05T15:41:16.659Z"),
            "committedAt": new Date("2017-10-05T15:41:16.659Z"),
            "data": [{
                "trxType": "release",
                "version": 5,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -**********00,
                "value": -***********,
                "property": "seed",
                "prevValue": ********000,
                "account": "medium"
            }, {
                "trxType": "release",
                "version": 6,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -********000,
                "value": 0,
                "property": "progressive",
                "prevValue": ********000,
                "account": "medium"
            }],
            "operation": {
                "operationName": "release",
                "params": {
                    "gameId": "test",
                    "brandId": 1,
                    "region": "eu",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "wins": [{
                        "totalProgressive": 40,
                        "progressive": 40,
                        "pool": "medium",
                        "seed": 100,
                        "totalSeed": 20,
                        "winAmount": 1023.39,
                        "currencyRate": 0.1368,
                        "initialSeed": 100
                    }],
                    "playerCode": "PL-CODE",
                    "roundId": 1
                },
                "externalTrxId": "123",
                "operationId": 1001
            }
        };

        await consumer.process(undefined, [data]);

        const unloaded = await ContributionModel.contributionLogDb.findAll();
        expect(unloaded).to.deep.equal([]);
        const remoteUnloaded = await ContributionModel.remoteContributionLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([]);
    });

    it("consumes remote contributes", async () => {
        const data = {
            "isExternal": true,
            "version": "1",
            "id": "bddiLAAAAtQAAALUbddiLJvi96U=",
            "ts": new Date("2017-10-05T15:41:16.659Z"),
            "committedAt": new Date("2017-10-05T15:41:16.659Z"),
            "operation": {
                "operationId": 1,
                "operationName": "remote-contribute",
                "externalTrxId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                "gameId": "1",
                "params": {
                    "totalSeed": "20",
                    "totalProgressive": "40",
                    "remoteTrxId": "bddiLgAAAtUAAALUbddiLgLLN4E=",
                    "remoteTrxRegion": "remote",
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "roundId": "1",
                    "contributionAmount": 10,
                    "currencyRate": 0.1368,
                    "gameData": {
                        "transactionId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                        "roundId": "1",
                        "amount": 10
                    },
                    "contributions": [
                        {
                            "pool": "small",
                            "seed": 0.01,
                            "progressive": 0.02,
                            "totalSeed": 0.01,
                            "totalProgressive": 0.02
                        },
                        {
                            "pool": "medium",
                            "seed": 0.1,
                            "progressive": 0.2,
                            "totalSeed": 0.1,
                            "totalProgressive": 0.2
                        }
                    ],
                    "playerContributions": [
                        {
                            "pool": "small",
                            "seed": 0.01,
                            "progressive": 0.02
                        },
                        {
                            "pool": "medium",
                            "seed": 0.1,
                            "progressive": 0.2
                        }
                    ]
                }
            },
            "data": [
                {
                    "account": "$$metaInf",
                    "property": "remoteCall",
                    "amount": 1,
                    "value": 1,
                    "trxType": "remote",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "prevValue": undefined
                }
            ]
        };
        await consumer.process(undefined, [data]);

        const unloaded = await ContributionModel.contributionLogDb.findAll();
        expect(unloaded).to.deep.equal([
            {
                "brandId": 1,
                "contributionAmount": "10",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "externalId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                "remoteTrxId": "bddiLgAAAtUAAALUbddiLgLLN4E=",
                "remoteTrxRegion": "remote",
                "gameCode": "test",
                "gameData": {
                    "amount": 10,
                    "roundId": "1",
                    "transactionId": "bddiLAAAAtQAAALUbddiLJvi96U="
                },
                "gameId": "test",
                "roundId": "1",
                "id": 1,
                "insertedAt": unloaded[0].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "playerProgressive": "0.02",
                "playerSeed": "0.01",
                "pool": "small",
                "progressive": "0.02",
                "region": "eu",
                "seed": "0.01",
                "trxDate": new Date("2017-10-05T15:41:16.659Z"),
                "trxId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                "totalSeed": "0.01",
                "totalProgressive": "0.02",
                "status": null
            },
            {
                "brandId": 1,
                "contributionAmount": "10",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "externalId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                "remoteTrxId": "bddiLgAAAtUAAALUbddiLgLLN4E=",
                "remoteTrxRegion": "remote",
                "roundId": "1",
                "gameCode": "test",
                "gameData": {
                    "amount": 10,
                    "roundId": "1",
                    "transactionId": "bddiLAAAAtQAAALUbddiLJvi96U="
                },
                "gameId": "test",
                "id": 2,
                "insertedAt": unloaded[1].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "playerProgressive": "0.2",
                "playerSeed": "0.1",
                "pool": "medium",
                "progressive": "0.2",
                "region": "eu",
                "seed": "0.1",
                "trxDate": new Date("2017-10-05T15:41:16.659Z"),
                "trxId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                "totalSeed": "0.1",
                "totalProgressive": "0.2",
                "status": null
            }
        ]);
        const remoteUnloaded = await ContributionModel.remoteContributionLogDb.findAll();
        expect(remoteUnloaded).to.be.empty;
    });

    it("consumes remote contributes with empty seed value", async () => {
        const data = {
            "isExternal": true,
            "version": "1",
            "id": "bddiLAAAAtQAAALUbddiLJvi96U=",
            "ts": new Date("2017-10-05T15:41:16.659Z"),
            "committedAt": new Date("2017-10-05T15:41:16.659Z"),
            "operation": {
                "operationId": 1,
                "operationName": "remote-contribute",
                "externalTrxId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                "gameId": "1",
                "params": {
                    "remoteTrxId": "bddiLgAAAtUAAALUbddiLgLLN4E=",
                    "remoteTrxRegion": "remote",
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "roundId": "1",
                    "contributionAmount": 10,
                    "currencyRate": 0.1368,
                    "gameData": {
                        "transactionId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                        "roundId": "1",
                        "amount": 10
                    },
                    "contributions": [
                        {
                            "pool": "small",
                            "progressive": 0.02
                        },
                        {
                            "pool": "medium",
                            "progressive": 0.2
                        }
                    ],
                    "playerContributions": [
                        {
                            "pool": "small",
                            "progressive": 0.02
                        },
                        {
                            "pool": "medium",
                            "progressive": 0.2
                        }
                    ]
                }
            },
            "data": [
                {
                    "account": "$$metaInf",
                    "property": "remoteCall",
                    "amount": 1,
                    "value": 1,
                    "trxType": "remote",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "prevValue": undefined
                }
            ]
        };
        await consumer.process(undefined, [data]);

        const unloaded = await ContributionModel.contributionLogDb.findAll();
        expect(unloaded).to.deep.equal([
            {
                "brandId": 1,
                "contributionAmount": "10",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "externalId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                "remoteTrxId": "bddiLgAAAtUAAALUbddiLgLLN4E=",
                "remoteTrxRegion": "remote",
                "gameCode": "test",
                "gameData": {
                    "amount": 10,
                    "roundId": "1",
                    "transactionId": "bddiLAAAAtQAAALUbddiLJvi96U="
                },
                "gameId": "test",
                "roundId": "1",
                "id": 1,
                "insertedAt": unloaded[0].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "playerProgressive": "0.02",
                "playerSeed": "0",
                "pool": "small",
                "progressive": "0.02",
                "region": "eu",
                "seed": "0",
                "trxDate": new Date("2017-10-05T15:41:16.659Z"),
                "trxId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                "totalSeed": "0",
                "totalProgressive": "0",
                "status": null
            },
            {
                "brandId": 1,
                "contributionAmount": "10",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "externalId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                "remoteTrxId": "bddiLgAAAtUAAALUbddiLgLLN4E=",
                "remoteTrxRegion": "remote",
                "roundId": "1",
                "gameCode": "test",
                "gameData": {
                    "amount": 10,
                    "roundId": "1",
                    "transactionId": "bddiLAAAAtQAAALUbddiLJvi96U="
                },
                "gameId": "test",
                "id": 2,
                "insertedAt": unloaded[1].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "playerProgressive": "0.2",
                "playerSeed": "0",
                "pool": "medium",
                "progressive": "0.2",
                "region": "eu",
                "seed": "0",
                "trxDate": new Date("2017-10-05T15:41:16.659Z"),
                "trxId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                "totalSeed": "0",
                "totalProgressive": "0",
                "status": null
            }
        ]);
        const remoteUnloaded = await ContributionModel.remoteContributionLogDb.findAll();
        expect(remoteUnloaded).to.be.empty;
    });

    it("consumes remote operation without contributions", async () => {
        const data = {
            "isExternal": true,
            "version": "1",
            "id": "bddiLAAAAtQAAALUbddiLJvi96U=",
            "ts": new Date("2017-10-05T15:41:16.659Z"),
            "committedAt": new Date("2017-10-05T15:41:16.659Z"),
            "operation": {
                "operationId": 1,
                "operationName": "remote-contribute",
                "externalTrxId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                "gameId": "1",
                "params": {
                    "remoteTrxId": "bddiLgAAAtUAAALUbddiLgLLN4E=",
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "roundId": "1",
                    "contributionAmount": 10,
                    "currencyRate": 0.1368,
                    "gameData": {
                        "transactionId": "bddiLAAAAtQAAALUbddiLJvi96U=",
                        "roundId": "1",
                        "amount": 10
                    }
                }
            },
            "data": [
                {
                    "account": "$$metaInf",
                    "property": "remoteCall",
                    "amount": 1,
                    "value": 1,
                    "trxType": "remote",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "prevValue": undefined
                }
            ]
        };
        await consumer.process(undefined, [data]);

        const unloaded = await ContributionModel.contributionLogDb.findAll();
        expect(unloaded).to.be.empty;
        const remoteUnloaded = await ContributionModel.remoteContributionLogDb.findAll();
        expect(remoteUnloaded).to.be.empty;
    });
});
