import { expect } from "chai";
import { WinConsumer } from "../../skywind/history/win.consumer";
import * as WinModel from "../../skywind/history/win.log";
import { flushDb } from "../helpers";

describe("Win Consumer", () => {

    const consumer = new WinConsumer();

    beforeEach(async () => {
        await flushDb();
        await WinModel.winLogDb.sync();
        await WinModel.remoteWinLogDb.sync();
    });

    it("consumes wins", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "id": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
            "ts": new Date("2017-10-05T15:41:16.659Z"),
            "committedAt": new Date("2017-10-10T15:41:16.659Z"),
            "data": [{
                "trxType": "release",
                "version": 5,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -************,
                "value": -***********,
                "property": "seed",
                "prevValue": **********0,
                "account": "medium"
            }, {
                "trxType": "release",
                "version": 6,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -**********0,
                "value": 0,
                "property": "progressive",
                "prevValue": **********0,
                "account": "medium"
            }, {
                "trxType": "release",
                "version": 5,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -***********,
                "value": -**********,
                "property": "seed",
                "prevValue": **********,
                "account": "small"
            }, {
                "trxType": "release",
                "version": 6,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -**********,
                "value": 0,
                "property": "progressive",
                "prevValue": **********,
                "account": "small"
            }, {
                "trxType": "release",
                "version": 5,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -***********,
                "value": -1**********,
                "property": "seed",
                "prevValue": **********,
                "account": "small"
            }, {
                "trxType": "release",
                "version": 6,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": 0,
                "value": 0,
                "property": "progressive",
                "prevValue": **********,
                "account": "small"
            }],
            "operation": {
                "operationName": "release",
                "params": {
                    "gameId": "test",
                    "brandId": 1,
                    "region": "eu",
                    "playerCurrency": "CNY",
                    "betAmount": 5,
                    "wins": [{
                        "totalProgressive": 40,
                        "progressive": 40,
                        "pool": "medium",
                        "seed": 100,
                        "totalSeed": 20,
                        "winAmount": 1023.39,
                        "currencyRate": 0.1368,
                        "initialSeed": 100,
                        "seedSinceLastWin": 1,
                        "progressiveSinceLastWin": 2
                    }, {
                        "totalProgressive": 4,
                        "progressive": 4,
                        "pool": "small",
                        "seed": 10,
                        "totalSeed": 2,
                        "winAmount": 102.34,
                        "currencyRate": 0.1368,
                        "initialSeed": 10,
                        "seedSinceLastWin": 1,
                        "progressiveSinceLastWin": 2
                    }, {
                        "totalProgressive": 4,
                        "progressive": 0,
                        "pool": "small",
                        "seed": 10,
                        "totalSeed": 10,
                        "winAmount": 102,
                        "currencyRate": 0.1368,
                        "initialSeed": 10,
                        "seedSinceLastWin": 1,
                        "progressiveSinceLastWin": 2
                    }],
                    "playerCode": "PL-CODE",
                    "roundId": "1",
                    "gameCode": "test"
                },
                "externalTrxId": "123",
                "operationId": 1001
            }
        };

        await consumer.process(undefined, [data]);

        const unloaded = await WinModel.winLogDb.findAll();
        expect(unloaded).to.deep.equal([
            {
                "brandId": 1,
                "region": "eu",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "eventId": 0,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameId": "test",
                "id": 1,
                "initialSeed": "100",
                "insertedAt": unloaded[0].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "pool": "medium",
                "progressive": "40",
                "seed": "100",
                "totalProgressive": "40",
                "totalSeed": "20",
                "trxId": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
                "trxDate": new Date("2017-10-10T15:41:16.659Z"),
                "winAmount": "1023.39",
                "roundId": "1",
                "gameCode": "test",
                "seedSinceLastWin": "1",
                "progressiveSinceLastWin": "2",
                "betAmount": "5",
                "status": null,
                "info": null,
            },
            {
                "brandId": 1,
                "region": "eu",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "eventId": 1,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameId": "test",
                "id": 2,
                "initialSeed": "10",
                "insertedAt": unloaded[1].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "pool": "small",
                "progressive": "4",
                "seed": "10",
                "totalProgressive": "4",
                "totalSeed": "2",
                "trxId": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
                "trxDate": new Date("2017-10-10T15:41:16.659Z"),
                "winAmount": "102.34",
                "roundId": "1",
                "gameCode": "test",
                "seedSinceLastWin": "1",
                "progressiveSinceLastWin": "2",
                "betAmount": "5",
                "status": null,
                "info": null,
            },
            {
                "brandId": 1,
                "region": "eu",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "eventId": 2,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameId": "test",
                "id": 3,
                "initialSeed": "10",
                "insertedAt": unloaded[2].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "pool": "small",
                "progressive": "0",
                "seed": "10",
                "totalProgressive": "4",
                "totalSeed": "10",
                "trxId": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
                "trxDate": new Date("2017-10-10T15:41:16.659Z"),
                "winAmount": "102",
                "roundId": "1",
                "gameCode": "test",
                "seedSinceLastWin": "1",
                "progressiveSinceLastWin": "2",
                "betAmount": "5",
                "status": null,
                "info": null,
            }
        ]);
        const remoteUnloaded = await WinModel.remoteWinLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([]);
    });

    it("consumes wins from remote", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "id": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
            "ts": new Date("2017-10-05T15:41:16.659Z"),
            "committedAt": new Date("2017-10-10T15:41:16.659Z"),
            "data": [{
                "trxType": "release",
                "version": 5,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -************,
                "value": -***********,
                "property": "seed",
                "prevValue": **********0,
                "account": "medium"
            }, {
                "trxType": "release",
                "version": 6,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -**********0,
                "value": 0,
                "property": "progressive",
                "prevValue": **********0,
                "account": "medium"
            }, {
                "trxType": "release",
                "version": 5,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -***********,
                "value": -**********,
                "property": "seed",
                "prevValue": **********,
                "account": "small"
            }, {
                "trxType": "release",
                "version": 6,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -**********,
                "value": 0,
                "property": "progressive",
                "prevValue": **********,
                "account": "small"
            }, {
                "trxType": "release",
                "version": 5,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -***********,
                "value": -1**********,
                "property": "seed",
                "prevValue": **********,
                "account": "small"
            }, {
                "trxType": "release",
                "version": 6,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": 0,
                "value": 0,
                "property": "progressive",
                "prevValue": **********,
                "account": "small"
            }],
            "operation": {
                "operationName": "release",
                "params": {
                    "fromRemote": true,
                    "remoteTrxId": "bTKz6wBBBrwAAAK9bTKz67ysDB0=",
                    "remoteTrxRegion": "remote",
                    "gameId": "test",
                    "brandId": 1,
                    "region": "eu",
                    "playerCurrency": "CNY",
                    "wins": [{
                        "totalProgressive": 40,
                        "progressive": 40,
                        "pool": "medium",
                        "seed": 100,
                        "totalSeed": 20,
                        "winAmount": 1023.39,
                        "currencyRate": 0.1368,
                        "initialSeed": 100,
                        "seedSinceLastWin": 1,
                        "progressiveSinceLastWin": 2
                    }, {
                        "totalProgressive": 4,
                        "progressive": 4,
                        "pool": "small",
                        "seed": 10,
                        "totalSeed": 2,
                        "winAmount": 102.34,
                        "currencyRate": 0.1368,
                        "initialSeed": 10,
                        "seedSinceLastWin": 1,
                        "progressiveSinceLastWin": 2
                    }, {
                        "totalProgressive": 4,
                        "progressive": 0,
                        "pool": "small",
                        "seed": 10,
                        "totalSeed": 10,
                        "winAmount": 102,
                        "currencyRate": 0.1368,
                        "initialSeed": 10,
                        "seedSinceLastWin": 1,
                        "progressiveSinceLastWin": 2
                    }],
                    "playerCode": "PL-CODE",
                    "roundId": "1",
                    "gameCode": "test",
                    "betAmount": 5,
                },
                "externalTrxId": "123",
                "operationId": 1001
            }
        };

        await consumer.process(undefined, [data]);

        const remoteUnloaded = await WinModel.remoteWinLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([
            {
                "brandId": 1,
                "region": "eu",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "eventId": 0,
                "externalId": "123",
                "remoteTrxId": "bTKz6wBBBrwAAAK9bTKz67ysDB0=",
                "remoteTrxRegion": "remote",
                "gameId": "test",
                "id": 1,
                "initialSeed": "100",
                "insertedAt": remoteUnloaded[0].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "pool": "medium",
                "progressive": "40",
                "seed": "100",
                "totalProgressive": "40",
                "totalSeed": "20",
                "trxId": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
                "trxDate": new Date("2017-10-10T15:41:16.659Z"),
                "winAmount": "1023.39",
                "roundId": "1",
                "gameCode": "test",
                "seedSinceLastWin": "1",
                "progressiveSinceLastWin": "2",
                "betAmount": "5",
                "info": null,
            },
            {
                "brandId": 1,
                "region": "eu",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "eventId": 1,
                "externalId": "123",
                "remoteTrxId": "bTKz6wBBBrwAAAK9bTKz67ysDB0=",
                "remoteTrxRegion": "remote",
                "gameId": "test",
                "id": 2,
                "initialSeed": "10",
                "insertedAt": remoteUnloaded[1].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "pool": "small",
                "progressive": "4",
                "seed": "10",
                "totalProgressive": "4",
                "totalSeed": "2",
                "trxId": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
                "trxDate": new Date("2017-10-10T15:41:16.659Z"),
                "winAmount": "102.34",
                "roundId": "1",
                "gameCode": "test",
                "seedSinceLastWin": "1",
                "progressiveSinceLastWin": "2",
                "betAmount": "5",
                "info": null,
            },
            {
                "brandId": 1,
                "region": "eu",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "eventId": 2,
                "externalId": "123",
                "remoteTrxId": "bTKz6wBBBrwAAAK9bTKz67ysDB0=",
                "remoteTrxRegion": "remote",
                "gameId": "test",
                "id": 3,
                "initialSeed": "10",
                "insertedAt": remoteUnloaded[2].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "pool": "small",
                "progressive": "0",
                "seed": "10",
                "totalProgressive": "4",
                "totalSeed": "10",
                "trxId": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
                "trxDate": new Date("2017-10-10T15:41:16.659Z"),
                "winAmount": "102",
                "roundId": "1",
                "gameCode": "test",
                "seedSinceLastWin": "1",
                "progressiveSinceLastWin": "2",
                "betAmount": "5",
                "info": null,
            }
        ]);
        const unloaded = await WinModel.winLogDb.findAll();
        expect(unloaded).to.deep.equal([]);
    });

    it("consumes wins with statuses", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "id": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
            "ts": new Date("2017-10-05T15:41:16.659Z"),
            "committedAt": new Date("2017-10-10T15:41:16.659Z"),
            "data": [{
                "trxType": "release",
                "version": 5,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -************,
                "value": -***********,
                "property": "seed",
                "prevValue": **********0,
                "account": "medium"
            }, {
                "trxType": "release",
                "version": 6,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -**********0,
                "value": 0,
                "property": "progressive",
                "prevValue": **********0,
                "account": "medium"
            }, {
                "trxType": "release",
                "version": 5,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -***********,
                "value": -**********,
                "property": "seed",
                "prevValue": **********,
                "account": "small"
            }, {
                "trxType": "release",
                "version": 6,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -**********,
                "value": 0,
                "property": "progressive",
                "prevValue": **********,
                "account": "small"
            }, {
                "trxType": "release",
                "version": 5,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -***********,
                "value": -1**********,
                "property": "seed",
                "prevValue": **********,
                "account": "small"
            }, {
                "trxType": "release",
                "version": 6,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": 0,
                "value": 0,
                "property": "progressive",
                "prevValue": **********,
                "account": "small"
            }],
            "operation": {
                "operationName": "release",
                "params": {
                    "gameId": "test",
                    "brandId": 1,
                    "region": "eu",
                    "playerCurrency": "CNY",
                    "betAmount": 5,
                    "wins": [{
                        "totalProgressive": 40,
                        "progressive": 40,
                        "pool": "medium",
                        "seed": 100,
                        "totalSeed": 20,
                        "betAmount": 10,
                        "winAmount": 1023.39,
                        "currencyRate": 0.1368,
                        "initialSeed": 100,
                        "seedSinceLastWin": 1,
                        "progressiveSinceLastWin": 2,
                        "status": "resolved",
                        "info": {
                            "external": {
                                "externalId": "JP-TEST_hash1",
                                "externalStartDate": "2023-10-16T14:43:04.902Z"
                            }
                        }
                    }, {
                        "totalProgressive": 4,
                        "progressive": 4,
                        "pool": "small",
                        "seed": 10,
                        "totalSeed": 2,
                        "betAmount": 5,
                        "winAmount": 102.34,
                        "currencyRate": 0.1368,
                        "initialSeed": 10,
                        "seedSinceLastWin": 1,
                        "progressiveSinceLastWin": 2,
                        "status": "resolved",
                        "info": {
                            "external": {
                                "externalId": "JP-TEST_hash2",
                                "externalStartDate": "2023-10-16T14:43:04.902Z"
                            }
                        }
                    }, {
                        "totalProgressive": 4,
                        "progressive": 0,
                        "pool": "small",
                        "seed": 10,
                        "totalSeed": 10,
                        "betAmount": 4,
                        "winAmount": 102,
                        "currencyRate": 0.1368,
                        "initialSeed": 10,
                        "seedSinceLastWin": 1,
                        "progressiveSinceLastWin": 2,
                        "status": "resolved",
                        "info": {
                            "external": {
                                "externalId": "JP-TEST_hash2",
                                "externalStartDate": "2023-10-16T14:43:04.902Z"
                            }
                        }
                    }],
                    "playerCode": "PL-CODE",
                    "roundId": "1",
                    "gameCode": "test"
                },
                "externalTrxId": "123",
                "operationId": 1001
            }
        };

        await consumer.process(undefined, [data]);

        const unloaded = await WinModel.winLogDb.findAll();
        expect(unloaded).to.deep.equal([
            {
                "brandId": 1,
                "region": "eu",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "eventId": 0,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameId": "test",
                "id": 1,
                "initialSeed": "100",
                "insertedAt": unloaded[0].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "pool": "medium",
                "progressive": "40",
                "seed": "100",
                "totalProgressive": "40",
                "totalSeed": "20",
                "trxId": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
                "trxDate": new Date("2017-10-10T15:41:16.659Z"),
                "winAmount": "1023.39",
                "roundId": "1",
                "gameCode": "test",
                "seedSinceLastWin": "1",
                "progressiveSinceLastWin": "2",
                "betAmount": "5",
                "status": "resolved",
                "info": {
                    "external": {
                        "externalId": "JP-TEST_hash1",
                        "externalStartDate": "2023-10-16T14:43:04.902Z"
                    }
                }
            },
            {
                "brandId": 1,
                "region": "eu",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "eventId": 2,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameId": "test",
                "id": 2,
                "initialSeed": "10",
                "insertedAt": unloaded[1].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "pool": "small",
                "progressive": "0",
                "seed": "10",
                "totalProgressive": "4",
                "totalSeed": "10",
                "trxId": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
                "trxDate": new Date("2017-10-10T15:41:16.659Z"),
                "winAmount": "102",
                "roundId": "1",
                "gameCode": "test",
                "seedSinceLastWin": "1",
                "progressiveSinceLastWin": "2",
                "betAmount": "5",
                "status": "resolved",
                "info": {
                    "external": {
                        "externalId": "JP-TEST_hash2",
                        "externalStartDate": "2023-10-16T14:43:04.902Z"
                    }
                }
            }
        ]);
        const remoteUnloaded = await WinModel.remoteWinLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([]);
    });

    it("consumes wins with info", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "id": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
            "ts": new Date("2017-10-05T15:41:16.659Z"),
            "committedAt": new Date("2017-10-10T15:41:16.659Z"),
            "data": [{
                "trxType": "release",
                "version": 5,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -************,
                "value": -***********,
                "property": "seed",
                "prevValue": **********0,
                "account": "medium"
            }, {
                "trxType": "release",
                "version": 6,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -**********0,
                "value": 0,
                "property": "progressive",
                "prevValue": **********0,
                "account": "medium"
            }, {
                "trxType": "release",
                "version": 5,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -***********,
                "value": -**********,
                "property": "seed",
                "prevValue": **********,
                "account": "small"
            }, {
                "trxType": "release",
                "version": 6,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -**********,
                "value": 0,
                "property": "progressive",
                "prevValue": **********,
                "account": "small"
            }, {
                "trxType": "release",
                "version": 5,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": -***********,
                "value": -1**********,
                "property": "seed",
                "prevValue": **********,
                "account": "small"
            }, {
                "trxType": "release",
                "version": 6,
                "walletKey": "jackpot:JP-TEST:EUR",
                "amount": 0,
                "value": 0,
                "property": "progressive",
                "prevValue": **********,
                "account": "small"
            }],
            "operation": {
                "operationName": "release",
                "params": {
                    "gameId": "test",
                    "brandId": 1,
                    "region": "eu",
                    "playerCurrency": "CNY",
                    "betAmount": 5,
                    "wins": [{
                        "totalProgressive": 40,
                        "progressive": 40,
                        "pool": "medium",
                        "seed": 100,
                        "totalSeed": 20,
                        "winAmount": 1023.39,
                        "currencyRate": 0.1368,
                        "initialSeed": 100,
                        "seedSinceLastWin": 1,
                        "progressiveSinceLastWin": 2,
                        "info": {
                            "external": {
                                "externalId": "JP-TEST_hash1",
                                "externalStartDate": "2023-10-16T14:43:04.902Z"
                            }
                        }
                    }, {
                        "totalProgressive": 4,
                        "progressive": 4,
                        "pool": "small",
                        "seed": 10,
                        "totalSeed": 2,
                        "winAmount": 102.34,
                        "currencyRate": 0.1368,
                        "initialSeed": 10,
                        "seedSinceLastWin": 1,
                        "progressiveSinceLastWin": 2,
                        "info": {
                            "external": {
                                "externalId": "JP-TEST_hash2",
                                "externalStartDate": "2023-10-16T14:43:04.902Z"
                            }
                        }
                    }, {
                        "totalProgressive": 4,
                        "progressive": 0,
                        "pool": "small",
                        "seed": 10,
                        "totalSeed": 10,
                        "winAmount": 102,
                        "currencyRate": 0.1368,
                        "initialSeed": 10,
                        "seedSinceLastWin": 1,
                        "progressiveSinceLastWin": 2,
                        "info": {
                            "external": {
                                "externalId": "JP-TEST_hash3",
                                "externalStartDate": "2023-10-16T14:43:04.902Z"
                            }
                        }
                    }],
                    "playerCode": "PL-CODE",
                    "roundId": "1",
                    "gameCode": "test"
                },
                "externalTrxId": "123",
                "operationId": 1001
            }
        };

        await consumer.process(undefined, [data]);

        const unloaded = await WinModel.winLogDb.findAll();
        expect(unloaded).to.deep.equal([
            {
                "brandId": 1,
                "region": "eu",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "eventId": 0,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameId": "test",
                "id": 1,
                "initialSeed": "100",
                "insertedAt": unloaded[0].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "pool": "medium",
                "progressive": "40",
                "seed": "100",
                "totalProgressive": "40",
                "totalSeed": "20",
                "trxId": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
                "trxDate": new Date("2017-10-10T15:41:16.659Z"),
                "winAmount": "1023.39",
                "roundId": "1",
                "gameCode": "test",
                "seedSinceLastWin": "1",
                "progressiveSinceLastWin": "2",
                "betAmount": "5",
                "status": null,
                "info": {
                    "external": {
                        "externalId": "JP-TEST_hash1",
                        "externalStartDate": "2023-10-16T14:43:04.902Z"
                    }
                }
            },
            {
                "brandId": 1,
                "region": "eu",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "eventId": 1,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameId": "test",
                "id": 2,
                "initialSeed": "10",
                "insertedAt": unloaded[1].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "pool": "small",
                "progressive": "4",
                "seed": "10",
                "totalProgressive": "4",
                "totalSeed": "2",
                "trxId": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
                "trxDate": new Date("2017-10-10T15:41:16.659Z"),
                "winAmount": "102.34",
                "roundId": "1",
                "gameCode": "test",
                "seedSinceLastWin": "1",
                "progressiveSinceLastWin": "2",
                "betAmount": "5",
                "status": null,
                "info": {
                    "external": {
                        "externalId": "JP-TEST_hash2",
                        "externalStartDate": "2023-10-16T14:43:04.902Z"
                    }
                }
            },
            {
                "brandId": 1,
                "region": "eu",
                "currency": "EUR",
                "currencyRate": "0.1368",
                "eventId": 2,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameId": "test",
                "id": 3,
                "initialSeed": "10",
                "insertedAt": unloaded[2].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "pool": "small",
                "progressive": "0",
                "seed": "10",
                "totalProgressive": "4",
                "totalSeed": "10",
                "trxId": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
                "trxDate": new Date("2017-10-10T15:41:16.659Z"),
                "winAmount": "102",
                "roundId": "1",
                "gameCode": "test",
                "seedSinceLastWin": "1",
                "progressiveSinceLastWin": "2",
                "betAmount": "5",
                "status": null,
                "info": {
                    "external": {
                        "externalId": "JP-TEST_hash3",
                        "externalStartDate": "2023-10-16T14:43:04.902Z"
                    }
                }
            }
        ]);
        const remoteUnloaded = await WinModel.remoteWinLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([]);
    });

    it("skips contributes", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "id": "bSF\/rQAAArwAAAK9bSF\/rfp0X60=",
            "ts": new Date("2017-10-05T15:22:29.169Z"),
            "committedAt": new Date("2017-10-05T15:22:29.169Z"),
            "data": [
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "small"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "medium"
                },
                {
                    "trxType": "contribution",
                    "version": 3,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "large"
                },
                {
                    "trxType": "contribution",
                    "version": 4,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ********,
                    "value": ********,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "large"
                }
            ],
            "operation": {
                "operationName": "contribute",
                "params": {
                    "precision": **********,
                    "gameData": {
                        "externalId": "123",
                        "transactionId": "bSF\/rQAAArwAAAK9bSF\/rfp0X60=",
                        "amount": 10
                    },
                    "currencyRate": 0.1368,
                    "gameId": "test",
                    "brandId": 1,
                    "region": "eu",
                    "playerCurrency": "CNY",
                    "contributionAmount": 73.1,
                    "playerCode": "PL-CODE",
                    "roundId": "1",
                    "gameCode": "test"
                },
                "externalTrxId": "123",
                "operationId": 1
            }
        };
        await consumer.process(undefined, [data]);

        const unloaded = await WinModel.winLogDb.findAll();
        expect(unloaded).to.deep.equal([]);
        const remoteUnloaded = await WinModel.remoteWinLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([]);
    });

    it("consumes remote wins", async () => {
        const data = {
            "isExternal": true,
            "version": "1",
            "ts": new Date("2017-10-05T15:41:16.659Z"),
            "committedAt": new Date("2017-10-10T15:41:16.659Z"),
            "id": "be1jIwAAAtQAAALUbe1jIyptU7c=",
            "operation": {
                "operationId": 1001,
                "operationName": "remote-release",
                "externalTrxId": "be1jJgAAAtUAAALUbe1jJlBTAGg=",
                "gameId": "1",
                "params": {
                    "remoteTrxId": "be1jJgBBBtUAAALUbe1jJlBTAGg=",
                    "remoteTrxRegion": "remote",
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "roundId": "1",
                    "betAmount": 5,
                    "winResults": [
                        {
                            "pool": "small",
                            "amount": 10,
                            "seed": 2,
                            "progressive": 8,
                            "playerAmount": 73.1,
                            "exchangeRate": 0.1368
                        }
                    ],
                    "wins": [
                        {
                            "pool": "small",
                            "winAmount": 10,
                            "currencyRate": 0.1368,
                            "initialSeed": 2,
                            "seed": 2,
                            "progressive": 8,
                            "totalSeed": 1,
                            "totalProgressive": 8,
                            "seedSinceLastWin": 1,
                            "progressiveSinceLastWin": 8
                        }
                    ]
                }
            },
            "data": [
                {
                    "account": "$$metaInf",
                    "property": "remoteCall",
                    "amount": 1,
                    "trxType": "remote",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "value": 1,
                    "prevValue": undefined
                }
            ]
        };

        await consumer.process(undefined, [data]);

        const unloaded = await WinModel.winLogDb.findAll();
        expect(unloaded).to.deep.equal([
            {
                "brandId": 1,
                "currency": "EUR",
                "currencyRate": "0.1368",
                "eventId": 0,
                "externalId": "be1jJgAAAtUAAALUbe1jJlBTAGg=",
                "remoteTrxId": "be1jJgBBBtUAAALUbe1jJlBTAGg=",
                "remoteTrxRegion": "remote",
                "gameCode": "test",
                "gameId": "test",
                "id": 1,
                "initialSeed": "2",
                "insertedAt": unloaded[0].insertedAt,
                "jackpotId": "JP-TEST",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "pool": "small",
                "progressive": "8",
                "progressiveSinceLastWin": "8",
                "region": "eu",
                "roundId": "1",
                "seed": "2",
                "seedSinceLastWin": "1",
                "totalProgressive": "8",
                "totalSeed": "1",
                "trxDate": new Date("2017-10-10T15:41:16.659Z"),
                "trxId": "be1jIwAAAtQAAALUbe1jIyptU7c=",
                "winAmount": "10",
                "betAmount": "5",
                "status": null,
                "info": null,
            }
        ]);
        const remoteUnloaded = await WinModel.remoteWinLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([]);
    });
});
