import { expect } from "chai";
import * as TransferModel from "../../skywind/history/transfer.log";
import { flushDb } from "../helpers";
import { TransferConsumer } from "../../skywind/history/transfer.consumer";

describe("Transfer Consumer", () => {

    const consumer = new TransferConsumer();

    beforeEach(async () => {
        await flushDb();
        await TransferModel.transferLogDb.sync();
        await TransferModel.remoteTransferLogDb.sync();
    });

    it("consumes transfers", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "id": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
            "ts": new Date("2017-10-05T15:41:16.659Z"),
            "committedAt": new Date("2017-10-10T15:41:16.659Z"),
            "data": [
                {
                    "trxType": "transfer",
                    "version": 5,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": -**********00,
                    "value": -***********,
                    "property": "seed",
                    "prevValue": ***********,
                    "account": "main"
                }, {
                    "trxType": "transfer",
                    "version": 6,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": -***********,
                    "value": 0,
                    "property": "progressive",
                    "prevValue": ***********,
                    "account": "main"
                }, {
                    "trxType": "transfer",
                    "version": 5,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": **********00,
                    "value": ***********,
                    "property": "seed",
                    "prevValue": ***********,
                    "account": "prize"
                }, {
                    "trxType": "transfer",
                    "version": 6,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": ***********,
                    "value": 0,
                    "property": "progressive",
                    "prevValue": ***********,
                    "account": "main"
                }
            ],
            "operation": {
                "operationName": "release",
                "params": {
                    "gameId": "test",
                    "brandId": 1,
                    "region": "eu",
                    "playerCurrency": "CNY",
                    "wins": [
                        {
                            "totalProgressive": 40,
                            "progressive": 40,
                            "pool": "main",
                            "seed": 100,
                            "totalSeed": 20,
                            "winAmount": 1023.39,
                            "currencyRate": 0.1368,
                            "initialSeed": 100,
                            "seedSinceLastWin": 1,
                            "progressiveSinceLastWin": 2
                        }, {
                            "totalProgressive": 40,
                            "progressive": 40,
                            "pool": "main",
                            "seed": 100,
                            "totalSeed": 20,
                            "initialSeed": 100,
                            "seedSinceLastWin": 1,
                            "progressiveSinceLastWin": 2,
                            "transferPool": "prize"
                        }
                    ],
                    "playerCode": "PL-CODE",
                    "roundId": "1",
                    "gameCode": "test"
                },
                "externalTrxId": "123",
                "operationId": 1001
            }
        };

        await consumer.process(undefined, [data]);

        const unloaded = await TransferModel.transferLogDb.findAll();
        expect(unloaded).to.deep.equal([
            {
                "currency": "EUR",
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameId": "test",
                "id": 1,
                "jackpotId": "JP-TEST",
                "fromPool": "main",
                "fromPoolProgressive": "40",
                "fromPoolSeed": "20",
                "progressive": "40",
                "seed": "100",
                "toPool": "prize",
                "toPoolProgressive": "0",
                "toPoolSeed": "0",
                "trxId": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
                "trxDate": new Date("2017-10-10T15:41:16.659Z"),
            }
        ]);
        const remoteUnloaded = await TransferModel.remoteTransferLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([]);
    });

    it("consumes transfers from remote", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "id": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
            "ts": new Date("2017-10-05T15:41:16.659Z"),
            "committedAt": new Date("2017-10-10T15:41:16.659Z"),
            "data": [
                {
                    "trxType": "release",
                    "version": 6,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": 1,
                    "value": 2,
                    "property": "seqId",
                    "prevValue": 1,
                    "account": "$$metaInf"
                }
            ],
            "operation": {
                "operationName": "remote-release",
                "params": {
                    "fromRemote": true,
                    "remoteTrxId": "bTKz6wBBBrwAAAK9bTKz67ysDB0=",
                    "remoteTrxRegion": "remote",
                    "gameId": "test",
                    "brandId": 1,
                    "region": "eu",
                    "playerCurrency": "CNY",
                    "wins": [
                        {
                            "totalProgressive": 40,
                            "progressive": 40,
                            "pool": "main",
                            "seed": 100,
                            "totalSeed": 20,
                            "winAmount": 1023.39,
                            "currencyRate": 0.1368,
                            "initialSeed": 100,
                            "seedSinceLastWin": 1,
                            "progressiveSinceLastWin": 2
                        }, {
                            "totalProgressive": 40,
                            "progressive": 40,
                            "pool": "main",
                            "seed": 100,
                            "totalSeed": 20,
                            "initialSeed": 100,
                            "seedSinceLastWin": 1,
                            "progressiveSinceLastWin": 2,
                            "transferPool": "prize"
                        }
                    ],
                    "playerCode": "PL-CODE",
                    "roundId": "1",
                    "gameCode": "test"
                },
                "externalTrxId": "123",
                "operationId": 1001
            }
        };

        await consumer.process(undefined, [data]);

        const remoteUnloaded = await TransferModel.remoteTransferLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([
            {
                "currency": "EUR",
                "externalId": "123",
                "remoteTrxId": "bTKz6wBBBrwAAAK9bTKz67ysDB0=",
                "remoteTrxRegion": "remote",
                "gameId": "test",
                "id": 1,
                "jackpotId": "JP-TEST",
                "fromPool": "main",
                "fromPoolProgressive": "40",
                "fromPoolSeed": "20",
                "progressive": "40",
                "seed": "100",
                "toPool": "prize",
                "toPoolProgressive": "0",
                "toPoolSeed": "0",
                "trxId": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
                "trxDate": new Date("2017-10-10T15:41:16.659Z")
            }
        ]);
        const unloaded = await TransferModel.transferLogDb.findAll();
        expect(unloaded).to.deep.equal([]);
    });

    it("skips wins", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "id": "bTKz6wAAArwAAAK9bTKz67ysDB0=",
            "ts": new Date("2017-10-05T15:41:16.659Z"),
            "committedAt": new Date("2017-10-10T15:41:16.659Z"),
            "data": [
                {
                    "trxType": "release",
                    "version": 5,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": -**********00,
                    "value": -***********,
                    "property": "seed",
                    "prevValue": ***********,
                    "account": "main"
                }, {
                    "trxType": "release",
                    "version": 6,
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": -***********,
                    "value": 0,
                    "property": "progressive",
                    "prevValue": ***********,
                    "account": "main"
                }
            ],
            "operation": {
                "operationName": "release",
                "params": {
                    "gameId": "test",
                    "brandId": 1,
                    "region": "eu",
                    "playerCurrency": "CNY",
                    "wins": [
                        {
                            "totalProgressive": 40,
                            "progressive": 40,
                            "pool": "main",
                            "seed": 100,
                            "totalSeed": 20,
                            "winAmount": 1023.39,
                            "currencyRate": 0.1368,
                            "initialSeed": 100,
                            "seedSinceLastWin": 1,
                            "progressiveSinceLastWin": 2
                        }
                    ],
                    "playerCode": "PL-CODE",
                    "roundId": "1",
                    "gameCode": "test"
                },
                "externalTrxId": "123",
                "operationId": 1001
            }
        };
        await consumer.process(undefined, [data]);

        const unloaded = await TransferModel.transferLogDb.findAll();
        expect(unloaded).to.deep.equal([]);
        const remoteUnloaded = await TransferModel.remoteTransferLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([]);
    });

    it("consumes pool transfer", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "committedAt": new Date("2019-11-01T10:15:37.761Z"),
            "id": "JnacHgAAAt4AAALcJnacHiZF2us=",
            "ts": new Date("2019-11-01T10:15:37.758Z"),
            "data": [
                {
                    "trxType": "take",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": -**********0000,
                    "value": **************,
                    "property": "progressive",
                    "prevValue": **************,
                    "account": "large"
                },
                {
                    "trxType": "give",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": **********0000,
                    "value": **************,
                    "property": "progressive",
                    "prevValue": **************,
                    "account": "medium"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": 1,
                    "value": 2,
                    "property": "seqId",
                    "prevValue": 1,
                    "account": "$$metaInf"
                }
            ],
            "operation": {
                "operationName": "pool_transfer",
                "params": {
                    "gameId": "test",
                    "precision": **********
                },
                "operationId": 1
            }
        };

        await consumer.process(undefined, [data]);

        const unloaded = await TransferModel.transferLogDb.findAll();
        expect(unloaded).to.deep.equal([
            {
                "currency": "EUR",
                "externalId": null,
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameId": "test",
                "id": 1,
                "jackpotId": "JP-TEST",
                "fromPool": "large",
                "fromPoolProgressive": "70000",
                "fromPoolSeed": "0",
                "progressive": "10000",
                "seed": "0",
                "toPool": "medium",
                "toPoolProgressive": "40000",
                "toPoolSeed": "0",
                "trxId": "JnacHgAAAt4AAALcJnacHiZF2us=",
                "trxDate": new Date("2019-11-01T10:15:37.761Z"),
            }
        ]);
        const remoteUnloaded = await TransferModel.remoteTransferLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([]);
    });

    it("consumes pool transfer with loan", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "committedAt": new Date("2019-11-01T11:24:05.869Z"),
            "id": "JrVLZwAAAtQAAALcJrVLZ673+0A=",
            "ts": new Date("2019-11-01T11:24:05.863Z"),
            "data": [
                {
                    "trxType": "take_loan",
                    "walletKey": "jackpot:JP-CHALLENGE:EUR",
                    "amount": -**************,
                    "value": -**************,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "challenge_pool"
                },
                {
                    "trxType": "take",
                    "walletKey": "jackpot:JP-CHALLENGE:EUR",
                    "amount": -**************,
                    "value": 0,
                    "property": "progressive",
                    "prevValue": **************,
                    "account": "challenge_pool"
                },
                {
                    "trxType": "give",
                    "walletKey": "jackpot:JP-CHALLENGE:EUR",
                    "amount": **********00000,
                    "value": **********00000,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "prize_pool"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-CHALLENGE:EUR",
                    "amount": 1,
                    "value": 2,
                    "property": "seqId",
                    "prevValue": 1,
                    "account": "$$metaInf"
                }
            ],
            "operation": {
                "operationName": "pool_transfer",
                "params": {
                    "precision": **********,
                    "gameId": "test-challenge"
                },
                "operationId": 1
            }
        };

        await consumer.process(undefined, [data]);

        const unloaded = await TransferModel.transferLogDb.findAll();
        expect(unloaded).to.deep.equal([
            {
                "id": 1,
                "currency": "EUR",
                "externalId": null,
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameId": "test-challenge",
                "jackpotId": "JP-CHALLENGE",
                "fromPool": "challenge_pool",
                "fromPoolProgressive": "50000",
                "fromPoolSeed": "0",
                "progressive": "50000",
                "seed": "50000",
                "toPool": "prize_pool",
                "toPoolProgressive": "0",
                "toPoolSeed": "0",
                "trxId": "JrVLZwAAAtQAAALcJrVLZ673+0A=",
                "trxDate": new Date("2019-11-01T11:24:05.869Z"),
            }
        ]);
        const remoteUnloaded = await TransferModel.remoteTransferLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([]);
    });

    it("consumes pool transfer with loan from different pool", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "committedAt": new Date("2019-11-01T11:24:05.869Z"),
            "id": "JrVLZwAAAtQAAALcJrVLZ673+0A=",
            "ts": new Date("2019-11-01T11:24:05.863Z"),
            "data": [
                {
                    "trxType": "take_loan",
                    "walletKey": "jackpot:JP-CHALLENGE:EUR",
                    "amount": -**************,
                    "value": -**************,
                    "property": "seed",
                    "prevValue": 0,
                    "account": "loan_pool"
                },
                {
                    "trxType": "take",
                    "walletKey": "jackpot:JP-CHALLENGE:EUR",
                    "amount": -**************,
                    "value": 0,
                    "property": "progressive",
                    "prevValue": **************,
                    "account": "challenge_pool"
                },
                {
                    "trxType": "give",
                    "walletKey": "jackpot:JP-CHALLENGE:EUR",
                    "amount": **********00000,
                    "value": **********00000,
                    "property": "progressive",
                    "prevValue": 0,
                    "account": "prize_pool"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-CHALLENGE:EUR",
                    "amount": 1,
                    "value": 2,
                    "property": "seqId",
                    "prevValue": 1,
                    "account": "$$metaInf"
                }
            ],
            "operation": {
                "operationName": "pool_transfer",
                "params": {
                    "precision": **********,
                    "gameId": "test-challenge"
                },
                "operationId": 1
            }
        };

        await consumer.process(undefined, [data]);

        const unloaded = await TransferModel.transferLogDb.findAll();
        expect(unloaded).to.deep.equal([
            {
                "id": 1,
                "currency": "EUR",
                "externalId": null,
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameId": "test-challenge",
                "jackpotId": "JP-CHALLENGE",
                "fromPool": "challenge_pool",
                "fromPoolProgressive": "50000",
                "fromPoolSeed": "0",
                "progressive": "50000",
                "seed": "0",
                "toPool": "prize_pool",
                "toPoolProgressive": "0",
                "toPoolSeed": "0",
                "trxId": "JrVLZwAAAtQAAALcJrVLZ673+0A=",
                "trxDate": new Date("2019-11-01T11:24:05.869Z"),
            },
            {
                "id": 2,
                "currency": "EUR",
                "externalId": null,
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "gameId": "test-challenge",
                "jackpotId": "JP-CHALLENGE",
                "fromPool": "loan_pool",
                "fromPoolProgressive": "0",
                "fromPoolSeed": "0",
                "progressive": "0",
                "seed": "50000",
                "toPool": "prize_pool",
                "toPoolProgressive": "0",
                "toPoolSeed": "0",
                "trxId": "JrVLZwAAAtQAAALcJrVLZ673+0A=",
                "trxDate": new Date("2019-11-01T11:24:05.869Z"),
            }
        ]);
        const remoteUnloaded = await TransferModel.remoteTransferLogDb.findAll();
        expect(remoteUnloaded).to.deep.equal([]);
    });

    it("consumes internal transfers", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "committedAt": new Date("2020-05-28T08:40:55.066Z"),
            "id": "WnECBAAAAuQAAALkWnECBHlKHZY=",
            "ts": new Date("2020-05-28T08:40:55.044Z"),
            "data": [
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": "-***********",
                    "value": "0",
                    "property": "seed",
                    "prevValue": "***********",
                    "account": "medium"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": "***********",
                    "value": "***********",
                    "property": "progressive",
                    "prevValue": "***********",
                    "account": "medium"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": "-***********",
                    "value": "**********0",
                    "property": "seed",
                    "prevValue": "***********",
                    "account": "large"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": "***********",
                    "value": "***********",
                    "property": "progressive",
                    "prevValue": "***********",
                    "account": "large"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": "1",
                    "value": "2",
                    "property": "seqId",
                    "prevValue": "1",
                    "account": "$$metaInf"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": "0",
                    "value": "0",
                    "property": "disabled",
                    "prevValue": "0",
                    "account": "$$metaInf"
                }
            ],
            "operation": {
                "operationName": "internalTransfer",
                "params": {
                    "gameCode": "test",
                    "brandId": 1,
                    "playerCurrency": "CNY",
                    "playerCode": "PL-CODE",
                    "transfers": [
                        {
                            "progressive": 0,
                            "pool": "medium",
                            "seed": 20,
                            "totalSeed": 20,
                            "transferPool": "medium",
                            "totalProgressive": 40
                        },
                        {
                            "progressive": 0,
                            "pool": "large",
                            "seed": 20,
                            "totalSeed": 30,
                            "transferPool": "large",
                            "totalProgressive": 70
                        }
                    ],
                    "gameId": "test",
                    "precision": **********,
                    "region": "eu",
                    "roundId": "1"
                },
                "externalTrxId": "123",
                "operationId": **********
            }
        };

        await consumer.process(undefined, [data as any]);

        const unloaded = await TransferModel.transferLogDb.findAll();
        expect(unloaded.length).to.equal(2);
        expect(unloaded[0]).deep.equal({
            "currency": "EUR",
            "externalId": "123",
            "fromPool": "medium",
            "fromPoolProgressive": "40",
            "fromPoolSeed": "20",
            "gameId": "test",
            "id": 1,
            "jackpotId": "JP-TEST",
            "progressive": "0",
            "remoteTrxId": null,
            "remoteTrxRegion": null,
            "seed": "20",
            "toPool": "medium",
            "toPoolProgressive": "0",
            "toPoolSeed": "0",
            "trxDate": new Date("2020-05-28T08:40:55.066Z"),
            "trxId": "WnECBAAAAuQAAALkWnECBHlKHZY="
        });
    });

    it("consumes transfers between pools", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "committedAt": new Date("2020-05-28T08:40:55.066Z"),
            "id": "WnECBAAAAuQAAALkWnECBHlKHZY=",
            "ts": new Date("2020-05-28T08:40:55.044Z"),
            "data": [
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-INSTANT-TEST:EUR",
                    "amount": "-***********",
                    "value": "0",
                    "property": "seed",
                    "prevValue": "***********",
                    "account": "B"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-INSTANT-TEST:EUR",
                    "amount": "-***********",
                    "value": "0",
                    "property": "progressive",
                    "prevValue": "***********",
                    "account": "B"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-INSTANT-TEST:EUR",
                    "amount": "***********",
                    "value": "***********",
                    "property": "progressive",
                    "prevValue": "***********",
                    "account": "D"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-INSTANT-TEST:EUR",
                    "amount": "-***********",
                    "value": "**********0",
                    "property": "seed",
                    "prevValue": "***********",
                    "account": "A"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-INSTANT-TEST:EUR",
                    "amount": "-***********",
                    "value": "0",
                    "property": "progressive",
                    "prevValue": "***********",
                    "account": "A"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-INSTANT-TEST:EUR",
                    "amount": "***********",
                    "value": "**********00",
                    "property": "progressive",
                    "prevValue": "***********",
                    "account": "D"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-INSTANT-TEST:EUR",
                    "amount": "1",
                    "value": "2",
                    "property": "seqId",
                    "prevValue": "1",
                    "account": "$$metaInf"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-INSTANT-TEST:EUR",
                    "amount": "0",
                    "value": "0",
                    "property": "disabled",
                    "prevValue": "0",
                    "account": "$$metaInf"
                }
            ],
            "operation": {
                "operationName": "internalTransfer",
                "params": {
                    "gameCode": "test",
                    "brandId": 1,
                    "playerCurrency": "CNY",
                    "playerCode": "PL-CODE",
                    "transfers": [
                        {
                            "pool": "B",
                            "seed": 20,
                            "progressive": 30,
                            "totalSeed": 20,
                            "totalProgressive": 30,
                            "transferPool": "D",
                            "transferPoolProgressive": 0
                        },
                        {
                            "pool": "A",
                            "seed": 20,
                            "progressive": 40,
                            "totalSeed": 30,
                            "totalProgressive": 40,
                            "transferPool": "D",
                            "transferPoolProgressive": 0
                        }
                    ],
                    "gameId": "test",
                    "precision": **********,
                    "region": "eu",
                    "roundId": "1"
                },
                "externalTrxId": "123",
                "operationId": **********
            }
        };

        await consumer.process(undefined, [data as any]);

        const unloaded = await TransferModel.transferLogDb.findAll();
        expect(unloaded.length).to.equal(2);
        expect(unloaded).deep.equal([
            {
                "currency": "EUR",
                "externalId": "123",
                "fromPool": "B",
                "fromPoolProgressive": "30",
                "fromPoolSeed": "20",
                "gameId": "test",
                "id": 1,
                "jackpotId": "JP-INSTANT-TEST",
                "progressive": "30",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "seed": "20",
                "toPool": "D",
                "toPoolProgressive": "0",
                "toPoolSeed": "0",
                "trxDate": new Date("2020-05-28T08:40:55.066Z"),
                "trxId": "WnECBAAAAuQAAALkWnECBHlKHZY="
            },
            {
                "currency": "EUR",
                "externalId": "123",
                "fromPool": "A",
                "fromPoolProgressive": "40",
                "fromPoolSeed": "30",
                "gameId": "test",
                "id": 2,
                "jackpotId": "JP-INSTANT-TEST",
                "progressive": "40",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "seed": "20",
                "toPool": "D",
                "toPoolProgressive": "0",
                "toPoolSeed": "0",
                "trxDate": new Date("2020-05-28T08:40:55.066Z"),
                "trxId": "WnECBAAAAuQAAALkWnECBHlKHZY="
            }
        ]);
    });

    it("ignore empty transfer transaction", async () => {
        const data = {
            "isExternal": false,
            "version": "1",
            "committedAt": new Date("2020-05-28T08:40:55.066Z"),
            "id": "WnECBAAAAuQAAALkWnECBHlKHZY=",
            "ts": new Date("2020-05-28T08:40:55.044Z"),
            "data": [
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": "1",
                    "value": "2",
                    "property": "seqId",
                    "prevValue": "1",
                    "account": "$$metaInf"
                },
                {
                    "trxType": "transfer",
                    "walletKey": "jackpot:JP-TEST:EUR",
                    "amount": "0",
                    "value": "0",
                    "property": "disabled",
                    "prevValue": "0",
                    "account": "$$metaInf"
                }
            ],
            "operation": {
                "operationName": "internalTransfer",
                "params": {
                    "gameCode": "test",
                    "brandId": 1,
                    "playerCurrency": "CNY",
                    "playerCode": "PL-CODE",
                    "transfers": {},
                    "gameId": "test",
                    "precision": **********,
                    "region": "eu",
                    "roundId": "1"
                },
                "externalTrxId": "123",
                "operationId": **********
            }
        };

        await consumer.process(undefined, [data as any]);

        const unloaded = await TransferModel.transferLogDb.findAll();
        expect(unloaded.length).to.equal(0);
    });
});
