import { expect, request } from "chai";
import * as redis from "../skywind/storage/redis";
import db from "../skywind/services/db.service";
import {
    CheckWinRequest,
    Contribution,
    JackpotGame,
    JackpotGameResult,
    JackpotGameWinResult,
    JackpotPools,
    MiniGameRequest,
    TickerAmount,
    WinJackpotRequest
} from "@skywind-group/sw-jpn-core";
import { AbstractJackpotGame } from "@skywind-group/sw-jpn-games";
import config from "../skywind/config";

import { ContributionConsumer } from "../skywind/history/contribution.consumer";
import * as ContributionModel from "../skywind/history/contribution.log";
import { WinConsumer } from "../skywind/history/win.consumer";
import * as WinModel from "../skywind/history/win.log";
import * as TransferModel from "../skywind/history/transfer.log";
import { JackpotInternalInstance } from "../skywind/api/model";
import { DEFAULT_PRECISION, JackpotWallet } from "../skywind/modules/jackpotWallet";
import { JackpotTypeService } from "../skywind/services/jackpot.type";
import { JackpotInstanceService } from "../skywind/services/jackpot.instance";
import JackpotRegionService from "../skywind/services/regionService";
import { createPlayerContributions } from "../skywind/services/jackpotGameFlow";
import { SinonSpy } from "sinon";
import { JackpotModule } from "../skywind/modules/jackpotModule";
import { JackpotAuditService } from "../skywind/services/jackpotAudits";
import { TransferConsumer } from "../skywind/history/transfer.consumer";
import { ContributionPayout } from "../skywind/modules/walletParams";

const fs = require("fs");
const path = require("path");

const transactionList = config.trxStoragePrefix + ":wallet:transaction-list";
const contributionConsumer = new ContributionConsumer();
const winConsumer = new WinConsumer();
const transferConsumer = new TransferConsumer();

config.logLevel = "debug";

export async function flush() {
    const client = await redis.get();
    try {
        await client.flushall();
    } finally {
        redis.release(client);
    }
}

export async function flushDb() {
    await db.drop({ cascade: true });
    await JackpotAuditService.model.sync();
    await JackpotTypeService.model.sync();
    await JackpotRegionService.model.sync();
    await JackpotInstanceService.model.sync();
    await createJackpotOperationStatusType();
}

async function createJackpotOperationStatusType(): Promise<void> {
    await createProcedureFromFile("resources/sql/enum_jackpot_operation_status.sql");
}

async function createProcedureFromFile(filePath: string) {
    const procedureSqlFile = path.join(process.cwd(), filePath);
    const script = fs.readFileSync(procedureSqlFile, "utf8");
    await db.query(script);
}

export const JP_POOL_SMALL_AMOUNT = 10;
export const JP_POOL_SMALL_PROGRESSIVE_PCT = 0.3;

export const JP_WITH_DYNAMIC_CONTRIBUTION: JackpotInternalInstance = {
    "internalId": 1,
    "id": "JP-TEST",
    "type": "test",
    "baseType": "base",
    "jpGameId": "test",
    "precision": DEFAULT_PRECISION,
    "definition": {
        "features": ["deposit", "transfer"],
        "currency": "EUR",
        "list": [
            {
                "id": "small",
                "title": "friendly name",
                "seed": {
                    "amount": JP_POOL_SMALL_AMOUNT,
                },
                "contribution": [
                    {
                        "seed": 0.1,
                        "progressive": JP_POOL_SMALL_PROGRESSIVE_PCT,
                        "condition": {
                            "seed": { "$lt": 10 },
                        },
                    }, {
                        "seed": 0,
                        "progressive": 0.4,
                    },
                ],
            }, {
                "id": "medium",
                "seed": {
                    "amount": 100,
                },
                "contribution": [
                    {
                        "seed": 0.2,
                        "progressive": 0.4,
                        "condition": {
                            "seed": { "$lt": 100 },
                        },
                    }, {
                        "seed": 0,
                        "progressive": 0.6,
                    },
                ],
            }, {
                "id": "large",
                "seed": {
                    "amount": 1000,
                },
                "contribution": [
                    {
                        "seed": 0.3,
                        "progressive": 0.7,
                        "condition": {
                            "seed": { "$lt": 1000 },
                        },
                    }, {
                        "seed": 0,
                        "progressive": 1.0,
                    },
                ],
            },
        ],
    },
};

export const JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT: JackpotInternalInstance = {
    "internalId": 1,
    "id": "JP-TEST",
    "type": "test",
    "baseType": "base",
    "jpGameId": "test",
    "precision": DEFAULT_PRECISION,
    "definition": {
        "features": ["transfer"],
        "currency": "EUR",
        "list": [
            {
                "id": "small",
                "seed": {
                    "amount": JP_POOL_SMALL_AMOUNT,
                },
                "contribution": [
                    {
                        "seed": 0.1,
                        "progressive": JP_POOL_SMALL_PROGRESSIVE_PCT,
                        "condition": {
                            "seed": { "$lt": 10 },
                        },
                    }, {
                        "seed": 0,
                        "progressive": 0.4,
                    },
                ],
            }, {
                "id": "medium",
                "seed": {
                    "amount": 100,
                },
                "contribution": [
                    {
                        "seed": 0.2,
                        "progressive": 0.4,
                        "condition": {
                            "seed": { "$lt": 100 },
                        },
                    }, {
                        "seed": 0,
                        "progressive": 0.6,
                    },
                ],
            }, {
                "id": "large",
                "seed": {
                    "amount": 1000,
                },
                "contribution": [
                    {
                        "seed": 0.3,
                        "progressive": 0.7,
                        "condition": {
                            "seed": { "$lt": 1000 },
                        },
                    }, {
                        "seed": 0,
                        "progressive": 1.0,
                    },
                ],
            },
        ],
    },
};

export const JP_WITH_INFO_AND_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT: JackpotInternalInstance = {
    ...JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT,
    "info": {
        "externalId": "JP-TEST_hash1",
        "externalStartDate": "2023-10-17T08:22:45.400Z",
    }
};

export const JP_WITH_LOAN_FEATURE: JackpotInternalInstance = {
    "internalId": 2,
    "id": "JP-CHALLENGE",
    "type": "test-challenge",
    "baseType": "base",
    "jpGameId": "test-challenge",
    "precision": DEFAULT_PRECISION,
    "definition": {
        "features": ["deposit", "transfer", "loan"],
        "loanPool": {
            "poolId": "challenge_pool",
            "property": "seed"
        },
        "currency": "EUR",
        "list": [
            {
                "id": "challenge_pool",
                "seed": {
                    "amount": 0
                },
                "contribution": [
                    {
                        "progressive": 0.5,
                        "condition": {
                            "seed": {
                                "$gte": 0
                            }
                        }
                    },
                    {
                        "progressive": 0,
                        "seed": 0.5
                    }
                ]
            },
            {
                "id": "prize_pool",
                "seed": {
                    "amount": 0
                },
                "contribution": [
                    {
                        "progressive": 0
                    }
                ]
            }
        ]
    }
};

export const JP_WITH_LIMITED_CONTRIBUTION: JackpotInternalInstance = {
    "internalId": 2,
    "id": "JP-TEST-2",
    "type": "test-2",
    "baseType": "base",
    "jpGameId": "test-2",
    "precision": DEFAULT_PRECISION,
    "definition": {
        "currency": "EUR",
        "list": [
            {
                "lowerSeedLimit": -20,
                "id": "small",
                "seed": {
                    "amount": JP_POOL_SMALL_AMOUNT,
                },
                "contribution": [
                    {
                        "seed": 0.1,
                        "progressive": 0.2,
                        "condition": {
                            "amount": { "$gt": 0, "$lte": 10 },
                        },
                    }
                ],
            }, {
                "id": "medium",
                "seed": {
                    "amount": 100,
                },
                "contribution": [
                    {
                        "seed": 0.2,
                        "progressive": 0.4,
                        "condition": {
                            "amount": { "$gt": 10, "$lte": 100 },
                        },
                    }
                ],
            }, {
                "maxContributionAmount": 1000,
                "id": "large",
                "seed": {
                    "amount": 1000,
                },
                "contribution": [
                    {
                        "seed": 0.3,
                        "progressive": 0.7,
                        "condition": {
                            "amount": { "$gt": 100 },
                        },
                    }
                ],
            },
        ],
    },
};

export const JP_WITH_WIN_TRANSFERS: JackpotInternalInstance = {
    "internalId": 1,
    "id": "JP-TEST-WIN-TRANSFER",
    "type": "test",
    "baseType": "base",
    "jpGameId": "test",
    "precision": DEFAULT_PRECISION,
    "definition": {
        "currency": "EUR",
        "list": [
            {
                "id": "main",
                "seed": {
                    "amount": 100,
                },
                "contribution": [
                    {
                        "seed": 0.2,
                        "progressive": 0.4,
                        "condition": {
                            "seed": { "$lt": 100 },
                        },
                    }, {
                        "seed": 0,
                        "progressive": 0.6,
                    },
                ],
            }, {
                "id": "prize",
                "contribution": [],
            },
        ],
    },
};

export const JP_WITH_VIRTUAL_SEED: JackpotInternalInstance = {
    "internalId": 1,
    "id": "JP-TEST-VIRTUAL-SEED",
    "type": "test",
    "baseType": "base",
    "jpGameId": "test",
    "precision": DEFAULT_PRECISION,
    "definition": {
        "currency": "EUR",
        "list": [
            {
                "id": "main",
                "seed": {
                    "amount": 0,
                },
                "virtualSeed": {
                    "amount": 500,
                    "USD": 600,
                    "CNY": 7000
                },
                "contribution": [
                    {
                        "progressive": 0.5,
                    },
                ],
            }
        ],
    },
};

export const JP_INSTANT: JackpotInternalInstance = {
    "internalId": 1,
    "id": "JP-INSTANT-TEST",
    "type": "sw-instant-jp",
    "baseType": "sw-instant-jp",
    "jpGameId": "test",
    "precision": DEFAULT_PRECISION,
    "definition": {
        "features": ["deposit", "transfer"],
        "currency": "EUR",
        "list": [
            {
                "id": "D",
                "title": "D",
                "seed": {
                    "amount": JP_POOL_SMALL_AMOUNT,
                },
                "contribution": [
                    {
                        "seed": 0.1,
                        "progressive": JP_POOL_SMALL_PROGRESSIVE_PCT,
                        "condition": {
                            "seed": { "$lt": 10 },
                        },
                    }, {
                        "seed": 0,
                        "progressive": 0.4,
                    },
                ],
            }, {
                "id": "C",
                "seed": {
                    "amount": 100,
                },
                "contribution": [
                    {
                        "seed": 0.2,
                        "progressive": 0.4,
                        "condition": {
                            "seed": { "$lt": 100 },
                        },
                    }, {
                        "seed": 0,
                        "progressive": 0.6,
                    },
                ],
            }, {
                "id": "B",
                "seed": {
                    "amount": 1000,
                },
                "contribution": [
                    {
                        "seed": 0.3,
                        "progressive": 0.7,
                        "condition": {
                            "seed": { "$lt": 1000 },
                        },
                    }, {
                        "seed": 0,
                        "progressive": 1.0,
                    },
                ],
            }, {
                "id": "A",
                "seed": {
                    "amount": 10000,
                },
                "contribution": [
                    {
                        "seed": 0.4,
                        "progressive": 0.8,
                        "condition": {
                            "seed": { "$lt": 10000 },
                        },
                    }, {
                        "seed": 0,
                        "progressive": 1.0,
                    },
                ],
            },
        ],
    },
};

export class TestJPGame extends AbstractJackpotGame {

    constructor(jackpot, currencyService?, rng?) {
        super(jackpot, currencyService, rng);
    }

    public async checkWin(request: CheckWinRequest): Promise<JackpotGameResult> {
        return undefined;
    }

    public async winMiniGame(request: MiniGameRequest): Promise<JackpotGameWinResult> {
        return undefined;
    }

    public async winJackpot(request: WinJackpotRequest): Promise<JackpotGameWinResult> {
        return undefined;
    }
}

export class TestJPGameWithCustomTicker extends TestJPGame {
    getLobbyTicker(pools: JackpotPools, toEurMultiplier: number): TickerAmount {
        if (toEurMultiplier === 1) {
            return {
                small: { amount: 1, seed: 100, progressive: 100 },
                medium: { amount: 1, seed: 100, progressive: 100 }
            };
        }
        return {
            small: { amount: 2, seed: 100, progressive: 100 },
            medium: { amount: 3, seed: 100, progressive: 100 }
        };
    }
}

export async function createTestJPGame(wallet: JackpotWallet | JackpotModule) {
    const jackpot = {
        definition: wallet.instance.definition,
        getTicker: wallet.getTicker.bind(wallet)
    };
    const result: JackpotGame = new TestJPGame(jackpot);
    result.getAdditionalPoolManipulations = (pool) => undefined;
    return result;
}

export function delay(ms): Promise<void> {
    return new Promise<void>((resolve, reject) => {
        setTimeout(resolve, ms);
    });
}

export async function unloadLastTransaction() {
    await ContributionModel.contributionLogDb.sync();
    await ContributionModel.remoteContributionLogDb.sync();
    await WinModel.winLogDb.sync();
    await WinModel.remoteWinLogDb.sync();
    await TransferModel.transferLogDb.sync();
    await TransferModel.remoteTransferLogDb.sync();

    const client = await redis.get();
    try {
        const value = await client.lpop(transactionList);
        if (value) {
            const trx = JSON.parse(value);
            await contributionConsumer.process(undefined, [trx]);
            await winConsumer.process(undefined, [trx]);
            await transferConsumer.process(undefined, [trx]);
        }
    } finally {
        await redis.release(client);
    }
}

export async function getLastTransaction() {
    const client = await redis.get();
    try {
        const value = await client.lpop(transactionList);
        if (value) {
            const trx = JSON.parse(value);
            return trx;
        }
    } finally {
        await redis.release(client);
    }
}

export async function hGetAll(redisPool: redis.RedisClientPool, key: string) {
    const client = await redisPool.get();
    try {
        return await client.hgetall(key);
    } finally {
        redisPool.release(client);
    }
}

export class APISupport {

    private static ACCESS_TOKEN: string = "X-Access-Token";

    constructor(private app, private token: string) {}

    public post<REQ, RES>(path: string, info: any): Promise<RES> {
        return new Promise<RES>((resolve, reject) => {
            request(this.app)
                .post(path)
                .set(APISupport.ACCESS_TOKEN, this.token)
                .send(info)
                .then(res => {
                    expect(res.status).to.equal(201);
                    expect(res).to.be.json;
                    resolve(res.body);
                })
                .catch(reject);
        });
    }

    public patch<REQ, RES>(path: string, info: any): Promise<RES> {
        return new Promise<RES>((resolve, reject) => {
            request(this.app)
                .patch(path)
                .set(APISupport.ACCESS_TOKEN, this.token)
                .send(info)
                .then(res => {
                    expect(res.status).to.equal(200);
                    expect(res).to.be.json;
                    resolve(res.body);
                })
                .catch(reject);
        });
    }

    public put<REQ, RES>(path: string, info: any, params = {}): Promise<RES> {
        return new Promise<RES>((resolve, reject) => {
            request(this.app)
                .put(path)
                .set(APISupport.ACCESS_TOKEN, this.token)
                .query(params)
                .send(info)
                .then(res => {
                    expect(res.status).to.equal(200);
                    expect(res).to.be.json;
                    resolve(res.body);
                })
                .catch(reject);
        });
    }

    public get<RES>(path: string, query = {}): Promise<RES> {
        return new Promise<RES>((resolve, reject) => {
            request(this.app)
                .get(path)
                .set(APISupport.ACCESS_TOKEN, this.token)
                .query(query)
                .then(res => {
                    expect(res.status).to.equal(200);
                    expect(res).to.be.json;
                    resolve(res.body);
                })
                .catch(reject);
        });
    }

    public del(path: string): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            request(this.app)
                .del(path)
                .set(APISupport.ACCESS_TOKEN, this.token)
                .then(res => {
                    expect(res.status).to.equal(204);
                    resolve();
                })
                .catch(reject);
        });
    }
}

export function getSpiedPlayerContributions(spy: SinonSpy, exchangeRate?: number): Contribution[] {
    return createPlayerContributions(spy.getCall(0).returnValue, exchangeRate);
}

export function convertContributionsToContributionsPayoutWithTotals(
    contributions: Contribution[],
    totals: { [pool: string]: { totalSeed: number, totalProgressive: number } }
): ContributionPayout[] {
    if (Array.isArray(contributions)) {
        return contributions.map(item => {
            const totalsForPool = totals[item.pool];
            return { ...item, totalSeed: totalsForPool.totalSeed, totalProgressive: totalsForPool.totalProgressive };
        });
    }
}
