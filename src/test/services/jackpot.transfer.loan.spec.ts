import { expect, use } from "chai";
import { JackpotWallet } from "../../skywind/modules/jackpotWallet";
import {
    createTestJPGame,
    flush, JP_WITH_DYNAMIC_CONTRIBUTION, JP_WITH_LOAN_FEATURE, TestJPGame,
} from "../helpers";
import { stub, SinonStub } from "sinon";
import JackpotInstanceService from "../../skywind/services/jackpot.instance";
import * as gameService from "../../skywind/services/game.service";
import { JackpotService, TransferPoolRequest } from "../../skywind/services/jackpot.service";
import { AuthRequest } from "@skywind-group/sw-jpn-core";
import wallet from "../../skywind/services/wallet.service";
import contextService from "../../skywind/services/jackpot.context";
import { init as initCurrencyRates } from "../../skywind/services/currency.service";
import {
    DuplicateTransactionError, InsufficientJackpotBalance, JpFeatureForbidden,
    ValidationError
} from "../../skywind/errors";
import { BaseWalletParams } from "../../skywind/modules/walletParams";

use(require("chai-as-promised"));

describe("Jackpot Transfer with loan", () => {

    const jpInstance = JP_WITH_LOAN_FEATURE;
    const playerInfo: AuthRequest = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        jackpotIds: [jpInstance.id],
        currency: "EUR",
        gameCode: "test"
    };
    const walletParams: BaseWalletParams = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        playerCurrency: "EUR",
        gameCode: "test",
        gameId: "test",
        roundId: "1"
    };
    let jpGame;
    const jackpotService: JackpotService = new JackpotService(JackpotInstanceService, wallet, contextService);

    let lookup: SinonStub;
    let load: SinonStub;
    let jpCheckWin: SinonStub;
    let jpMiniGame: SinonStub;
    let jpWinJackpot: SinonStub;

    before(async() => {
        lookup = stub(JackpotInstanceService, "findInternal");
        load = stub(gameService, "load");
        await initCurrencyRates();
    });

    beforeEach(async() => {
        await flush();

        const jpModule = new JackpotWallet(walletParams, jpInstance, 1, wallet);

        jpGame = await createTestJPGame(jpModule);

        jpCheckWin = stub(jpGame, "checkWin");
        jpMiniGame = stub(jpGame, "winMiniGame");
        jpWinJackpot = stub(jpGame, "winJackpot");

        lookup.resetBehavior();
        lookup.returns(jpInstance);
        load.resetBehavior();
        load.returns(jpGame);
        jpCheckWin.resetHistory();
        jpMiniGame.resetHistory();
        jpWinJackpot.resetHistory();
    });

    after(() => {
        lookup.restore();
        load.restore();
        jpCheckWin.restore();
        jpMiniGame.restore();
        jpWinJackpot.restore();
    });

    async function poolTransfer(fromPoolId: string, toPoolId: string, amount: number, init: boolean = true,
                                transactionId?: string): Promise<any> {

        const jackpotModule = new JackpotWallet(walletParams, jpInstance, 1, wallet);

        if (init) {
            const trxId = await wallet.generateTransactionId();

            await jackpotService.contribute(playerInfo, {
                externalId: "123",
                transactionId: trxId,
                amount: 10000000,
                exchangeRate: 1,
                roundId: "1"
            });

            const jpInfo1 = await jackpotModule.getTicker(true);
            expect(jpInfo1).deep.equal({
                "currency": "EUR",
                "id": "JP-CHALLENGE",
                "pools": {
                    "challenge_pool": {
                        "progressive": 50000,
                        "seed": 0,
                    },
                    "prize_pool": {
                        "progressive": 0,
                        "seed": 0
                    }
                },
                "seqId": 1
            });
        }

        transactionId = transactionId || (await jackpotService.getTransferTransactionId()).transactionId;

        const request: TransferPoolRequest = {
            fromPoolId: fromPoolId,
            toPoolId: toPoolId,
            transactionId: transactionId,
            amount: amount
        };

        try {
            await jackpotService.transferProgressive(playerInfo, request);
        } catch (error) {
            if (error instanceof DuplicateTransactionError) {
                // Duplicating transaction, It's Ok -  continue work.
            } else {
                return Promise.reject(error);
            }
        }

        return jackpotModule.getTicker(true);

    }

    it("Transfer fixed amount of progressive from pool to pool without loan", async() => {
        const result = await poolTransfer("challenge_pool", "prize_pool", 667);
        expect(result).deep.equal({
            "currency": "EUR",
            "id": "JP-CHALLENGE",
            "pools": {
                "challenge_pool": {
                    "progressive": 49333,
                    "seed": 0,
                },
                "prize_pool": {
                    "progressive": 667,
                    "seed": 0,
                }
            },
            "seqId": 2
        });
        expect(49333 + 667).deep.equal(50000);
    });

    it("Transfer all amount from pool to pool with loan", async() => {
        const result = await poolTransfer("challenge_pool", "prize_pool", undefined);
        expect(result).deep.equal({
            "currency": "EUR",
            "id": "JP-CHALLENGE",
            "pools": {
                "challenge_pool": {
                    "progressive": 0,
                    "seed": 0,
                },
                "prize_pool": {
                    "progressive": 50000,
                    "seed": 0
                }
            },
            "seqId": 2
        });
    });

    it("Transfer large amount, insufficient part take from loan pool", async() => {
        const result = await poolTransfer("challenge_pool", "prize_pool", 100000);
        expect(result).deep.equal({
            "currency": "EUR",
            "id": "JP-CHALLENGE",
            "pools": {
                "challenge_pool": {
                    "progressive": 0,
                    "seed": -50000
                },
                "prize_pool": {
                    "progressive": 100000,
                    "seed": 0
                }
            },
            "seqId": 2
        });
    });

    it("Transfer large amount, when loan pool not available, should be error", async() => {
        // All to prize_pool
        const result1 = await poolTransfer("challenge_pool", "prize_pool", undefined);
        expect(result1).deep.equal({
            "currency": "EUR",
            "id": "JP-CHALLENGE",
            "pools": {
                "challenge_pool": {
                    "progressive": 0,
                    "seed": 0,
                },
                "prize_pool": {
                    "progressive": 50000,
                    "seed": 0
                }
            },
            "seqId": 2
        });

        await expect(poolTransfer("prize_pool", "challenge_pool", 100000, false)).to
            .be.rejectedWith(InsufficientJackpotBalance);

    });

    it("Transfer amount with loan, then return back part of loan", async() => {

        const result1 = await poolTransfer("challenge_pool", "prize_pool", 100000);
        expect(result1).deep.equal({
            "currency": "EUR",
            "id": "JP-CHALLENGE",
            "pools": {
                "challenge_pool": {
                    "progressive": 0,
                    "seed": -50000,
                },
                "prize_pool": {
                    "progressive": 100000,
                    "seed": 0
                }
            },
            "seqId": 2
        });

        const result2 = await poolTransfer("prize_pool", "challenge_pool",  10000, false);
        expect(result2).deep.equal({
            "currency": "EUR",
            "id": "JP-CHALLENGE",
            "pools": {
                "challenge_pool": {
                    "progressive": 0,
                    "seed": -50000 + 10000,
                },
                "prize_pool": {
                    "progressive": 100000 - 10000,
                    "seed": 0
                }
            },
            "seqId": 3
        });

    });

    it("Transfer amount with loan, then return back large than loan", async() => {

        const result1 = await poolTransfer("challenge_pool", "prize_pool", 100000);
        expect(result1).deep.equal({
            "currency": "EUR",
            "id": "JP-CHALLENGE",
            "pools": {
                "challenge_pool": {
                    "progressive": 0,
                    "seed": -50000,
                },
                "prize_pool": {
                    "progressive": 100000,
                    "seed": 0
                }
            },
            "seqId": 2
        });

        const transactionId2 = (await jackpotService.getTransferTransactionId()).transactionId;
        await jackpotService.poolDeposit(playerInfo, "prize_pool",
            { transactionId: transactionId2, progressive: 6000000 });

        const result2 = await poolTransfer("prize_pool", "challenge_pool",  6000000, false);
        expect(result2).deep.equal({
            "currency": "EUR",
            "id": "JP-CHALLENGE",
            "pools": {
                "challenge_pool": {
                    "progressive": 6000000 - 50000,
                    "seed": 0
                },
                "prize_pool": {
                    "progressive": 100000,
                    "seed": 0
                }
            },
            "seqId": 4
        });

    });

    it("Zero amount progressive not affect on pools and loan", async() => {
        const result = await poolTransfer("challenge_pool", "prize_pool", 0);
        expect(result).deep.equal({
            "currency": "EUR",
            "id": "JP-CHALLENGE",
            "pools": {
                "challenge_pool": {
                    "progressive": 50000,
                    "seed": 0
                },
                "prize_pool": {
                    "progressive": 0,
                    "seed": 0
                }
            },
            "seqId": 2
        });
    });

    it("Negative amount progressive", async() => {
        await expect(poolTransfer("challenge_pool", "prize_pool", -1)).to.be.rejectedWith(ValidationError);
    });

    it("Duplicate transaction - positive", async() => {
        const transactionId = (await jackpotService.getTransferTransactionId()).transactionId;
        const result1 = await poolTransfer("challenge_pool", "prize_pool", 10000, true, transactionId);

        const expectedResult = {
            "currency": "EUR",
            "id": "JP-CHALLENGE",
            "pools": {
                "challenge_pool": {
                    "progressive": 40000,
                    "seed": 0
                },
                "prize_pool": {
                    "progressive": 10000,
                    "seed": 0
                }
            },
            "seqId": 2
        };

        expect(result1).deep.equal(expectedResult);

        // Amount should't affect on duplicate transaction.
        const result2 = await poolTransfer("challenge_pool", "prize_pool", 11111, false, transactionId);
        expect(result2).deep.equal(expectedResult);
    });

});
