import { expect, use } from "chai";
import { stub, SinonStub } from "sinon";
import { flushDb } from "../helpers";
import JackpotTypeService from "../../skywind/services/jackpot.type";
import * as GameService from "../../skywind/services/game.service";
import { JackpotType } from "../../skywind/api/model";
import * as Errors from "../../skywind/errors";

use(require("chai-as-promised"));

describe("Jackpot Type Service", () => {

    let jpGameExists: SinonStub;
    let jpDefaultTypes: SinonStub;
    let jpTypeFromModule: SinonStub;
    const type: JackpotType = {
        name: "test",
        baseType: "base",
        jpGameId: "test",
        definition: {
            currency: "EUR",
            list: [{
                id: "pool",
                seed: {
                    amount: 100,
                },
                contribution: [],
            }],
        },
        configurable: true,
        overridable: false,
        canBeDisabled: false,
        supportsWinCap: false
    };

    before(() => {
        jpGameExists = stub(GameService, "exists");
        jpDefaultTypes = stub(GameService, "lookupJackpotTypes");
        jpTypeFromModule = stub(GameService, "lookupJackpotType");
    });

    after(() => {
        jpGameExists.restore();
        jpDefaultTypes.restore();
        jpTypeFromModule.restore();
    });

    beforeEach(async() => {
        await flushDb();
        jpGameExists.resetHistory();
        jpDefaultTypes.resetHistory();
        jpTypeFromModule.resetHistory();
    });

    it("creates all defined jackpot types", async() => {
        const defaultType = {
            name: "test",
            baseType: "base",
            jpGameId: "test",
            definition: {
                currency: "EUR",
                list: [{
                    id: "pool",
                    seed: {
                        amount: 10
                    }
                }]
            },
            configurable: true,
            overridable: false,
            canBeDisabled: false,
            supportsWinCap: false
        };
        jpDefaultTypes.returns(Promise.resolve([defaultType]));

        jpGameExists.returns(true);

        await JackpotTypeService.createAllDefined();
        const created = await JackpotTypeService.findAll();
        expect(created).to.deep.equal([defaultType]);
    });

    it("creates jackpot type from module", async() => {
        const createType = {
            name: "test",
            baseType: "base",
            jpGameId: "test",
            definition: {
                currency: "EUR",
                list: [{
                    id: "pool",
                    seed: {
                        amount: 100,
                    },
                    contribution: [],
                }],
            },
            supportsWinCap: false
        };

        jpTypeFromModule.returns(createType);

        jpGameExists.returns(true);

        const created = await JackpotTypeService.createTypeFromModule("test");
        expect(created).to.deep.equal(createType);
    });

    it("updates jackpot type from module", async() => {
        const createType = { ...type, name: "module-type" };

        jpTypeFromModule.returns(createType);

        jpGameExists.returns(true);

        const created = await JackpotTypeService.createTypeFromModule("module-type");
        expect(created).to.deep.equal(createType);

        const updateType = {
            name: "module-type",
            baseType: "base",
            jpGameId: "test",
            definition: {
                currency: "EUR",
                list: [{
                    id: "pool",
                    seed: {
                        amount: 200
                    }
                }]
            },
            configurable: false,
            overridable: false,
            canBeDisabled: false,
            supportsWinCap: false
        };
        jpTypeFromModule.returns(updateType);

        const updated = await JackpotTypeService.updateTypeFromModule("module-type");
        expect(updated).to.deep.equal(updateType);
    });

    it("creates jackpot type", async() => {
        jpGameExists.returns(true);

        const createdType = await JackpotTypeService.create(type);
        expect(createdType).to.deep.equal(type);
    });

    it("creates jackpot type without seed pot", async() => {
        jpGameExists.returns(true);

        const typeWtSeed: JackpotType = {
            name: "no-seed",
            baseType: "base",
            jpGameId: "test",
            definition: {
                currency: "EUR",
                list: [{
                    id: "pool",
                    contribution: [],
                }],
            },
            configurable: false,
            overridable: false,
            canBeDisabled: false,
            supportsWinCap: false
        };
        const createdType = await JackpotTypeService.create(typeWtSeed);
        expect(createdType).to.deep.equal(typeWtSeed);
    });

    it("fails to create jackpot type if game not found", async() => {
        jpGameExists.returns(false);

        await expect(JackpotTypeService.create(type)).to.be.rejectedWith(Errors.JackpotGameNotFound);
    });

    it("fails to create jackpot type if definition not valid", async() => {
        jpGameExists.returns(true);

        const invalidType: any = {
            name: "test",
            jpGameId: "test",
            definition: {},
        };

        await expect(JackpotTypeService.create(invalidType)).to.be.rejectedWith(Errors.ValidationError);
    });

    it("gets jackpot type", async() => {
        jpGameExists.returns(true);

        await JackpotTypeService.create(type);
        const createdType = await JackpotTypeService.find(type.name);
        expect(createdType).to.deep.equal(type);
    });

    it("fails to get jackpot type in not exist", async() => {
        await expect(JackpotTypeService.find(type.name)).to.be.rejectedWith(Errors.JackpotTypeNotFound);
    });

    it("gets all jackpot types", async() => {
        jpGameExists.returns(true);

        await JackpotTypeService.create(type);
        const types = await JackpotTypeService.findAll();
        expect(types).to.deep.equal([type]);
    });

    it("gets jackpot types by single name", async() => {
        await JackpotTypeService.create(type);
        const types = await JackpotTypeService.findAll([type.name]);
        expect(types).to.deep.equal([type]);
    });

    it("gets jackpot types by wrong name", async() => {
        await JackpotTypeService.create(type);
        const types = await JackpotTypeService.findAll(["wrong_name"]);
        expect(types).to.be.empty;
    });

    it("updates jackpot type", () => {

        it("updates jackpot type", async() => {
            jpGameExists.returns(true);

            await JackpotTypeService.create(type);

            const typeUpdate: JackpotType = {
                name: "test",
                baseType: "base",
                jpGameId: "test_updated",
                definition: {
                    currency: "EUR",
                    list: [{
                        id: "pool",
                        seed: {
                            amount: 100,
                        },
                        contribution: [],
                    }, {
                        id: "pool1",
                        seed: {
                            amount: 1000,
                        },
                        contribution: [],
                    }],
                },
            };
            const updatedType = await JackpotTypeService.update(type.name, typeUpdate);
            expect(updatedType).to.deep.equal(typeUpdate);
        });
    });

    it("fails to update jackpot type if game not found", async() => {
        jpGameExists.returns(true);

        await JackpotTypeService.create(type);

        jpGameExists.reset();
        jpGameExists.returns(false);
        const typeUpdate: JackpotType = {
            name: "test",
            baseType: "base",
            jpGameId: "test_updated",
            definition: {
                currency: "EUR",
                list: [{
                    id: "pool",
                    seed: {
                        amount: 100,
                    },
                    contribution: [],
                }],
            },
        };
        await expect(JackpotTypeService.update(type.name, typeUpdate)).to.be.rejectedWith(Errors.JackpotGameNotFound);
    });

    it("fails to update jackpot type if not valid", async() => {
        jpGameExists.returns(true);

        await JackpotTypeService.create(type);

        const typeUpdate: any = {
            name: "test",
            jpGameId: "test_updated",
            definition: {},
        };
        await expect(JackpotTypeService.update(type.name, typeUpdate)).to.be.rejectedWith(Errors.ValidationError);
    });

    it("fails to update jackpot type if set not configurable", async() => {
        jpGameExists.returns(true);

        const createType = { ...type, configurable: false };

        await JackpotTypeService.create(createType);

        const typeUpdate: any = {
            name: "test",
            definition: {
                currency: "EUR",
                list: [{
                    id: "pool",
                    seed: {
                        amount: 10,
                    },
                    contribution: [],
                }],
            }
        };
        await expect(JackpotTypeService.update(type.name, typeUpdate)).to.be.rejectedWith(Errors.ValidationError);
    });

    it("fails to update jackpot type with new currency", async() => {
        jpGameExists.returns(true);

        await JackpotTypeService.create(type);

        const typeUpdate: any = {
            name: "test",
            jpGameId: "test_updated",
            definition: {
                currency: "USD",
                list: [{
                    id: "pool",
                    seed: {
                        amount: 100,
                    },
                    contribution: [],
                }],
            },
        };
        await expect(JackpotTypeService.update(type.name, typeUpdate)).to.be.rejectedWith(Errors.ValidationError);
    });

    it("update jackpot type with new seed should be possible", async() => {
        jpGameExists.returns(true);

        await JackpotTypeService.create(type);

        const typeUpdate: any = {
            name: "test",
            baseType: "base",
            jpGameId: "test_updated",
            definition: {
                currency: "EUR",
                list: [{
                    id: "pool",
                    seed: {
                        amount: 1000,
                    },
                    contribution: [],
                }],
            },
            configurable: true,
            overridable: false,
            canBeDisabled: false,
            supportsWinCap: false
        };
        const updatedType = await JackpotTypeService.update(type.name, typeUpdate);
        expect(updatedType).to.deep.equal(typeUpdate);
    });

    it("update jackpot type without seed should be possible", async() => {
        jpGameExists.returns(true);

        await JackpotTypeService.create(type);

        const typeUpdate: any = {
            name: "test",
            baseType: "base",
            jpGameId: "test_updated",
            definition: {
                currency: "EUR",
                list: [{
                    id: "pool",
                    contribution: [],
                }],
            },
            configurable: true,
            overridable: false,
            canBeDisabled: false,
            supportsWinCap: false
        };
        const updatedType = await JackpotTypeService.update(type.name, typeUpdate);
        expect(updatedType).to.deep.equal(typeUpdate);
    });

    it("deletes jackpot type", async() => {
        jpGameExists.returns(true);

        await JackpotTypeService.create(type);
        await JackpotTypeService.remove(type.name);
    });

    it("fails to delete jackpot type if not exist", async() => {
        await expect(JackpotTypeService.remove(type.name)).to.be.rejectedWith(Errors.JackpotTypeNotFound);
    });

    it("test create jp type with supports win capping", async() => {
        jpGameExists.returns(true);
        const newType = { ...type, name: "test2", supportsWinCap: true };
        const createdType = await JackpotTypeService.create(newType);
        expect(createdType).to.deep.equal(newType);
    });
});
