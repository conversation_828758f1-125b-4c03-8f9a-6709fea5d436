import { expect, use } from "chai";
import { flushDb } from "../helpers";
import { JackpotAuditInitiator, JackpotAuditType, JackpotInstance } from "../../skywind/api/model";
import JackpotAuditService from "../../skywind/services/jackpotAudits";

use(require("chai-as-promised"));

describe("Jackpot Audit Service", () => {

    const instance: JackpotInstance = {
        id: "testid",
        type: "test",
    };

    before(async () => {
        await flushDb();
    });

    it("audit jackpot - user", async () => {
        await JackpotAuditService.auditJackpot(instance, JackpotAuditType.CREATE, {
            initiatorType: JackpotAuditInitiator.USER,
            initiatorName: "test",
            ip: "127.0.0.1",
            userAgent: "N/A"
        });

        await JackpotAuditService.auditJackpot(instance, JackpotAuditType.UPDATE, {
            initiatorType: JackpotAuditInitiator.USER,
            initiatorName: "test",
            ip: "127.0.0.1",
            userAgent: "N/A"
        });

        await JackpotAuditService.auditJackpot(instance, JackpotAuditType.DISABLE, {
            initiatorType: JackpotAuditInitiator.USER,
            initiatorName: "test",
            ip: "127.0.0.1",
            userAgent: "N/A"
        });

        const audits = await JackpotAuditService.findAll();

        expect(audits.map((item) => { delete item.ts; return item; })).to.deep.equal([{
            "history": {
                "id": "testid",
                "type": "test"
            },
            "initiatorName": "test",
            "initiatorType": "user",
            "ip": "127.0.0.1",
            "jackpotId": "testid",
            "type": "disable",
            "userAgent": "N/A"
        }, {
            "history": {
                "id": "testid",
                "type": "test"
            },
            "initiatorName": "test",
            "initiatorType": "user",
            "ip": "127.0.0.1",
            "jackpotId": "testid",
            "type": "update",
            "userAgent": "N/A"
        }, {
            "history": {
                "id": "testid",
                "type": "test"
            },
            "initiatorName": "test",
            "initiatorType": "user",
            "ip": "127.0.0.1",
            "jackpotId": "testid",
            "type": "create",
            "userAgent": "N/A"
        }]);
    });
});
