import { expect, use } from "chai";
import { DEFAULT_PRECISION, JackpotWallet } from "../../skywind/modules/jackpotWallet";
import {
    flush,
} from "../helpers";
import { stub, SinonStub } from "sinon";
import JackpotInstanceService from "../../skywind/services/jackpot.instance";
import { JackpotService } from "../../skywind/services/jackpot.service";
import { AuthRequest } from "@skywind-group/sw-jpn-core";
import wallet from "../../skywind/services/wallet.service";
import contextService from "../../skywind/services/jackpot.context";
import { init as initCurrencyRates } from "../../skywind/services/currency.service";
import {
    ValidationError
} from "../../skywind/errors";
import { BaseWalletParams } from "../../skywind/modules/walletParams";
import { JackpotInternalInstance } from "../../skywind/api/model";
import config from "../../skywind/config";

use(require("chai-as-promised"));

export const POOLS = [
    {
        "id": "challenge_pool",
        "seed": {
            "amount": 0
        },
        "contribution": [
            {
                "progressive": 0.5,
                "condition": {
                    "seed": {
                        "$gte": 0
                    }
                }
            },
            {
                "progressive": 0,
                "seed": 0.5
            }
        ]
    },
    {
        "id": "prize_pool",
        "seed": {
            "amount": 0
        },
        "contribution": [
            {
                "progressive": 0
            }
        ]
    }
];

export const DEFINITION = {
    "features": ["deposit", "transfer"],
    "currency": "EUR",
    "list": POOLS,
};

export const DEFINITION_WITH_DEPOSIT_TRANSFER = {
    "features": ["deposit", "transfer"],
    "currency": "EUR",
    "list": POOLS,
};

export const JP_NOT_TEST: JackpotInternalInstance = {
    "internalId": 1,
    "id": "JP-CHALLENGE-1",
    "type": "test-challenge",
    "jpGameId": "test-challenge",
    "precision": DEFAULT_PRECISION,
    "definition": DEFINITION,
};

export const JP_TEST: JackpotInternalInstance = {
    "internalId": 2,
    "id": "JP-CHALLENGE-2",
    "isTest": true,
    "type": "test-challenge",
    "jpGameId": "test-challenge",
    "precision": DEFAULT_PRECISION,
    "definition": DEFINITION_WITH_DEPOSIT_TRANSFER
};

describe("Jackpot Service With Update Pool", () => {

    let jpInstance = JP_NOT_TEST;
    const playerInfo: AuthRequest = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        jackpotIds: [jpInstance.id],
        currency: "EUR",
        gameCode: "test"
    };
    const walletParams: BaseWalletParams = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        playerCurrency: "EUR",
        gameCode: "test",
        gameId: "test",
        roundId: "1"
    };
    const jackpotService: JackpotService = new JackpotService(JackpotInstanceService, wallet, contextService);

    let lookup: SinonStub;

    before(async () => {
        lookup = stub(JackpotInstanceService, "findInternal");
        await initCurrencyRates();
    });

    beforeEach(async () => {
        await flush();
    });

    after(() => {
        lookup.restore();
    });

    it("Updates pool for not test jackpot instance - negative", async () => {

        jpInstance = JP_NOT_TEST;

        lookup.resetBehavior();
        lookup.returns(jpInstance);

        await expect(jackpotService.updatePool(playerInfo, "prize_pool",
            { seed: 101, progressive: 1001 })).to.be.rejectedWith(ValidationError);
    });

    it("Updates pool for in production environment - negative", async () => {

        config.environment = "production";

        jpInstance = JP_TEST;

        lookup.resetBehavior();
        lookup.returns(jpInstance);

        await expect(jackpotService.updatePool(playerInfo, "prize_pool",
            { seed: 101, progressive: 1001 })).to.be.rejectedWith(ValidationError);

        config.environment = "development";
    });

    async function checkTicker(jp: JackpotWallet, progressive: number, seed: number, seq: number) {
        const ticker = await jp.getTicker(true);
        expect(ticker).to.deep.equal({
            "currency": "EUR",
            "id": "JP-CHALLENGE-2",
            "pools": {
                "challenge_pool": {
                    "progressive": 0,
                    "seed": 0,
                },
                "prize_pool": {
                    "progressive": progressive,
                    "seed": seed,
                }
            },
            "seqId": seq
        });
    }

    it("Updates pool for test jackpot", async () => {

        jpInstance = JP_TEST;
        const jp = new JackpotWallet(walletParams, jpInstance, 1, wallet);

        lookup.resetBehavior();
        lookup.returns(jpInstance);

        await checkTicker(jp, 0, 0, 0);

        await jackpotService.updatePool(playerInfo, "prize_pool", { seed: 101, progressive: 1001 });
        await checkTicker(jp, 1001, 101, 1);

        await jackpotService.updatePool(playerInfo, "prize_pool", { progressive: 0 });
        await checkTicker(jp, 0, 101, 2);

        await jackpotService.updatePool(playerInfo, "prize_pool", { seed: 0 });
        await checkTicker(jp, 0, 0, 3);
    });
});
