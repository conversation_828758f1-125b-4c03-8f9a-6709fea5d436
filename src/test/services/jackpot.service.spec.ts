import { expect, use } from "chai";
import { DEFAULT_PRECISION, JackpotWallet } from "../../skywind/modules/jackpotWallet";
import {
    createTestJPGame,
    flush,
    getLastTransaction,
    getSpiedPlayerContributions,
    JP_POOL_SMALL_AMOUNT,
    JP_POOL_SMALL_PROGRESSIVE_PCT,
    JP_WITH_DYNAMIC_CONTRIBUTION,
    JP_WITH_VIRTUAL_SEED,
    JP_WITH_WIN_TRANSFERS,
} from "../helpers";
import { SinonSpy, SinonStub, spy, stub } from "sinon";
import JackpotInstanceService from "../../skywind/services/jackpot.instance";
import * as gameService from "../../skywind/services/game.service";
import { JackpotService } from "../../skywind/services/jackpot.service";
import {
    AuthRequest,
    BaseRequest,
    ContributionRequest,
    JackpotGame,
    PlayerInformation,
    PoolManipulationType,
    WinJackpotRequest
} from "@skywind-group/sw-jpn-core";
import wallet from "../../skywind/services/wallet.service";
import contextService from "../../skywind/services/jackpot.context";
import { init as initCurrencyRates } from "../../skywind/services/currency.service";
import { CheatingRandomGenerator } from "@skywind-group/sw-random-cs-cheating";
import * as Errors from "../../skywind/errors";
import {
    CURRENT_JACKPOT_CONTEXT_VERSION,
    JackpotContext,
    JackpotContextService,
    JackpotPlayerWin,
    JackpotStatus,
    JackpotWin,
    JackpotWinType
} from "../../definition";
import * as JackpotGameFlows from "../../skywind/services/jackpotGameFlow";
import { JackpotGameFlow } from "../../skywind/services/jackpotGameFlow";
import { JackpotModule } from "../../skywind/modules/jackpotModule";
import { BaseWalletParams } from "../../skywind/modules/walletParams";
import * as JWT from "jsonwebtoken";

use(require("chai-as-promised"));

describe("Jackpot Service", () => {

    const jpInstance = JP_WITH_DYNAMIC_CONTRIBUTION;
    const playerInfo: PlayerInformation = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        currency: "EUR",
        gameCode: "test"
    };
    const auth: AuthRequest = {
        ...playerInfo,
        jackpotIds: [jpInstance.id],
    };
    const walletParams: BaseWalletParams = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        playerCurrency: "EUR",
        gameCode: "test",
        gameId: "test",
        roundId: "1"
    };
    const cnyPlayerInfo: PlayerInformation = {
        playerCode: "PL-CODE-CNY",
        brandId: 1,
        region: "eu",
        currency: "CNY",
        gameCode: "test"
    };
    const cnyPlayer: AuthRequest = {
        ...cnyPlayerInfo,
        jackpotIds: [jpInstance.id],
    };
    let jpGame;
    const jackpotService: JackpotService = new JackpotService(JackpotInstanceService, wallet, contextService);

    let lookup: SinonStub;
    let findOrCreateTest: SinonStub;
    let getExternalTickers: SinonStub;
    let load: SinonStub;
    let jpCheckWin: SinonStub;
    let jpMiniGame: SinonStub;
    let jpWinJackpot: SinonStub;
    let walletFindCommitted: SinonStub;
    let spyJPContribute: SinonSpy;

    before(async () => {
        lookup = stub(JackpotInstanceService, "findInternal");
        findOrCreateTest = stub(JackpotInstanceService, "findOrCreateTest");
        getExternalTickers = stub(JackpotInstanceService, "getExternalTickers");
        load = stub(gameService, "load");
        walletFindCommitted = stub(wallet, "findCommittedTransaction");
        await initCurrencyRates();
    });

    beforeEach(async () => {
        await flush();

        const jpModule = new JackpotWallet(walletParams, jpInstance, 1, wallet);

        jpGame = await createTestJPGame(jpModule);
        spyJPContribute = spy(jpGame, "getContributions");

        jpCheckWin = stub(jpGame, "checkWin");
        jpMiniGame = stub(jpGame, "winMiniGame");
        jpWinJackpot = stub(jpGame, "winJackpot");

        lookup.resetBehavior();
        lookup.returns(jpInstance);
        findOrCreateTest.resetBehavior();
        getExternalTickers.resetBehavior();
        load.resetBehavior();
        load.returns(jpGame);
        jpCheckWin.resetHistory();
        jpMiniGame.resetHistory();
        jpWinJackpot.resetHistory();
    });

    after(() => {
        lookup.restore();
        findOrCreateTest.restore();
        getExternalTickers.restore();
        load.restore();
        jpCheckWin.restore();
        jpMiniGame.restore();
        jpWinJackpot.restore();
        walletFindCommitted.restore();
    });

    it("auth", async () => {
        const response = await jackpotService.auth(auth);
        expect(response.token).to.exist;
        expect(response.jackpots).to.deep.equal([
            {
                "id": "JP-TEST",
                "jpGameId": "test",
                "type": "test",
                "baseType": "base",
                "currency": "EUR",
                "isDisabled": false,
                "isTest": false,
                "isLocal": undefined,
                "createdFromId": undefined
            }
        ]);
    });

    it("auth disabled", async () => {
        lookup.returns({ ...jpInstance, isDisabled: true, disableMode: 0 });

        let response = await jackpotService.auth(auth);
        expect(response.token).to.exist;
        expect(response.jackpots).to.deep.equal([]);

        response = await jackpotService.auth({ ...auth, includeDisabled: true });
        expect(response.token).to.exist;
        expect(response.jackpots).to.deep.equal([
            {
                "id": "JP-TEST",
                "jpGameId": "test",
                "type": "test",
                "baseType": "base",
                "currency": "EUR",
                "isDisabled": true,
                "isTest": false,
                "isLocal": undefined,
                "createdFromId": undefined
            }
        ]);
    });

    it("auth test - auto create", async () => {
        findOrCreateTest.returns({
            ...jpInstance,
            id: jpInstance.id + "_test",
            isTest: true,
            createdFromId: jpInstance.id
        });
        const authTest: AuthRequest = {
            ...auth,
            isTest: true,
            autoCreateTestJackpot: true
        };
        const response = await jackpotService.auth(authTest);
        expect(response.token).to.exist;
        expect(response.jackpots).to.deep.equal([
            {
                "id": "JP-TEST_test",
                "jpGameId": "test",
                "type": "test",
                "baseType": "base",
                "currency": "EUR",
                "isDisabled": false,
                "isTest": true,
                "isLocal": undefined,
                "createdFromId": "JP-TEST"
            }
        ]);
    });

    it("auth - failed. Test player uses real jackpot", async () => {
        const authTest: AuthRequest = {
            ...auth,
            isTest: true,
            autoCreateTestJackpot: false
        };
        await expect(jackpotService.auth(authTest)).to.be.rejectedWith(Errors.TestPlayerUsesRealJackpotError);
    });

    it("auth - failed. Real player uses test jackpot", async () => {
        lookup.returns({
            ...jpInstance,
            id: jpInstance.id + "_test",
            isTest: true,
            createdFromId: jpInstance.id
        });
        const authReal: AuthRequest = {
            ...auth,
            isTest: false,
            jackpotIds: [jpInstance.id],
        };
        await expect(jackpotService.auth(authReal)).to.be.rejectedWith(Errors.RealPlayerUsesTestJackpotError);
    });

    it("gets ticker", async () => {
        const ticker = await jackpotService.getTicker(auth, {});
        expect(ticker).to.deep.equal([
            {
                "jackpotId": jpInstance.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": 0
                    },
                },
            }
        ]);
    });

    it("gets ticker with custom exchange rate", async () => {
        const ticker = await jackpotService.getTicker(auth, { exchangeRates: { "EUR": 2 } });
        expect(ticker).to.deep.equal([
            {
                "jackpotId": jpInstance.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 500,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 50,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 5,
                        "progressive": 0,
                        "seed": 0
                    },
                },
            }
        ]);
    });

    it("gets ticker in different currency", async () => {
        const ticker = await jackpotService.getTicker(cnyPlayer, {});
        expect(ticker).to.deep.equal([
            {
                "jackpotId": jpInstance.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 7965.12,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 796.51,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 79.65,
                        "progressive": 0,
                        "seed": 0
                    },
                },
            }
        ]);
    });

    it("gets ticker with info", async () => {
        const instanceWithInfo = {
            "internalId": 11,
            "id": "JP-TEST-INFO",
            "type": "test",
            "baseType": "base",
            "jpGameId": "test",
            "precision": DEFAULT_PRECISION,
            "definition": {
                "currency": "EUR",
                "list": [
                    {
                        "id": "bet10",
                        "seed": {
                            "amount": 10,
                        },
                        "contribution": [
                            {
                                "seed": 0.1,
                                "progressive": 0.2,
                            },
                        ],
                        "info": {
                            "title": "Minor",
                            "bet": {
                                "$lte": "exchange(10)",
                            },
                        },
                    }, {
                        "id": "bet20",
                        "seed": {
                            "amount": 20,
                        },
                        "contribution": [
                            {
                                "seed": 0.2,
                                "progressive": 0.3,
                            },
                        ],
                        "info": {
                            "title": "Major",
                            "bet": {
                                "$gt": "exchange(10)", "$lte": "exchange(20)",
                            },
                        },
                    }, {
                        "id": "bet30",
                        "seed": {
                            "amount": 30,
                        },
                        "contribution": [
                            {
                                "seed": 0.3,
                                "progressive": 0.4,
                            },
                        ],
                        "info": {
                            "title": "Mega",
                            "bet": {
                                "$gt": "exchange(20)",
                            },
                        },
                    },
                ],
            },
        };
        const playerWithInfo: AuthRequest = {
            playerCode: "PL-CODE-INFO",
            brandId: 1,
            region: "eu",
            jackpotIds: [instanceWithInfo.id],
            currency: "CNY",
            gameCode: "test"
        };
        lookup.returns(instanceWithInfo);

        const ticker = await jackpotService.getTicker(playerWithInfo, {});
        expect(ticker).to.deep.equal([
            {
                "jackpotId": instanceWithInfo.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "bet10": {
                        "amount": 79.65,
                        "progressive": 0,
                        "seed": 0,
                        "info": {
                            "bet": {
                                "$lte": 79.65124278,
                            },
                            "title": "Minor",
                        },
                    },
                    "bet20": {
                        "amount": 159.3,
                        "progressive": 0,
                        "seed": 0,
                        "info": {
                            "bet": {
                                "$gt": 79.65124278, "$lte": 159.30248555,
                            },
                            "title": "Major",
                        },
                    },
                    "bet30": {
                        "amount": 238.95,
                        "progressive": 0,
                        "seed": 0,
                        "info": {
                            "bet": {
                                "$gt": 159.30248555,
                            },
                            "title": "Mega",
                        },
                    },
                },
            }
        ]);
    });

    it("gets ticker with info floating point error", async () => {
        const instanceWithInfo = {
            "internalId": 11,
            "id": "JP-TEST-INFO",
            "type": "test",
            "baseType": "base",
            "jpGameId": "test",
            "precision": DEFAULT_PRECISION,
            "definition": {
                "currency": "EUR",
                "list": [
                    {
                        "id": "bet10",
                        "seed": {
                            "amount": 10,
                        },
                        "contribution": [
                            {
                                "seed": 0.1,
                                "progressive": 0.2,
                            },
                        ],
                        "info": {
                            "title": "Minor",
                            "bet": {
                                "$lte": "exchange(1)",
                            },
                        },
                    }, {
                        "id": "bet20",
                        "seed": {
                            "amount": 20,
                        },
                        "contribution": [
                            {
                                "seed": 0.2,
                                "progressive": 0.3,
                            },
                        ],
                        "info": {
                            "title": "Major",
                            "bet": {
                                "$gt": "exchange(2)", "$lte": "exchange(5)",
                            },
                        },
                    }, {
                        "id": "bet30",
                        "seed": {
                            "amount": 30,
                        },
                        "contribution": [
                            {
                                "seed": 0.3,
                                "progressive": 0.4,
                            },
                        ],
                        "info": {
                            "title": "Mega",
                            "bet": {
                                "$gt": "exchange(10)", "$lte": "exchange(300)",
                            },
                        },
                    },
                ],
            },
        };
        const playerWithInfo: AuthRequest = {
            playerCode: "PL-CODE-INFO",
            brandId: 1,
            region: "eu",
            jackpotIds: [instanceWithInfo.id],
            currency: "VND",
            gameCode: "test"
        };

        lookup.returns(instanceWithInfo);

        const ticker = await jackpotService.getTicker(playerWithInfo, { exchangeRates: { EUR: 0.00004 } });

        expect(Object.values(ticker[0].pools).map(p => {
            delete p.amount;
            delete p.progressive;
            delete p.seed;
            return p;
        })).to.deep.equal(
            [
                {
                    "info": {
                        "bet": {
                            "$lte": 25000,
                        },
                        "title": "Minor",
                    },
                },
                {
                    "info": {
                        "bet": {
                            "$gt": 50000, "$lte": 125000,
                        },
                        "title": "Major",
                    },
                },
                {
                    "info": {
                        "bet": {
                            "$gt": 250000,
                            "$lte": 7500000
                        },
                        "title": "Mega",
                    },
                }
            ]);
    });

    it("gets ticker with external jackpot", async () => {
        const authWithExtJackpot: AuthRequest = {
            ...playerInfo,
            jackpotIds: [jpInstance.id, "PP;jp1;sc1_wg1_EUR"]
        };

        getExternalTickers.returns([{
            jackpotId: "jp1",
            jackpotType: "jp1_type",
            pools: {
                small: {
                    amount: 100
                },
                big: {
                    amount: 1000
                }
            }
        }]);

        const ticker = await jackpotService.getTicker(authWithExtJackpot, {});
        expect(getExternalTickers.lastCall.args[0]).to.equal("PP");
        expect(getExternalTickers.lastCall.args[1]).to.deep.equal(["PP;jp1;sc1_wg1_EUR"]);
        expect(ticker).to.deep.equal([
            {
                "jackpotId": jpInstance.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": 0
                    },
                },
            },
            {
                "jackpotId": "jp1",
                "jackpotType": "jp1_type",
                "jackpotBaseType": "jp1_type",
                "pools": {
                    "small": {
                        "amount": 100
                    },
                    "big": {
                        "amount": 1000
                    }
                }
            }
        ]);
    });

    it("checks win", async () => {
        jpCheckWin.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        const events = await jackpotService.checkWin(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({ "events": [] });

        const context = await contextService.find(jpInstance.id, auth, "123");
        expect(context).to.not.exist;

        const ticker = await jackpotService.getTicker(auth, { exchangeRates: { "EUR": 1 } });
        expect(ticker).to.deep.equal([
            {
                "jackpotId": jpInstance.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": 0
                    },
                },
            }
        ]);
    });

    it("checks win and wins jackpot", async () => {
        jpCheckWin.returns({
            type: "win",
            pool: "small",
        });

        const trxId = await wallet.generateTransactionId();
        let events = await jackpotService.checkWin(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "pool": "small",
                    "title": "friendly name",
                    "amount": 10,
                    "progressive": 0,
                    "seed": 10
                }
            ],
        });

        let context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "small",
                "type": "win"
            },
            "win": [
                {
                    "type": "player",
                    "pool": "small",
                    "amount": 10,
                    "playerAmount": 10,
                    "seed": 10,
                    "progressive": 0,
                    "exchangeRate": 1,
                }
            ],
        });

        // confirms win
        events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.not.exist;

        const ticker = await jackpotService.getTicker(auth, { exchangeRates: { "EUR": 1 } });
        expect(ticker).to.deep.equal([
            {
                "jackpotId": jpInstance.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": -10
                    },
                },
            }
        ]);
    });

    it("contributes", async () => {
        jpCheckWin.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        const events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyJPContribute),
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10.03,
                            "progressive": 0.03,
                            "seed": 0.01
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        });

        const context = await contextService.find(jpInstance.id, auth, "123");
        expect(context).to.not.exist;
    });

    it("contributes with specified jackpotId", async () => {
        jpCheckWin.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        const events = await jackpotService.contribute(auth, {
            externalId: "123",
            jackpotIds: [jpInstance.id],
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyJPContribute),
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10.03,
                            "progressive": 0.03,
                            "seed": 0.01
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        });

        const context = await contextService.find(jpInstance.id, auth, "123");
        expect(context).to.not.exist;
    });

    it("cheating not allowed", async () => {
        jpCheckWin.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1",
            cheats: [1],
        });

        expect(load.lastCall.args[3]).not.to.be.instanceof(CheatingRandomGenerator);
    });

    describe("cheating allowed", () => {

        const cheatsConfig = require("../../../resources/cheatsConfig.json");

        before(() => {
            cheatsConfig.allowSetPositionsByClient = true;
        });

        after(() => {
            cheatsConfig.allowSetPositionsByClient = false;
        });

        it("cheats", async () => {
            jpCheckWin.returns(undefined);

            const trxId = await wallet.generateTransactionId();
            await jackpotService.contribute(auth, {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1",
                cheats: [1],
            });

            expect(load.lastCall.args[3]).to.be.instanceof(CheatingRandomGenerator);
        });
    });

    it("contributes in different currency", async () => {
        jpCheckWin.returns(undefined);

        const trxId = await wallet.generateTransactionId();

        const events = await jackpotService.contribute(cnyPlayer, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyJPContribute,
                        0.125547319179561),
                    "pools": {
                        "large": {
                            "amount": 7965.19,
                            "progressive": 0.008788312,
                            "seed": 0.00376642
                        },
                        "medium": {
                            "amount": 796.55,
                            "progressive": 0.005021893,
                            "seed": 0.002510946
                        },
                        "small": {
                            "amount": 79.68,
                            "progressive": 0.00376642,
                            "seed": 0.001255473
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        });

        const context = await contextService.find(jpInstance.id, cnyPlayer, "123");
        expect(context).to.not.exist;
    });

    it("contributes and wins jackpot", async () => {
        jpCheckWin.returns({
            type: "win",
            pool: "small",
        });

        // contributes and wins
        const trxId = await wallet.generateTransactionId();
        let events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "pool": "small",
                    "title": "friendly name",
                    "amount": 10.03,
                    "progressive": 0.03,
                    "seed": 10
                },
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyJPContribute),
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10,
                            "progressive": 0,
                            "seed": -9.99
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        });

        let context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "small",
                "type": "win"
            },
            "win": [
                {
                    "type": "player",
                    "pool": "small",
                    "amount": 10.03,
                    "playerAmount": 10.03,
                    "seed": 10,
                    "progressive": 0.03,
                    "exchangeRate": 1,
                }
            ]
        });

        // confirms win
        events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.not.exist;
    });

    it("contributes and wins multiple jackpots", async () => {
        jpCheckWin.returns([
            {
                type: "win",
                pool: "small",
            }, {
                type: "win",
                pool: "medium"
            }
        ]);

        // contributes and wins
        const trxId = await wallet.generateTransactionId();
        let events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "pool": "small",
                    "title": "friendly name",
                    "amount": 10.03,
                    "progressive": 0.03,
                    "seed": 10
                },
                {
                    "type": "win",
                    "jackpotId": jpInstance.id,
                    "transactionId": trxId,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pool": "medium",
                    "title": "medium",
                    "amount": 100.04,
                    "progressive": 0.04,
                    "seed": 100
                },
                {
                    "jackpotId": jpInstance.id,
                    "contributions": getSpiedPlayerContributions(spyJPContribute),
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100,
                            "progressive": 0,
                            "seed": -99.98
                        },
                        "small": {
                            "amount": 10,
                            "progressive": 0,
                            "seed": -9.99
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        });

        let context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": [
                {
                    "pool": "small",
                    "type": "win"
                }, {
                    "pool": "medium",
                    "type": "win"
                }
            ],
            "win": [
                {
                    "type": "player",
                    "pool": "small",
                    "amount": 10.03,
                    "playerAmount": 10.03,
                    "exchangeRate": 1,
                    "seed": 10,
                    "progressive": 0.03
                }, {
                    "type": "player",
                    "pool": "medium",
                    "amount": 100.04,
                    "playerAmount": 100.04,
                    "exchangeRate": 1,
                    "seed": 100,
                    "progressive": 0.04
                }
            ]
        });

        // confirms win
        events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.not.exist;
    });

    it("contributes and wins same jackpot pool multiple times", async () => {
        jpCheckWin.returns([
            {
                type: "win",
                pool: "small",
            }, {
                type: "win",
                pool: "small"
            }
        ]);

        // contributes and wins
        const trxId = await wallet.generateTransactionId();
        let events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "pool": "small",
                    "title": "friendly name",
                    "amount": 10.03,
                    "progressive": 0.03,
                    "seed": 10
                },
                {
                    "type": "win",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "pool": "small",
                    "title": "friendly name",
                    "amount": 10,
                    "progressive": 0.0,
                    "seed": 10
                },
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyJPContribute),
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10,
                            "progressive": 0,
                            "seed": -19.99
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        });

        let context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": [
                {
                    "pool": "small",
                    "type": "win"
                }, {
                    "pool": "small",
                    "type": "win"
                }
            ],
            "win": [
                {
                    "type": "player",
                    "pool": "small",
                    "amount": 10.03,
                    "playerAmount": 10.03,
                    "exchangeRate": 1,
                    "seed": 10,
                    "progressive": 0.03
                }, {
                    "type": "player",
                    "pool": "small",
                    "amount": 10,
                    "playerAmount": 10,
                    "exchangeRate": 1,
                    "seed": 10,
                    "progressive": 0
                }
            ]
        });

        // confirms win
        events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance.id,
                    "transactionId": trxId,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                },
            ],
        });

        context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.not.exist;
    });

    it("contributes and wins jackpot with transfer", async () => {
        const instance = { ...JP_WITH_WIN_TRANSFERS };
        const jp = new JackpotWallet(walletParams, instance, 1, wallet);
        const gameWithTransfer = await createTestJPGame(jp);

        lookup.returns(instance);
        load.returns(gameWithTransfer);

        const spyGetContributions = spy(gameWithTransfer, "getContributions");
        const checkWin = stub(gameWithTransfer, "checkWin");
        const winJackpot = stub(gameWithTransfer, "winJackpot");
        const getWinPayouts = stub(gameWithTransfer, "getWinPayouts");

        // main win

        checkWin.resolves({
            type: "win",
        });

        getWinPayouts.returns([
            {
                amount: 80,
                pool: "main",
                progressive: 20,
                seed: 60
            },
            {
                amount: 60,
                pool: "main",
                progressive: 20,
                seed: 40,
                transferPool: "prize"
            }
        ]);

        // contributes and wins
        const trxId = await wallet.generateTransactionId();
        let events = await jackpotService.contribute({
            ...playerInfo,
            jackpotIds: [instance.id],
        }, {
            externalId: "123",
            transactionId: trxId,
            amount: 10000,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win",
                    "jackpotId": instance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "pool": "main",
                    "title": "main",
                    "amount": 80,
                    "progressive": 20,
                    "seed": 60
                },
                {
                    "jackpotId": instance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyGetContributions),
                    "pools": {
                        "main": {
                            "amount": 100,
                            "progressive": 0,
                            "seed": -80
                        },
                        "prize": {
                            "amount": 20,
                            "progressive": 20,
                            "seed": 40
                        }
                    },
                    "type": "contribution",
                    "totalContribution": 60
                },
            ],
        });

        let context = await contextService.find(instance.id, auth, trxId);
        expect(context).to.deep.equal({
            "jackpotId": instance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "type": "win"
            },
            "win": [
                {
                    "amount": 80,
                    "exchangeRate": 1,
                    "playerAmount": 80,
                    "pool": "main",
                    "progressive": 20,
                    "seed": 60,
                    "type": "player"
                }, {
                    "amount": 60,
                    "pool": "main",
                    "progressive": 20,
                    "seed": 40,
                    "transferPool": "prize",
                    "type": "transfer"
                }
            ]
        });

        // confirms win
        events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: instance.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": instance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(instance.id, auth, trxId);
        expect(context).to.not.exist;

        // prize win
        winJackpot.resolves({
            type: "win",
            pool: "prize",
        });

        getWinPayouts.returns([
            {
                amount: 20,
                pool: "prize",
                progressive: 5,
                seed: 15
            }
        ]);

        // contributes and wins
        const prizeTrxId = await wallet.generateTransactionId();
        let prizeEvents = await jackpotService.winJackpot({
            ...playerInfo,
            jackpotIds: [instance.id],
        }, {
            externalId: "123",
            transactionId: prizeTrxId,
            jackpotId: instance.id,
            roundId: "1"
        });

        expect(prizeEvents).to.deep.equal({
            "events": [
                {
                    "type": "win",
                    "jackpotId": instance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": prizeTrxId,
                    "pool": "prize",
                    "title": "prize",
                    "amount": 20,
                    "progressive": 5,
                    "seed": 15
                }
            ],
        });

        let prizeContext = await contextService.find(instance.id, auth, prizeTrxId);
        expect(prizeContext).to.deep.equal({
            "jackpotId": instance.id,
            "transactionId": prizeTrxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "prize",
                "type": "win"
            },
            "win": [
                {
                    "amount": 20,
                    "exchangeRate": 1,
                    "playerAmount": 20,
                    "pool": "prize",
                    "progressive": 5,
                    "seed": 15,
                    "type": "player"
                }
            ]
        });

        // confirms win
        prizeEvents = await jackpotService.confirmWin(auth, { transactionId: prizeTrxId, jackpotId: instance.id, roundId: "1" });
        expect(prizeEvents).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": instance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": prizeTrxId,
                },
            ],
        });

        prizeContext = await contextService.find(instance.id, auth, prizeTrxId);
        expect(prizeContext).to.not.exist;
    });

    it("contributes and wins jackpot with transfer to void", async () => {
        const instance = { ...JP_WITH_WIN_TRANSFERS };
        const jp = new JackpotWallet(walletParams, instance, 1, wallet);
        const gameWithTransfer = await createTestJPGame(jp);

        lookup.returns(instance);
        load.returns(gameWithTransfer);

        const spyGetContributions = spy(gameWithTransfer, "getContributions");
        const checkWin = stub(gameWithTransfer, "checkWin");
        const getWinPayouts = stub(gameWithTransfer, "getWinPayouts");

        // main win

        checkWin.resolves({
            type: "win",
        });

        getWinPayouts.returns([
            {
                amount: 80,
                pool: "main",
                progressive: 20,
                seed: 60
            },
            {
                amount: 60,
                pool: "main",
                progressive: 20,
                seed: 40,
                transferPool: null
            }
        ]);

        // contributes and wins
        const trxId = await wallet.generateTransactionId();
        let events = await jackpotService.contribute({
            ...playerInfo,
            jackpotIds: [instance.id],
        }, {
            externalId: "123",
            transactionId: trxId,
            amount: 10000,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win",
                    "jackpotId": instance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "pool": "main",
                    "title": "main",
                    "amount": 80,
                    "progressive": 20,
                    "seed": 60
                },
                {
                    "jackpotId": instance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyGetContributions),
                    "pools": {
                        "main": {
                            "amount": 100,
                            "progressive": 0,
                            "seed": -80
                        },
                        "prize": {
                            "amount": 0,
                            "progressive": 0,
                            "seed": 0
                        }
                    },
                    "type": "contribution",
                    "totalContribution": 60
                },
            ],
        });

        let context = await contextService.find(instance.id, auth, trxId);
        expect(context).to.deep.equal({
            "jackpotId": instance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "type": "win"
            },
            "win": [
                {
                    "amount": 80,
                    "exchangeRate": 1,
                    "playerAmount": 80,
                    "pool": "main",
                    "progressive": 20,
                    "seed": 60,
                    "type": "player"
                }, {
                    "amount": 60,
                    "pool": "main",
                    "progressive": 20,
                    "seed": 40,
                    "transferPool": null,
                    "type": "transfer"
                }
            ]
        });

        // confirms win
        events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: instance.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": instance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(instance.id, auth, trxId);
        expect(context).to.not.exist;
    });

    it("contributes with internal transfer", async () => {
        const instance = { ...JP_WITH_WIN_TRANSFERS };
        const jp = new JackpotWallet(walletParams, instance, 1, wallet);
        const gameWithTransfer = await createTestJPGame(jp);

        lookup.returns(instance);
        load.returns(gameWithTransfer);
        const jpGetModifications = stub(gameWithTransfer, "getAdditionalPoolManipulations");
        jpGetModifications.returns([
            {
                pool: "main",
                type: PoolManipulationType.InternalTransferFromSeed, payload: { restValue: 1 }
            }
        ]);

        // contributes and wins
        const trxId = await wallet.generateTransactionId();
        await jackpotService.contribute({
            ...playerInfo,
            jackpotIds: [instance.id],
        }, {
            externalId: "123",
            transactionId: trxId,
            amount: 10000,
            roundId: "1"
        });
        const ticker = await jackpotService.getTicker({
            ...playerInfo,
            jackpotIds: [instance.id],
        }, {} as any);
        expect(ticker[0].pools.main).deep.equal({
            "amount": 159,
            "progressive": 59,
            "seed": 1
        });
    });

    describe("retries to win jackpot on insufficient balance", () => {

        let createJackpotGameFlowStub;
        let jpGetWinPayouts: SinonStub;
        let jpValidateCheckWin: SinonStub;

        before(() => {
            const createJackpotGameFlow = JackpotGameFlows.createJackpotGameFlow;

            createJackpotGameFlowStub = stub(JackpotGameFlows, "createJackpotGameFlow").callsFake(
                async (request: BaseRequest, jpModule: JackpotModule,
                       game: JackpotGame, contextService: JackpotContextService): Promise<JackpotGameFlow> => {
                    const flow = await createJackpotGameFlow(request, jpModule, game, contextService);

                    const jpWallet = jpModule["jpWallet"];
                    const jpModuleWin = jpWallet.releaseWin;

                    jpWallet.releaseWin = async (trxId: string,
                                                 externalId: string,
                                                 roundId: string,
                                                 wins: JackpotWin[]) => {
                        // concurrent win
                        const otherTrxId = await wallet.generateTransactionId();
                        const otherModule = new JackpotWallet(walletParams, jpInstance, 1, wallet);
                        await otherModule.releaseWin(otherTrxId, "concurrent", "concurrent", [wins[0]]);

                        return jpModuleWin.call(jpWallet, trxId, externalId, roundId, wins);
                    };

                    return flow;
                });
        });

        beforeEach(() => {
            jpGetWinPayouts = stub(jpGame, "getWinPayouts");
            jpValidateCheckWin = stub(jpGame, "validateCheckWin");
        });

        afterEach(() => {
            jpGetWinPayouts.restore();
            jpValidateCheckWin.restore();
        });

        after(() => {
            createJackpotGameFlowStub.restore();
        });

        it("retries to win jackpot on insufficient balance", async () => {
            jpCheckWin.returns({
                type: "win",
                pool: "small",
            });
            jpValidateCheckWin.returns({
                type: "win",
                pool: "small",
            });
            jpGetWinPayouts.onFirstCall().returns([
                {
                    "pool": "small",
                    "amount": 10.03,
                    "progressive": 0.03,
                    "seed": 10
                }
            ]);
            jpGetWinPayouts.onSecondCall().returns([
                {
                    "pool": "small",
                    "amount": 10,
                    "progressive": 0,
                    "seed": 10
                }
            ]);

            // contributes and wins
            const trxId = await wallet.generateTransactionId();
            let events = await jackpotService.contribute(auth, {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1"
            });

            expect(events).to.deep.equal({
                "events": [
                    {
                        "type": "win",
                        "jackpotId": jpInstance.id,
                        "transactionId": trxId,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "pool": "small",
                        "title": "friendly name",
                        "amount": 10,
                        "progressive": 0,
                        "seed": 10
                    },
                    {
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "contributions": getSpiedPlayerContributions(spyJPContribute),
                        "pools": {
                            "large": {
                                "amount": 1000.07,
                                "progressive": 0.07,
                                "seed": 0.03
                            },
                            "medium": {
                                "amount": 100.04,
                                "progressive": 0.04,
                                "seed": 0.02
                            },
                            "small": {
                                "amount": 10,
                                "progressive": 0,
                                "seed": -29.99
                            },
                        },
                        "type": "contribution",
                        "totalContribution": 0.2
                    },
                ],
            });

            let context = await contextService.find(jpInstance.id, auth, trxId);
            expect(context).to.deep.equal({
                "jackpotId": jpInstance.id,
                "transactionId": trxId,
                "externalId": "123",
                "playerInfo": playerInfo,
                "roundId": "1",
                "status": 3,
                "version": CURRENT_JACKPOT_CONTEXT_VERSION,
                "result": {
                    "pool": "small",
                    "type": "win"
                },
                "win": [
                    {
                        "type": "player",
                        "pool": "small",
                        "amount": 10,
                        "playerAmount": 10,
                        "exchangeRate": 1,
                        "progressive": 0,
                        "seed": 10
                    }
                ]
            });

            // confirms win
            events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
            expect(events).to.deep.equal({
                "events": [
                    {
                        "type": "win-confirm",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                    },
                ],
            });

            context = await contextService.find(jpInstance.id, auth, trxId);
            expect(context).to.not.exist;
        });

        it("retries to win multiple jackpots on insufficient balance", async () => {
            const gameResult = [
                {
                    type: "win",
                    pool: "small",
                }, {
                    type: "win",
                    pool: "medium",
                }
            ];
            jpCheckWin.returns(gameResult);
            jpValidateCheckWin.returns(gameResult);
            jpGetWinPayouts.onFirstCall().returns([
                {
                    "pool": "small",
                    "amount": 10.03,
                    "progressive": 0.03,
                    "seed": 10
                }
            ]);
            jpGetWinPayouts.onSecondCall().returns([
                {
                    "pool": "small",
                    "amount": 10,
                    "progressive": 0,
                    "seed": 10
                }, {
                    "pool": "medium",
                    "amount": 100.04,
                    "progressive": 0.04,
                    "seed": 100
                }
            ]);

            // contributes and wins
            const trxId = await wallet.generateTransactionId();
            let events = await jackpotService.contribute(auth, {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1"
            });

            expect(events).to.deep.equal({
                "events": [
                    {
                        "type": "win",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                        "pool": "small",
                        "title": "friendly name",
                        "amount": 10,
                        "progressive": 0.0,
                        "seed": 10
                    },
                    {
                        "type": "win",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                        "pool": "medium",
                        "title": "medium",
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 100
                    },
                    {
                        "jackpotId": jpInstance.id,
                        "contributions": getSpiedPlayerContributions(spyJPContribute),
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "pools": {
                            "large": {
                                "amount": 1000.07,
                                "progressive": 0.07,
                                "seed": 0.03
                            },
                            "medium": {
                                "amount": 100,
                                "progressive": 0,
                                "seed": -99.98
                            },
                            "small": {
                                "amount": 10,
                                "progressive": 0,
                                "seed": -29.99
                            },
                        },
                        "type": "contribution",
                        "totalContribution": 0.2
                    },
                ],
            });

            let context = await contextService.find(jpInstance.id, auth, trxId);
            expect(context).to.deep.equal({
                "jackpotId": jpInstance.id,
                "transactionId": trxId,
                "externalId": "123",
                "playerInfo": playerInfo,
                "roundId": "1",
                "status": 3,
                "version": CURRENT_JACKPOT_CONTEXT_VERSION,
                "result": [
                    {
                        "pool": "small",
                        "type": "win"
                    },
                    {
                        "pool": "medium",
                        "type": "win"
                    }
                ],
                "win": [
                    {
                        "type": "player",
                        "pool": "small",
                        "amount": 10,
                        "playerAmount": 10,
                        "exchangeRate": 1,
                        "progressive": 0,
                        "seed": 10
                    }, {
                        "type": "player",
                        "pool": "medium",
                        "amount": 100.04,
                        "playerAmount": 100.04,
                        "exchangeRate": 1,
                        "progressive": 0.04,
                        "seed": 100
                    }
                ]
            });

            // confirms win
            events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
            expect(events).to.deep.equal({
                "events": [
                    {
                        "type": "win-confirm",
                        "jackpotId": jpInstance.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                    },
                ],
            });

            context = await contextService.find(jpInstance.id, auth, trxId);
            expect(context).to.not.exist;
        });

        it("retries to win jackpot on insufficient balance till attempts exhausted", async () => {
            const gameResult = {
                type: "win",
                pool: "small",
                amount: 10.03,
            };
            jpCheckWin.returns(gameResult);
            jpValidateCheckWin.returns(gameResult);
            jpGetWinPayouts.returns([
                {
                    "pool": "small",
                    "amount": 10.03,
                    "progressive": 0.03,
                    "seed": 10
                }
            ]);

            // contributes and wins
            const trxId = await wallet.generateTransactionId();

            await expect(jackpotService.contribute(auth, {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1"
            })).to.be.rejectedWith(Errors.ConcurrentJackpotWin);

            const context = await contextService.find(jpInstance.id, auth, trxId);
            expect(context).to.not.exist;
        });

        it("rejects to win jackpot on retry", async () => {
            const gameResult = {
                type: "win",
                pool: "small",
                amount: 10.03,
            };
            jpCheckWin.returns(gameResult);
            jpValidateCheckWin.onFirstCall().returns(gameResult);
            jpValidateCheckWin.onSecondCall().returns(undefined);
            jpGetWinPayouts.returns([
                {
                    "pool": "small",
                    "amount": 10.03,
                    "progressive": 0.03,
                    "seed": 10
                }
            ]);

            // contributes and wins
            const trxId = await wallet.generateTransactionId();

            const result = await jackpotService.contribute(auth, {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1"
            });

            expect(result).to.deep.equal({
                "events": [
                    {
                        "jackpotId": jpInstance.id,
                        "contributions": getSpiedPlayerContributions(spyJPContribute),
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "pools": {
                            "large": {
                                "amount": 1000.07,
                                "progressive": 0.07,
                                "seed": 0.03
                            },
                            "medium": {
                                "amount": 100.04,
                                "progressive": 0.04,
                                "seed": 0.02
                            },
                            "small": {
                                "amount": 10,
                                "progressive": 0,
                                "seed": -9.99
                            },
                        },
                        "type": "contribution",
                        "totalContribution": 0.2
                    },
                ],
            });

            const context = await contextService.find(jpInstance.id, auth, trxId);
            expect(context).to.not.exist;
        });

        it("rejects jackpot on calculate win when pool was changed", async () => {
            const gameResult = {
                type: "win",
                pool: "small",
                amount: 10.03,
            };
            jpCheckWin.returns(gameResult);
            jpValidateCheckWin.returns(undefined);
            // contributes and wins
            const trxId = await wallet.generateTransactionId();

            const result = await jackpotService.contribute(auth, {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1"
            });

            expect(result).to.deep.equal({
                "events": [
                    {
                        "jackpotId": jpInstance.id,
                        "contributions": getSpiedPlayerContributions(spyJPContribute),
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "pools": {
                            "large": {
                                "amount": 1000.07,
                                "progressive": 0.07,
                                "seed": 0.03
                            },
                            "medium": {
                                "amount": 100.04,
                                "progressive": 0.04,
                                "seed": 0.02
                            },
                            "small": {
                                "amount": 10.03,
                                "progressive": 0.03,
                                "seed": 0.01
                            },
                        },
                        "type": "contribution",
                        "totalContribution": 0.2
                    },
                ],
            });

            const context = await contextService.find(jpInstance.id, auth, trxId);
            expect(context).to.not.exist;
        });
    });

    it("contributes and wins jackpot in different currency", async () => {
        jpCheckWin.returns({
            type: "win",
            pool: "small",
        });

        // contributes and wins
        const trxId = await wallet.generateTransactionId();
        let events = await jackpotService.contribute(cnyPlayer, {
            externalId: "123",
            transactionId: trxId,
            amount: 73.13,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "pool": "small",
                    "title": "friendly name",
                    "amount": 79.87,
                    "progressive": 0.21938999717394295,
                    "seed": 79.65124277721728
                },
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyJPContribute,
                        0.125547319179561),
                    "pools": {
                        "large": {
                            "amount": 7965.63,
                            "progressive": 0.064268928,
                            "seed": 0.027543826
                        },
                        "medium": {
                            "amount": 796.8,
                            "progressive": 0.036725102,
                            "seed": 0.018362551
                        },
                        "small": {
                            "amount": 79.65,
                            "progressive": 0,
                            "seed": -9.990818725
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 1.4626 // = 0.20007742978031734 / exchangeRate CNY
                },
            ],
        });

        let context = await contextService.find(jpInstance.id, cnyPlayer, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": cnyPlayerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "small",
                "type": "win"
            },
            "win": [
                {
                    "type": "player",
                    "pool": "small",
                    "amount": 10.027543826,
                    "playerAmount": 79.87,
                    "exchangeRate": 0.125547319179561,
                    "progressive": 0.027543826,
                    "seed": 10
                }
            ]
        });

        // confirms win
        events = await jackpotService.confirmWin(cnyPlayer, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance.id,
                    "transactionId": trxId,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                },
            ],
        });

        context = await contextService.find(jpInstance.id, cnyPlayer, trxId);
        expect(context).to.not.exist;
    });

    it("retries winning contribution", async () => {
        jpCheckWin.returns({
            type: "win",
            pool: "small",
        });

        const trxId = await wallet.generateTransactionId();
        const contribution = {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        };
        const expectedContext = {
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "small",
                "type": "win"
            },
            "win": [
                {
                    "type": "player",
                    "pool": "small",
                    "amount": 10.03,
                    "playerAmount": 10.03,
                    "exchangeRate": 1,
                    "seed": 10,
                    "progressive": 0.03
                }
            ]
        };

        // contributes and wins
        let events = await jackpotService.contribute(auth, contribution);
        const expectedEvents = {
            "events": [
                {
                    "type": "win",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "pool": "small",
                    "title": "friendly name",
                    "amount": 10.03,
                    "progressive": 0.03,
                    "seed": 10
                },
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyJPContribute),
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10,
                            "progressive": 0,
                            "seed": -9.99
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        };
        expect(events).to.deep.equal(expectedEvents);
        let context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.deep.equal(expectedContext);

        const winTrx = await getLastTransaction();
        const contributeTrx = await getLastTransaction();
        walletFindCommitted.onFirstCall().returns(contributeTrx);
        walletFindCommitted.onSecondCall().returns(winTrx);

        // retries
        events = await jackpotService.contribute(auth, contribution);
        expect(events).to.deep.equal(expectedEvents);
        context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.deep.equal(expectedContext);
    });

    it("contributes and starts mini game", async () => {
        jpCheckWin.returns({
            type: "start-mini-game",
        });

        jpMiniGame.returns({
            type: "win",
            pool: "medium",
        });

        const trxId = await wallet.generateTransactionId();

        // contribute
        let events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "gameData": undefined,
                    "type": "start-mini-game",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
                {
                    "jackpotId": jpInstance.id,
                    "contributions": getSpiedPlayerContributions(spyJPContribute),
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10.03,
                            "progressive": 0.03,
                            "seed": 0.01
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        });

        let context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 1,
            "miniGame": {},
            "result": {
                "type": "start-mini-game"
            },
            "version": CURRENT_JACKPOT_CONTEXT_VERSION
        });

        // update mini game
        events = await jackpotService.updateMiniGame(auth, {
            transactionId: trxId,
            jackpotId: jpInstance.id,
            roundId: "1"
        });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "amount": 100.04,
                    "progressive": 0.04,
                    "seed": 100,
                    "pool": "medium",
                    "title": "medium",
                },
                {
                    "type": "end-mini-game",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "medium",
                "type": "win"
            },
            "win": [
                {
                    "type": "player",
                    "pool": "medium",
                    "amount": 100.04,
                    "playerAmount": 100.04,
                    "exchangeRate": 1,
                    "progressive": 0.04,
                    "seed": 100
                }
            ],
        });

        // confirms win
        events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.not.exist;

        // jackpot updated
        const ticker = await jackpotService.getTicker(auth, { exchangeRates: { "EUR": 1 } });
        expect(ticker).to.deep.equal([
            {
                "jackpotId": jpInstance.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": -99.98
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
            }
        ]);
    });

    it("contributes and starts mini game with gameData", async () => {
        jpCheckWin.returns({
            type: "start-mini-game",
            gameData: "abstract game data",
        });

        jpMiniGame.returns({
            type: "win",
            pool: "medium",
        });

        const trxId = await wallet.generateTransactionId();

        // contribute
        const events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "gameData": "abstract game data",
                    "type": "start-mini-game",
                    "jackpotId": jpInstance.id,
                    "transactionId": trxId,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                },
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyJPContribute),
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10.03,
                            "progressive": 0.03,
                            "seed": 0.01
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        });
    });

    it("retries to update mini game", async () => {
        jpCheckWin.returns({
            type: "start-mini-game",
        });

        jpMiniGame.returns({
            type: "win",
            pool: "medium",
        });

        const trxId = await wallet.generateTransactionId();

        // contribute
        let events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "gameData": undefined,
                    "type": "start-mini-game",
                    "jackpotId": jpInstance.id,
                    "transactionId": trxId,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                },
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyJPContribute),
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10.03,
                            "progressive": 0.03,
                            "seed": 0.01
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        });

        let context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 1,
            "miniGame": {},
            "result": {
                "type": "start-mini-game"
            },
            "version": CURRENT_JACKPOT_CONTEXT_VERSION
        });

        // update mini game
        const expectedMGEvents = {
            "events": [
                {
                    "type": "win",
                    "jackpotId": jpInstance.id,
                    "transactionId": trxId,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "amount": 100.04,
                    "progressive": 0.04,
                    "seed": 100,
                    "pool": "medium",
                    "title": "medium",
                },
                {
                    "type": "end-mini-game",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        };
        const expectedContext = {
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "type": "win",
                "pool": "medium"
            },
            "win": [
                {
                    "type": "player",
                    "pool": "medium",
                    "amount": 100.04,
                    "playerAmount": 100.04,
                    "exchangeRate": 1,
                    "progressive": 0.04,
                    "seed": 100
                }
            ],
        };

        // update #1
        events = await jackpotService.updateMiniGame(auth, {
            transactionId: trxId,
            jackpotId: jpInstance.id,
            roundId: "1"
        });
        expect(events).to.deep.equal(expectedMGEvents);
        context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.deep.equal(expectedContext);

        const winTrx = await getLastTransaction();
        walletFindCommitted.returns(winTrx);

        // update #2
        events = await jackpotService.updateMiniGame(auth, {
            transactionId: trxId,
            jackpotId: jpInstance.id,
            roundId: "1"
        });
        expect(events).to.deep.equal(expectedMGEvents);
        context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.deep.equal(expectedContext);

        // confirms win
        events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.not.exist;

        // jackpot updated
        const ticker = await jackpotService.getTicker(auth, { exchangeRates: { "EUR": 1 } });
        expect(ticker).to.deep.equal([
            {
                "jackpotId": jpInstance.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": -99.98
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
            }
        ]);
    });

    it("retries contribution and cannot win", async () => {
        jpCheckWin.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        const contribution = {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        };

        const events1 = await jackpotService.contribute(auth, contribution);
        const expectedEvents = {
            "events": [
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyJPContribute),
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10.03,
                            "progressive": 0.03,
                            "seed": 0.01
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        };

        expect(events1).to.deep.equal(expectedEvents);

        const lastTrx = await getLastTransaction();
        walletFindCommitted.returns(lastTrx);

        const events2 = await jackpotService.contribute(auth, contribution);
        expect(events2).to.deep.equal(expectedEvents);
    });

    it("cannot win if contribution is zero", async () => {
        const omrPlayer: AuthRequest = {
            playerCode: "PL-CODE",
            brandId: 1,
            region: "eu",
            jackpotIds: [jpInstance.id],
            currency: "OMR",
            gameCode: "test"
        };

        const trxId = await wallet.generateTransactionId();
        const contribution = {
            externalId: "123",
            transactionId: trxId,
            amount: 0,
            roundId: "1"
        };

        const events = await jackpotService.contribute(omrPlayer, contribution);
        const expectedEvents = {
            "events": [
                {
                    "jackpotId": jpInstance.id,
                    "contributions": getSpiedPlayerContributions(spyJPContribute),
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "large": {
                            "amount": 441.38,
                            "progressive": 0,
                            "seed": 0
                        },
                        "medium": {
                            "amount": 44.138,
                            "progressive": 0,
                            "seed": 0
                        },
                        "small": {
                            "amount": 4.413,
                            "progressive": 0,
                            "seed": 0
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0
                },
            ],
        };
        expect(events).to.deep.equal(expectedEvents);

        // verify we skipped jackpot win probability check
        expect(jpCheckWin.notCalled).to.be.true;
    });

    it("win jackpot without amount and with custom exchange rate", async () => {
        const trxId = await wallet.generateTransactionId();
        jpWinJackpot.returns({
            type: "win",
            pool: "small",
        });

        const exchangeRate = 1 / 2;
        const winJPRequest: WinJackpotRequest = {
            transactionId: trxId,
            exchangeRates: { "EUR": exchangeRate },
            externalId: "123",
            jackpotId: jpInstance.id,
            roundId: "1"
        };

        const events = await jackpotService.winJackpot(cnyPlayer, winJPRequest);

        expect(events).deep.equal({
            "events": [
                {
                    "amount": JP_POOL_SMALL_AMOUNT / exchangeRate,
                    "jackpotId": "JP-TEST",
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pool": "small",
                    "title": "friendly name",
                    "transactionId": trxId,
                    "type": "win",
                    "progressive": 0,
                    "seed": 20
                },
            ],
        });
    });

    it("win jackpot without amount and without custom exchange rate", async () => {
        const trxId = await wallet.generateTransactionId();
        jpWinJackpot.returns({
            type: "win",
            pool: "small",
        });

        const winJPRequest: WinJackpotRequest = {
            transactionId: trxId,
            externalId: "123",
            jackpotId: jpInstance.id,
            roundId: "1"
        };

        const events = await jackpotService.winJackpot(cnyPlayer, winJPRequest);

        expect(events).deep.equal({
            "events": [
                {
                    "amount": 79.65,
                    "jackpotId": "JP-TEST",
                    "pool": "small",
                    "title": "friendly name",
                    "transactionId": trxId,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "type": "win",
                    "progressive": 0,
                    "seed": 79.65124277721728
                },
            ],
        });
    });

    it("contribute with custom exchange rate", async () => {
        jpCheckWin.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        const exchangeRate = 1 / 2;
        const amount = 10;
        const events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: amount,
            exchangeRates: { "EUR": exchangeRate },
            roundId: "1"
        });

        const progressiveVal = JP_POOL_SMALL_PROGRESSIVE_PCT / 100;
        const expectedSmallPool = (JP_POOL_SMALL_AMOUNT + (amount * exchangeRate) * progressiveVal) / exchangeRate;

        expect(events).to.deep.equal({
            "events": [
                {
                    "jackpotId": "JP-TEST",
                    "contributions": getSpiedPlayerContributions(spyJPContribute,
                        exchangeRate),
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "large": {
                            "amount": 2000.07,
                            "progressive": 0.035,
                            "seed": 0.015
                        },
                        "medium": {
                            "amount": 200.04,
                            "progressive": 0.02,
                            "seed": 0.01
                        },
                        "small": {
                            "amount": expectedSmallPool,
                            "progressive": 0.015,
                            "seed": 0.005
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        });

        const context = await contextService.find(jpInstance.id, auth, "123");
        expect(context).to.not.exist;
    });

    it("contribute with custom exchange rate, check jackpot state", async () => {
        jpCheckWin.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        const exchangeRate = 1 / 2;
        const amount = 10;
        const events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: amount,
            exchangeRates: { "EUR": exchangeRate },
            roundId: "1"
        });

        const progressiveVal = JP_POOL_SMALL_PROGRESSIVE_PCT / 100;
        const expectedSmallPool = (JP_POOL_SMALL_AMOUNT + (amount * exchangeRate) * progressiveVal) / exchangeRate;

        expect(events).to.deep.equal({
            "events": [
                {
                    "jackpotId": "JP-TEST",
                    "contributions": getSpiedPlayerContributions(spyJPContribute,
                        exchangeRate),
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "large": {
                            "amount": 2000.07,
                            "progressive": 0.035,
                            "seed": 0.015
                        },
                        "medium": {
                            "amount": 200.04,
                            "progressive": 0.02,
                            "seed": 0.01
                        },
                        "small": {
                            "amount": expectedSmallPool,
                            "progressive": 0.015,
                            "seed": 0.005
                        },
                    },
                    "type": "contribution",
                    "totalContribution": (0.02 + 0.03 + 0.05) / exchangeRate
                },
            ],
        });

        const context = await contextService.find(jpInstance.id, auth, "123");
        expect(context).to.not.exist;

        // Get jackpot ticker in Jackpot currency
        const jackpotModule = new JackpotWallet(walletParams, jpInstance, exchangeRate, wallet);
        const jpInfo = await jackpotModule.getTicker();
        expect(jpInfo).deep.equal({
            "currency": "EUR",
            "id": "JP-TEST",
            "pools": {
                "large": {
                    "progressive": 0.035,
                    "seed": 0.015,
                },
                "medium": {
                    "progressive": 0.02,
                    "seed": 0.01,
                },
                "small": {
                    "progressive": 0.015,
                    "seed": amount * exchangeRate / 1000,
                },
            },
            "seqId": 1
        });
    });

    it("win jackpot with broken context", async () => {
        const trxId = await wallet.generateTransactionId();
        jpWinJackpot.returns({
            type: "win",
            pool: "small",
        });
        const winJPRequest: WinJackpotRequest = {
            transactionId: trxId,
            externalId: "123",
            jackpotId: jpInstance.id,
            roundId: "1"
        };

        const brokenContext: JackpotContext = {
            jackpotId: jpInstance.id,
            transactionId: trxId,
            playerInfo: cnyPlayer,
            roundId: "1",
            result: {
                type: "win",
                pool: "small"
            } as any,
            win: {
                type: JackpotWinType.PLAYER,
                pool: "small",
                amount: 10,
                seed: 10,
                progressive: 0,
                playerAmount: 73.1,
                exchangeRate: 0.13679572663771186,
            } as JackpotPlayerWin,
            status: JackpotStatus.WON,
            version: CURRENT_JACKPOT_CONTEXT_VERSION
        };

        await contextService.update(brokenContext);

        const events = await jackpotService.winJackpot(cnyPlayer, winJPRequest);

        expect(events).deep.equal({
            "events": [
                {
                    "amount": Math.round((JP_POOL_SMALL_AMOUNT / 0.1368) * 100) / 100,
                    "jackpotId": "JP-TEST",
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pool": "small",
                    "title": "friendly name",
                    "transactionId": trxId,
                    "type": "win",
                    "progressive": 0,
                    "seed": 73.10169875762186
                },
            ],
        });
    });

    it("win jackpot with broken context of old version", async () => {
        const trxId = await wallet.generateTransactionId();
        jpWinJackpot.returns({
            type: "win",
            pool: "small",
        });
        const winJPRequest: WinJackpotRequest = {
            transactionId: trxId,
            externalId: "123",
            jackpotId: jpInstance.id,
            roundId: "1"
        };

        const brokenContext: JackpotContext = {
            jackpotId: jpInstance.id,
            transactionId: trxId,
            playerInfo: cnyPlayer,
            roundId: "1",
            win: {
                pool: "small",
                amount: 10,
                playerAmount: 73.1,
                exchangeRate: 0.13679572663771186,
            },
            status: JackpotStatus.WON
        } as any;

        await contextService.update(brokenContext);

        const events = await jackpotService.winJackpot(cnyPlayer, winJPRequest);

        expect(events).deep.equal({
            "events": [
                {
                    "amount": Math.round((JP_POOL_SMALL_AMOUNT / 0.1368) * 100) / 100,
                    "jackpotId": "JP-TEST",
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pool": "small",
                    "title": "friendly name",
                    "transactionId": trxId,
                    "type": "win",
                    "progressive": 0,
                    "seed": 73.10169875762186
                },
            ],
        });
    });

    it("win jackpot with broken context and concurrent jackpot win", async () => {
        const trxId = await wallet.generateTransactionId();
        jpWinJackpot.returns({
            type: "win",
            pool: "small",
        });
        const winJPRequest: WinJackpotRequest = {
            transactionId: trxId,
            externalId: "123",
            jackpotId: jpInstance.id,
            roundId: "1",
        };

        const brokenContext: JackpotContext = {
            jackpotId: jpInstance.id,
            transactionId: trxId,
            playerInfo: cnyPlayer,
            roundId: "1",
            result: {
                type: "win",
                pool: "small"
            } as any,
            win: {
                type: JackpotWinType.PLAYER,
                pool: "small",
                amount: 11.13,
                seed: 10,
                progressive: 1.13,
                playerAmount: 73.1,
                exchangeRate: 0.13679572663771186,
            } as JackpotPlayerWin,
            status: JackpotStatus.WON,
            version: CURRENT_JACKPOT_CONTEXT_VERSION
        };

        await contextService.update(brokenContext);

        const events = await jackpotService.winJackpot(cnyPlayer, winJPRequest);

        expect(events).deep.equal({
            "events": [
                {
                    "amount": Math.round((JP_POOL_SMALL_AMOUNT / 0.1368) * 100) / 100,
                    "jackpotId": "JP-TEST",
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pool": "small",
                    "title": "friendly name",
                    "transactionId": trxId,
                    "type": "win",
                    "progressive": 0,
                    "seed": 73.10169875762186
                },
            ],
        });
    });

    it("handles invalid jackpot result", async () => {
        jpCheckWin.returns([
            {
                type: "win",
                pool: "small"
            }, {
                type: "start-mini-game",
            }
        ]);

        const trxId = await wallet.generateTransactionId();

        await expect(jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        })).to.be.rejectedWith(Errors.InvalidJackpotResult);
    });

    it("deferred contributes", async () => {
        jpCheckWin.returns(undefined);

        const result = await jackpotService.deferredContribute(auth, {
            externalId: "123",
            transactionId: undefined,
            amount: 10,
            roundId: "1"
        });

        expect((JWT.decode(result.result) as any).payload).to.deep.equals([
            {
                "contributions": [
                    {
                        "pool": "small",
                        "progressive": 0.03,
                        "seed": 0.01,
                    },
                    {
                        "pool": "medium",
                        "progressive": 0.04,
                        "seed": 0.02,
                    },
                    {
                        "pool": "large",
                        "progressive": 0.07,
                        "seed": 0.03,
                    }
                ],
                "jackpotId": "JP-TEST",
                "playerContributions": [
                    {
                        "pool": "small",
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                    {
                        "pool": "medium",
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    {
                        "pool": "large",
                        "progressive": 0.07,
                        "seed": 0.03,
                    }
                ],
                "totalPlayerContribution": 0.2
            }
        ]);

        const context = await contextService.find(jpInstance.id, auth, result.transactionId);
        expect(context).to.not.exist;
    });

    it("deferred contribution with deferredWinsEnabled = true", async () => {
        jpCheckWin.returns(undefined);

        const result = await jackpotService.deferredContribute(auth, {
            externalId: "123",
            transactionId: undefined,
            amount: 10,
            roundId: "1",
            deferredWinsEnabled: true
        });

        expect((JWT.decode(result.result) as any).payload).to.deep.equals([
            {
                "contributions": [
                    {
                        "pool": "small",
                        "progressive": 0.03,
                        "seed": 0.01,
                    },
                    {
                        "pool": "medium",
                        "progressive": 0.04,
                        "seed": 0.02,
                    },
                    {
                        "pool": "large",
                        "progressive": 0.07,
                        "seed": 0.03,
                    }
                ],
                "jackpotId": "JP-TEST",
                "playerContributions": [
                    {
                        "pool": "small",
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                    {
                        "pool": "medium",
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    {
                        "pool": "large",
                        "progressive": 0.07,
                        "seed": 0.03,
                    }
                ],
                "totalPlayerContribution": 0.2
            }
        ]);

        const context = await contextService.find(jpInstance.id, auth, result.transactionId);
        expect(context).to.not.exist;
    });

    it("deferred contribution with deferredWinsEnabled = true - JP Win", async () => {
        jpCheckWin.returns({
            type: "win",
            pool: "small"
        });

        const result = await jackpotService.deferredContribute(auth, {
            externalId: "123",
            transactionId: undefined,
            amount: 10,
            roundId: "1",
            deferredWinsEnabled: true
        });
        const expectedResult = (JWT.decode(result.result) as any).payload;
        delete expectedResult[0].events[0].transactionId;
        expect(expectedResult).to.deep.equals([
            {
                "contributions": [
                    {
                        "pool": "small",
                        "progressive": 0.03,
                        "seed": 0.01,
                    },
                    {
                        "pool": "medium",
                        "progressive": 0.04,
                        "seed": 0.02,
                    },
                    {
                        "pool": "large",
                        "progressive": 0.07,
                        "seed": 0.03,
                    }
                ],
                "jackpotId": "JP-TEST",
                "playerContributions": [
                    {
                        "pool": "small",
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                    {
                        "pool": "medium",
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    {
                        "pool": "large",
                        "progressive": 0.07,
                        "seed": 0.03,
                    }
                ],
                "totalPlayerContribution": 0.2,
                "gameResult": {
                    "pool": "small",
                    "type": "win"
                },
                "events": [
                    {
                        "amount": 10.03,
                        "jackpotBaseType": "base",
                        "jackpotId": "JP-TEST",
                        "jackpotType": "test",
                        "pool": "small",
                        "progressive": 0.03,
                        "seed": 10,
                        "status": "pending",
                        "title": "friendly name",
                        "type": "win"
                    },
                    {
                        "contributions": [
                            {
                                "pool": "small",
                                "progressive": 0.03,
                                "seed": 0.01
                            },
                            {
                                "pool": "medium",
                                "progressive": 0.04,
                                "seed": 0.02
                            },
                            {
                                "pool": "large",
                                "progressive": 0.07,
                                "seed": 0.03
                            }
                        ],
                        "jackpotBaseType": "base",
                        "jackpotId": "JP-TEST",
                        "jackpotType": "test",
                        "pools": {
                            "large": {
                                "amount": 1000.07,
                                "progressive": 0.07,
                                "seed": 0.03
                            },
                            "medium": {
                                "amount": 100.04,
                                "progressive": 0.04,
                                "seed": 0.02
                            },
                            "small": {
                                "amount": 10,
                                "progressive": 0,
                                "seed": -9.99
                            }
                        },
                        "status": "pending",
                        "totalContribution": 0.2,
                        "type": "contribution"
                    }
                ]
            }
        ]);
    });

    it("deferred contribute and continue", async () => {
        jpCheckWin.returns(undefined);

        const result = await jackpotService.deferredContribute(auth, {
            transactionId: undefined,
            amount: 10,
            roundId: "1"
        });

        expect((JWT.decode(result.result) as any).payload).to.deep.equals([
            {
                "contributions": [
                    {
                        "pool": "small",
                        "progressive": 0.03,
                        "seed": 0.01,
                    },
                    {
                        "pool": "medium",
                        "progressive": 0.04,
                        "seed": 0.02,
                    },
                    {
                        "pool": "large",
                        "progressive": 0.07,
                        "seed": 0.03,
                    }
                ],
                "jackpotId": "JP-TEST",
                "playerContributions": [
                    {
                        "pool": "small",
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                    {
                        "pool": "medium",
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    {
                        "pool": "large",
                        "progressive": 0.07,
                        "seed": 0.03,
                    }
                ],
                "totalPlayerContribution": 0.2
            }
        ]);

        const context = await contextService.find(jpInstance.id, auth, result.transactionId);
        expect(context).to.not.exist;

        const state1 = await jackpotService.getAllPoolsState(auth);
        expect(state1).to.deep.equals([
            {
                "initialSeed": 10,
                "poolId": "small",
                "progressive": 0,
                "seed": 0,
            },
            {
                "initialSeed": 100,
                "poolId": "medium",
                "progressive": 0,
                "seed": 0
            },
            {
                "initialSeed": 1000,
                "poolId": "large",
                "progressive": 0,
                "seed": 0,
            }
        ]);

        const finalizeContribResult = await jackpotService.continueDeferredContribute(auth, {
            transactionId: result.transactionId,
            roundId: "1",
            deferredContribution: result.result,
        } as ContributionRequest);

        expect(finalizeContribResult).to.deep.equals({
            "events": [
                {
                    "contributions": [
                        {
                            "pool": "small",
                            "progressive": 0.03,
                            "seed": 0.01,
                        },
                        {
                            "pool": "medium",
                            "progressive": 0.04,
                            "seed": 0.02,
                        },
                        {
                            "pool": "large",
                            "progressive": 0.07,
                            "seed": 0.03
                        }
                    ],
                    "jackpotId": "JP-TEST",
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03,
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10.03,
                            "progressive": 0.03,
                            "seed": 0.01
                        }
                    },
                    "totalContribution": 0.2,
                    "type": "contribution"
                }
            ]
        });

        const state2 = await jackpotService.getAllPoolsState(auth);
        expect(state2).to.deep.equals([
            {
                "initialSeed": 10,
                "poolId": "small",
                "progressive": 0.03,
                "seed": 0.01
            },
            {
                "initialSeed": 100,
                "poolId": "medium",
                "progressive": 0.04,
                "seed": 0.02
            },
            {
                "initialSeed": 1000,
                "poolId": "large",
                "progressive": 0.07,
                "seed": 0.03,
            }
        ]);
    });

    it("continue deferred contributes with jp win", async () => {
        jpCheckWin.returns([
            {
                type: "win",
                pool: "small"
            }
        ]);

        const result = await jackpotService.deferredContribute(auth, {
            transactionId: undefined,
            amount: 10,
            roundId: "1"
        });

        expect((JWT.decode(result.result) as any).payload).to.deep.equals([
            {
                "contributions": [
                    {
                        "pool": "small",
                        "progressive": 0.03,
                        "seed": 0.01,
                    },
                    {
                        "pool": "medium",
                        "progressive": 0.04,
                        "seed": 0.02,
                    },
                    {
                        "pool": "large",
                        "progressive": 0.07,
                        "seed": 0.03,
                    }
                ],
                "jackpotId": "JP-TEST",
                "playerContributions": [
                    {
                        "pool": "small",
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                    {
                        "pool": "medium",
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    {
                        "pool": "large",
                        "progressive": 0.07,
                        "seed": 0.03,
                    }
                ],
                "totalPlayerContribution": 0.2,
                "gameResult": [
                    {
                        "pool": "small",
                        "type": "win",
                    }
                ]
            }
        ]);

        const context = await contextService.find(jpInstance.id, auth, result.transactionId);
        expect(context).to.not.exist;

        const state1 = await jackpotService.getAllPoolsState(auth);
        expect(state1).to.deep.equals([
            {
                "initialSeed": 10,
                "poolId": "small",
                "progressive": 0,
                "seed": 0,
            },
            {
                "initialSeed": 100,
                "poolId": "medium",
                "progressive": 0,
                "seed": 0
            },
            {
                "initialSeed": 1000,
                "poolId": "large",
                "progressive": 0,
                "seed": 0,
            }
        ]);

        const finalizeContribResult = await jackpotService.continueDeferredContribute(auth, {
            transactionId: result.transactionId,
            roundId: "1",
            deferredContribution: result.result,
        } as ContributionRequest);

        expect(finalizeContribResult).to.deep.equals({
            "events": [
                {
                    "amount": 10.03,
                    "progressive": 0.03,
                    "seed": 10,
                    "jackpotId": "JP-TEST",
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pool": "small",
                    "title": "friendly name",
                    "transactionId": result.transactionId,
                    "type": "win"
                },
                {
                    "contributions": [
                        {
                            "pool": "small",
                            "progressive": 0.03,
                            "seed": 0.01,
                        },
                        {
                            "pool": "medium",
                            "progressive": 0.04,
                            "seed": 0.02,
                        },
                        {
                            "pool": "large",
                            "progressive": 0.07,
                            "seed": 0.03
                        }
                    ],
                    "jackpotId": "JP-TEST",
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03,
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10,
                            "progressive": 0,
                            "seed": 0.01 - 10
                        }
                    },
                    "totalContribution": 0.2,
                    "type": "contribution"
                }
            ]
        });

        const state2 = await jackpotService.getAllPoolsState(auth);
        expect(state2).to.deep.equals([
            {
                "initialSeed": 10,
                "poolId": "small",
                "progressive": 0.0,
                "seed": -9.99
            },
            {
                "initialSeed": 100,
                "poolId": "medium",
                "progressive": 0.04,
                "seed": 0.02
            },
            {
                "initialSeed": 1000,
                "poolId": "large",
                "progressive": 0.07,
                "seed": 0.03,
            }
        ]);
    });

    describe("with virtual seed", () => {

        const instanceWithVirtualSeed = JP_WITH_VIRTUAL_SEED;
        const vsAuth: AuthRequest = {
            ...playerInfo,
            jackpotIds: [instanceWithVirtualSeed.id]
        };

        beforeEach(async () => {
            lookup.returns(instanceWithVirtualSeed);
        });

        it("gets ticker with virtual seed", async () => {
            const ticker = await jackpotService.getTicker(vsAuth, {}, { withVirtualSeed: true, withPots: true });
            expect(ticker).to.deep.equal([
                {
                    "jackpotId": instanceWithVirtualSeed.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "main": {
                            "amount": 500,
                            "progressive": 0,
                            "seed": 0
                        }
                    },
                }
            ]);
        });

        it("gets ticker with virtual seed in different currency - from paytable", async () => {
            const ticker = await jackpotService.getTicker({ ...vsAuth, currency: "USD" }, {},
                { withVirtualSeed: true, withPots: true });
            expect(ticker).to.deep.equal([
                {
                    "jackpotId": instanceWithVirtualSeed.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "main": {
                            "amount": 600,
                            "progressive": 0,
                            "seed": 0
                        }
                    },
                }
            ]);
        });

        it("gets ticker with virtual seed in different currency - from conversion", async () => {
            const ticker = await jackpotService.getTicker({ ...vsAuth, currency: "BYN" }, {},
                { withVirtualSeed: true, withPots: true });
            expect(ticker).to.deep.equal([
                {
                    "jackpotId": instanceWithVirtualSeed.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "main": {
                            "amount": 1202.69,
                            "progressive": 0,
                            "seed": 0
                        }
                    },
                }
            ]);
        });

        it("gets ticker", async () => {
            const ticker = await jackpotService.getTicker(vsAuth, {}, { withPots: true });
            expect(ticker).to.deep.equal([
                {
                    "jackpotId": instanceWithVirtualSeed.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "main": {
                            "amount": 0,
                            "progressive": 0,
                            "seed": 0
                        }
                    },
                }
            ]);
        });

        it("contributes", async () => {
            jpCheckWin.returns(undefined);

            const trxId = await wallet.generateTransactionId();
            const events = await jackpotService.contribute(vsAuth, {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1"
            });

            expect(events).to.deep.equal({
                "events": [
                    {
                        "jackpotId": instanceWithVirtualSeed.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "contributions": getSpiedPlayerContributions(spyJPContribute),
                        "pools": {
                            "main": {
                                "amount": 0.05,
                                "progressive": 0.05,
                                "seed": 0
                            }
                        },
                        "type": "contribution",
                        "totalContribution": 0.05
                    },
                ],
            });

            const context = await contextService.find(jpInstance.id, auth, "123");
            expect(context).to.not.exist;
        });
    });
});
