import { expect, use } from "chai";
import {
    createTestJPGame,
    flush, flushDb,
    getSpiedPlayerContributions,
    JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT,
} from "../helpers";
import { SinonSpy, SinonStub, spy, stub } from "sinon";
import JackpotInstanceService from "../../skywind/services/jackpot.instance";
import * as gameService from "../../skywind/services/game.service";
import { JackpotService } from "../../skywind/services/jackpot.service";
import { AuthRequest } from "@skywind-group/sw-jpn-core";
import wallet, { WalletServiceImpl } from "../../skywind/services/wallet.service";
import contextService from "../../skywind/services/jackpot.context";
import { init as initCurrencyRates } from "../../skywind/services/currency.service";
import { JackpotInternalInstance, JackpotRegion } from "../../skywind/api/model";
import JackpotRegionService from "../../skywind/services/regionService";
import { createRemoteServices, RemoteJackpotModule } from "../../skywind/modules/remoteJackpotModule";
import * as request from "request";
import { CURRENT_JACKPOT_CONTEXT_VERSION } from "../../definition";

use(require("chai-as-promised"));

describe("Jackpot Service With Remote JPN", () => {

    const region: JackpotRegion = {
        code: "eu",
        url: "http://jpn.eu",
        secureOptions: { ca: "rootCA" }
    };
    const jpInstance: JackpotInternalInstance = {
        ...JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT,
        regionCode: region.code,
        region
    };
    const playerInfo: AuthRequest = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        jackpotIds: [jpInstance.id],
        currency: "EUR",
        gameCode: "test"
    };
    let jpGame;
    const jackpotService: JackpotService = new JackpotService(JackpotInstanceService, wallet, contextService);

    let lookup: SinonStub;
    let load: SinonStub;
    let remoteGet: SinonStub;
    let remotePost: SinonStub;
    let jpCheckWin: SinonStub;
    let jpWinMiniGame: SinonStub;
    let jpWinJackpot: SinonStub;
    let spyJPContribute: SinonSpy;
    let findTransaction: SinonStub;

    const remoteTicker = {
        id: "JP-TEST",
        currency: "EUR",
        pools:
            {
                small: { seed: 0, progressive: 0 },
                medium: { seed: 0, progressive: 0 },
                large: { seed: 0, progressive: 0 }
            },
        seqId: 2
    };
    const remoteServices = createRemoteServices(JackpotInstanceService);

    before(async () => {
        await flushDb();
        await JackpotRegionService.create(region);
        lookup = stub(JackpotInstanceService, "findInternal");
        load = stub(gameService, "load");
        remoteGet = stub(request, "get");
        remotePost = stub(request, "post");
        findTransaction = stub(WalletServiceImpl.prototype, "findCommittedTransaction");
        await initCurrencyRates();
    });

    beforeEach(async () => {
        await flush();

        const jpModule = new RemoteJackpotModule(playerInfo, jpInstance, 1, wallet, remoteServices);
        jpGame = await createTestJPGame(jpModule);
        spyJPContribute = spy(jpGame, "getContributions");

        jpCheckWin = stub(jpGame, "checkWin");
        jpWinJackpot = stub(jpGame, "winJackpot");
        jpWinMiniGame = stub(jpGame, "winMiniGame");

        lookup.resetBehavior();
        lookup.returns(jpInstance);
        load.resetBehavior();
        load.returns(jpGame);
        remoteGet.resetHistory();
        remotePost.resetHistory();
        jpCheckWin.resetHistory();
        jpWinJackpot.resetHistory();
        jpWinMiniGame.resetHistory();
        findTransaction.resetHistory();
    });

    after(() => {
        lookup.restore();
        load.restore();
        jpCheckWin.restore();
        jpWinJackpot.restore();
        remoteGet.restore();
        remotePost.restore();
        findTransaction.restore();
    });

    it("gets ticker", async () => {
        remoteGet.yields(null, { statusCode: 200 }, remoteTicker);

        const ticker = await jackpotService.getTicker(playerInfo, {});
        expect(ticker).to.deep.equal([{
                "jackpotId": jpInstance.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": 0
                    },
                },
        }]);
    });

    it("gets ticker from disabled jackpot", async () => {
        const disabledTicker = {
            ...remoteTicker,
            isDisabled: true
        };
        remoteGet.yields(null, { statusCode: 200 }, disabledTicker);

        const ticker = await jackpotService.getTicker(playerInfo, {});
        expect(ticker).to.deep.equal([{
                "jackpotId": jpInstance.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": 0
                    },
                },
                "isDisabled": true
        }]);
    });

    it("contributes and wins jackpot", async () => {
        jpCheckWin.returns({
            type: "win",
            pool: "small",
        });

        remoteGet.onFirstCall().yields(null, { statusCode: 200 }, remoteTicker);

        const remoteTrxId = await wallet.generateTransactionId();
        remoteGet.onSecondCall().yields(null, { statusCode: 200 }, { transactionIds: [remoteTrxId] });

        const updatedRemoteTicker = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0.02, progressive: 0.04 },
                    large: { seed: 0.03, progressive: 0.07 }
                },
            seqId: 3
        };

        remotePost.onFirstCall().yields(null, { statusCode: 200 },
            { results: [
                    {
                        contributions: [
                            {
                                pool: "small",
                                progressive: 0.03,
                                seed: 0.01
                            },
                            {
                                pool: "medium",
                                progressive: 0.04,
                                seed: 0.02
                            },
                            {
                                pool: "large",
                                progressive: 0.07,
                                seed: 0.03
                            }
                        ],
                        playerContributions: [
                            {
                                pool: "small",
                                progressive: 0.03,
                                seed: 0.01
                            },
                            {
                                pool: "medium",
                                progressive: 0.04,
                                seed: 0.02
                            },
                            {
                                pool: "large",
                                progressive: 0.07,
                                seed: 0.03
                            }
                        ],
                        gameResult: { type: "win", pool: "small" },
                        wins: [
                            {
                                type: "player",
                                pool: "small",
                                amount: 10.03,
                                playerAmount: 10.03,
                                exchangeRate: 1,
                                seed: 10,
                                progressive: 0.03,
                                fixedAmount: undefined
                            }
                        ],
                    winPayouts: [{
                                pool: "small",
                                winAmount: 10.03,
                                currencyRate: 1,
                                initialSeed: 10,
                                seed: 10,
                                progressive: 0.03,
                                totalSeed: 0,
                                totalProgressive: 0,
                                seedSinceLastWin: 0,
                                progressiveSinceLastWin: 0
                    }],
                        ticker: updatedRemoteTicker
                }]
            });

        remotePost.onSecondCall().yields(null, { statusCode: 200 },
            { results: [
                    {
                        contributions: [
                            {
                                pool: "small",
                                progressive: 0.03,
                                seed: 0.01
                            },
                            {
                                pool: "medium",
                                progressive: 0.04,
                                seed: 0.02
                            },
                            {
                                pool: "large",
                                progressive: 0.07,
                                seed: 0.03
                            }
                        ],
                        playerContributions: [
                            {
                                pool: "small",
                                progressive: 0.03,
                                seed: 0.01
                            },
                            {
                                pool: "medium",
                                progressive: 0.04,
                                seed: 0.02
                            },
                            {
                                pool: "large",
                                progressive: 0.07,
                                seed: 0.03
                            }
                        ],
                        gameResult: { type: "win", pool: "small" },
                        wins: [
                            {
                                type: "player",
                                pool: "small",
                                amount: 10.03,
                                playerAmount: 10.03,
                                exchangeRate: 1,
                                seed: 10,
                                progressive: 0.03,
                                fixedAmount: undefined
                            }
                        ],
                    winPayouts: [{
                                pool: "small",
                                winAmount: 10.03,
                                currencyRate: 1,
                                initialSeed: 10,
                                seed: 10,
                                progressive: 0.03,
                                totalSeed: 0,
                                totalProgressive: 0,
                                seedSinceLastWin: 0,
                                progressiveSinceLastWin: 0
                    }],
                        ticker: updatedRemoteTicker
                }]
            });

        // contributes and wins
        const trxId = await wallet.generateTransactionId();
        let events = await jackpotService.contribute(playerInfo, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "pool": "small",
                    "title": "small",
                    "amount": 10.03,
                    "progressive": 0.03,
                    "seed": 10
                },
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyJPContribute),
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10,
                            "progressive": 0,
                            "seed": 0
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        });

        let context = await contextService.find(jpInstance.id, playerInfo, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "small",
                "type": "win"
            },
            "win": [{
                    "type": "player",
                    "pool": "small",
                    "amount": 10.03,
                    "playerAmount": 10.03,
                    "seed": 10,
                    "progressive": 0.03,
                    "exchangeRate": 1,
            }]
        });

        // confirms win
        events = await jackpotService.confirmWin(playerInfo, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(jpInstance.id, playerInfo, trxId);
        expect(context).to.not.exist;
    });

    it("mini game flow", async () => {
        jpCheckWin.returns({
            type: "start-mini-game"
        });
        jpWinMiniGame.returns({
            type: "win",
            pool: "medium"
        });

        remoteGet.onFirstCall().yields(null, { statusCode: 200 }, remoteTicker);

        const remoteTrxId = await wallet.generateTransactionId();
        const secondRemoteId = await wallet.generateTransactionId();
        remoteGet.onSecondCall().yields(null, { statusCode: 200 }, { transactionIds: [remoteTrxId, secondRemoteId] });

        const updatedRemoteTicker = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0.02, progressive: 0.04 },
                    large: { seed: 0.03, progressive: 0.07 }
                },
            seqId: 3
        };

        remotePost.onFirstCall().yields(null, { statusCode: 200 },
            {
                results: [
                    {
                        contributions: [
                            {
                                pool: "small",
                                progressive: 0.03,
                                seed: 0.01
                            },
                            {
                                pool: "medium",
                                progressive: 0.04,
                                seed: 0.02
                            },
                            {
                                pool: "large",
                                progressive: 0.07,
                                seed: 0.03
                            }
                        ],
                        playerContributions: [
                            {
                                pool: "small",
                                progressive: 0.03,
                                seed: 0.01
                            },
                            {
                                pool: "medium",
                                progressive: 0.04,
                                seed: 0.02
                            },
                            {
                                pool: "large",
                                progressive: 0.07,
                                seed: 0.03
                            }
                        ],
                        ticker: updatedRemoteTicker
                    }
                ]
            });
        remotePost.onSecondCall().yields(null, { statusCode: 200 },
            {
                results: [
                    {
                        gameResult: { type: "win", pool: "medium" },
                        wins: [
                            {
                                type: "player",
                                pool: "medium",
                                amount: 10.03,
                                playerAmount: 10.03,
                                exchangeRate: 1,
                                seed: 10,
                                progressive: 0.03,
                                fixedAmount: undefined
                            }
                        ],
                        winPayouts: [{
                            pool: "medium",
                            winAmount: 10.03,
                            currencyRate: 1,
                            initialSeed: 10,
                            seed: 10,
                            progressive: 0.03,
                            totalSeed: 0,
                            totalProgressive: 0,
                            seedSinceLastWin: 0,
                            progressiveSinceLastWin: 0
                        }],
                        ticker: updatedRemoteTicker
                    }
                ]
            });

        // contributes and triggers mini game
        const trxId = await wallet.generateTransactionId();
        findTransaction.returns({
            "operation": {
                "operationName": "remote-contribute",
                "params": {
                    "gameResult": { "type": "start-mini-game" },
                    "playerContributions": [
                        { "progressive": 0.03, "pool": "small", "seed": 0.01 },
                        { "progressive": 0.04, "pool": "medium", "seed": 0.02 },
                        { "progressive": 0.07, "pool": "large", "seed": 0.03 }
                    ],
                    "gameData": {
                        "roundId": "1",
                        "externalId": "123",
                        "transactionId": "NNU4qgAAAtkAAALbNNU4qlHkHEk=",
                        "amount": 10
                    },
                    "region": "eu",
                    "brandId": 1,
                    "playerCurrency": "EUR",
                    "remoteTrxId": "NNU4qQAAAtsAAALbNNU4qVvRCbs=",
                    "playerCode": "PL-CODE",
                    "remoteTrxRegion": "eu",
                    "initialSeed": { "small": 10, "large": 1000, "medium": 100 },
                    "contributions": [
                        {
                            "progressive": 0.03,
                            "pool": "small",
                            "seed": 0.01,
                            "totalSeed": 0.01,
                            "totalProgressive": 0.03
                        },
                        {
                            "progressive": 0.04,
                            "pool": "medium",
                            "seed": 0.02,
                            "totalSeed": 0.02,
                            "totalProgressive": 0.04
                        },
                        {
                            "progressive": 0.07,
                            "pool": "large",
                            "seed": 0.03,
                            "totalSeed": 0.03,
                            "totalProgressive": 0.07
                        }
                    ],
                    "gameId": "test",
                    "currencyRate": 1,
                    "contributionAmount": 10,
                    "gameCode": "test",
                    "roundId": "1"
                }
            }
        });

        let events = await jackpotService.contribute(playerInfo, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events.events[0]).deep.equal(
            {
                "type": "start-mini-game",
                "gameData": undefined,
                "jackpotId": jpInstance.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "transactionId": trxId
            }
        );

        let context = await contextService.find(jpInstance.id, playerInfo, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "miniGame": {},
            "status": 1,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "type": "start-mini-game"
            }
        });

        // update mini game
        events = await jackpotService.updateMiniGame(playerInfo,
            { transactionId: trxId, jackpotId: jpInstance.id, roundId: "11" });
        expect(remotePost.args[1][0].json.results[0].gameResult).deep.equal({
            type: "win",
            pool: "medium"
        });
        expect(events).to.deep.equal({
            "events": [
                {
                    "amount": 10.03,
                    "progressive": 0.03,
                    "seed": 10,
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pool": "medium",
                    "title": "medium",
                    "transactionId": trxId,
                    "type": "win"
                },
                {
                    "type": "end-mini-game",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                }
            ],
        });

        context = await contextService.find(jpInstance.id, playerInfo, trxId);
        expect(context).deep.equal({
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "11",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "medium",
                "type": "win"
            },
            "win": [{
                "type": "player",
                "pool": "medium",
                "amount": 10.03,
                "playerAmount": 10.03,
                "seed": 10,
                "progressive": 0.03,
                "exchangeRate": 1,
            }]
        });
    });

    it("contributes and wins jackpot - rejected win", async () => {
        jpCheckWin.returns({
            type: "win",
            pool: "small",
        });

        remoteGet.onFirstCall().yields(null, { statusCode: 200 }, remoteTicker);

        const remoteTrxId = await wallet.generateTransactionId();
        remoteGet.onSecondCall().yields(null, { statusCode: 200 }, { transactionIds: [remoteTrxId] });

        const updatedRemoteTicker = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0.01, progressive: 0.03 },
                    medium: { seed: 0.02, progressive: 0.04 },
                    large: { seed: 0.03, progressive: 0.07 }
                },
            seqId: 3
        };

        remotePost.onFirstCall().yields(null, { statusCode: 200 },
            { results: [
                    {
                        contributions: [
                            {
                                pool: "small",
                                progressive: 0.03,
                                seed: 0.01
                            },
                            {
                                pool: "medium",
                                progressive: 0.04,
                                seed: 0.02
                            },
                            {
                                pool: "large",
                                progressive: 0.07,
                                seed: 0.03
                            }
                        ],
                        playerContributions: [
                            {
                                pool: "small",
                                progressive: 0.03,
                                seed: 0.01
                            },
                            {
                                pool: "medium",
                                progressive: 0.04,
                                seed: 0.02
                            },
                            {
                                pool: "large",
                                progressive: 0.07,
                                seed: 0.03
                            }
                        ],
                        gameResult: undefined,
                        wins: undefined,
                        winPayouts: undefined,
                        ticker: updatedRemoteTicker
                    }]
            });

        // contributes and wins
        const trxId = await wallet.generateTransactionId();
        const events = await jackpotService.contribute(playerInfo, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "jackpotId": jpInstance.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyJPContribute),
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10.03,
                            "progressive": 0.03,
                            "seed": 0.01
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ],
        });

        const context = await contextService.find(jpInstance.id, playerInfo, trxId);
        expect(context).to.not.exist;
    });
});
