import { expect } from "chai";
import { flush } from "../helpers";
import { PlayerInformation } from "@skywind-group/sw-jpn-core";
import contextService from "../../skywind/services/jackpot.context";
import {
    CURRENT_JACKPOT_CONTEXT_VERSION, JackpotContext, JackpotPlayerWin, JackpotStatus,
    JackpotWinType
} from "../../definition";
import { toCurrentVersion } from "../../skywind/services/jackpot.context.version";

describe("Jackpot Context", () => {

    const jpId: string = "id";
    const playerInfo: PlayerInformation = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        currency: "EUR",
        gameCode: "test"
    };
    const trxId = "123trx";

    beforeEach(async() => {
        await flush();
    });

    it("not found", async() => {
        const context = await contextService.find(jpId, playerInfo, trxId);
        expect(context).to.not.exist;
    });

    it("updates context", async() => {
        const ctx: JackpotContext = {
            jackpotId: jpId,
            transactionId: trxId,
            externalId: "123",
            playerInfo: playerInfo,
            roundId: "1",
            status: JackpotStatus.WON,
            result: {
                type: "win",
                pool: "p1",
            } as any,
            win: {
                type: JackpotWinType.PLAYER,
                pool: "p1",
                amount: 105,
                seed: 100,
                progressive: 5,
                playerAmount: 100,
                exchangeRate: 1.05,
            } as JackpotPlayerWin,
            version: CURRENT_JACKPOT_CONTEXT_VERSION
        };
        await contextService.update(ctx);

        let context = await contextService.find(jpId, playerInfo, trxId);
        expect(context).to.deep.equal(ctx);

        ctx.status = JackpotStatus.PAID;
        await contextService.update(ctx);

        context = await contextService.find(jpId, playerInfo, trxId);
        expect(context).to.deep.equal(ctx);
    });

    it("removes context", async() => {
        const ctx: JackpotContext = {
            jackpotId: jpId,
            transactionId: trxId,
            playerInfo: playerInfo,
            roundId: "1",
            status: JackpotStatus.WON,
            result: {
                type: "win",
                pool: "p1",
            } as any,
            win: {
                type: JackpotWinType.PLAYER,
                pool: "p1",
                amount: 105,
                seed: 100,
                progressive: 5,
                playerAmount: 100,
                exchangeRate: 1.05,
            } as JackpotPlayerWin,
            version: CURRENT_JACKPOT_CONTEXT_VERSION
        };
        await contextService.update(ctx);

        let context = await contextService.find(jpId, playerInfo, trxId);
        expect(context).to.deep.equal(ctx);

        await contextService.remove(ctx);

        context = await contextService.find(jpId, playerInfo, trxId);
        expect(context).to.not.exist;
    });

    it("decode old context with mini game", async() => {
        const ctx: JackpotContext = {
            jackpotId: jpId,
            transactionId: trxId,
            externalId: "123",
            playerInfo: playerInfo,
            roundId: "1",
            status: JackpotStatus.WON,
            result: {
                type: "start-mini-game"
            } as any,
            version: 1
        };
        await contextService.update(ctx);

        let context = await contextService.find(jpId, playerInfo, trxId);
        expect(context).to.deep.equal(ctx);

        context = await toCurrentVersion(context, undefined, undefined);
        expect(context).to.deep.equal({
            jackpotId: jpId,
            transactionId: trxId,
            externalId: "123",
            playerInfo: playerInfo,
            roundId: "1",
            status: JackpotStatus.WON,
            result: {
                type: "start-mini-game"
            } as any,
            version: 2
        });
    });

    it("decode old context with win", async() => {
        const ctx: JackpotContext = {
            jackpotId: jpId,
            transactionId: trxId,
            externalId: "123",
            playerInfo: playerInfo,
            roundId: "1",
            status: JackpotStatus.WON,
            result: {
                type: "win",
                pool: "p1",
            } as any,
            win: {
                pool: "p1",
                amount: 105,
                seed: 100,
                progressive: 5,
                playerAmount: 100,
                exchangeRate: 1.05,
            } as JackpotPlayerWin,
            version: 1
        };
        await contextService.update(ctx);

        let context = await contextService.find(jpId, playerInfo, trxId);
        expect(context).to.deep.equal(ctx);

        context = await toCurrentVersion(context, undefined, undefined);
        expect(context).to.deep.equal({
            jackpotId: jpId,
            transactionId: trxId,
            externalId: "123",
            playerInfo: playerInfo,
            roundId: "1",
            status: JackpotStatus.WON,
            result: {
                type: "win",
                pool: "p1",
            },
            win: {
                type: JackpotWinType.PLAYER,
                pool: "p1",
                amount: 105,
                seed: 100,
                progressive: 5,
                playerAmount: 100,
                exchangeRate: 1.05,
            },
            version: 2
        });
    });
});
