import { expect, use } from "chai";
import { flush, flushDb, JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT } from "../helpers";
import { SinonStub, stub, createSandbox, SinonSandbox } from "sinon";
import * as request from "request";
import config from "../../skywind/config";
import { JackpotInternalInstance, JackpotRegion } from "../../skywind/api/model";
import JackpotRegionService from "../../skywind/services/regionService";
import { Contribution } from "@skywind-group/sw-jpn-core";
import JackpotInstanceService from "../../skywind/services/jackpot.instance";
import { RemoteGameFlowRequest } from "../../skywind/services/remoteJackpotService";
import { RemoteRequestQueue } from "../../skywind/services/remoteRequestQueue";
import { getRedisPool, remoteTickerPool } from "../../skywind/storage/redis";
import { RemoteTickerService } from "../../skywind/services/remoteTicker.service";
import { sleep } from "@skywind-group/sw-utils";
import { ContributionPayout, convertContributionsToContributionsPayout } from "../../skywind/modules/walletParams";
import * as superagent from "superagent";

use(require("chai-as-promised"));

describe("Remote Request Queue", async () => {

    const region: JackpotRegion = {
        code: "eu",
        url: "http://jpn.eu",
        secureOptions: { ca: "rootCA" }
    };
    const jpInstance: JackpotInternalInstance = {
        ...JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT,
        regionCode: region.code,
        region
    };

    const contributions: Contribution[] = [{
        pool: "small",
        seed: 0.01,
        progressive: 0.02,
    }, {
        pool: "medium",
        seed: 0.1,
        progressive: 0.2
    }];
    const ticker = {
        id: "JP-TEST",
        currency: "EUR",
        pools:
            {
                small: { amount: 10, initialSeed: 10, seed: 0, progressive: 0 },
                medium: { amount: 100, initialSeed: 100, seed: 0, progressive: 0 },
                large: { amount: 1000, initialSeed: 1000, seed: 0, progressive: 0 }
            },
        seqId: 2
    };
    const contributionsPayout: ContributionPayout[] = convertContributionsToContributionsPayout(contributions);
    const remoteRequest: RemoteGameFlowRequest = {
        transactionId: "remotetrx",
        playerInfo: {
            playerCode: "PL-CODE",
            brandId: 1,
            region: "eu",
            currency: "CNY",
            gameCode: "test"
        },
        requestRegion: "asia",
        request: {
            transactionId: "trx",
            roundId: "1"
        },
        results: [{
            jackpotId: "id",
            contributions: contributionsPayout,
            playerContributions: contributions
        }]
    };

    let lookup: SinonStub;
    let remotePost: SinonStub;

    let queueConfig;
    let queue: RemoteRequestQueue;
    let tickerService: RemoteTickerService;

    before(async () => {
        queueConfig = config.remoteRequestQueue;
        config.remoteRequestQueue = {
            ...queueConfig,
            retransmitTimeout: 10
        };
        lookup = stub(JackpotInstanceService, "findInternal") as any;
        remotePost = stub(request, "post");
        tickerService = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);
        queue = new RemoteRequestQueue(getRedisPool(), JackpotInstanceService, tickerService);
        queue.retransmit();

        await flushDb();
        await JackpotRegionService.create(region);
    });

    after(() => {
        config.remoteRequestQueue = queueConfig;
        lookup.restore();
        remotePost.restore();
    });

    beforeEach(async () => {
        await flush();
        lookup.resetBehavior();
        lookup.returns(jpInstance);
        remotePost.resetHistory();
    });

    it("request scheduled", async () => {
        remotePost.yields(null, { statusCode: 200 },
            { results: [{ contributions, playerContributions: contributions, ticker }] });

        await queue.schedule(remoteRequest);

        await sleep(5);

        expect(remotePost.calledOnce).to.be.true;
        expect(await queue.isEmpty()).to.be.true;
    });

    it("request retransmitted", async () => {
        remotePost.onFirstCall().yields(null, { statusCode: 500 });
        remotePost.onSecondCall().yields(null, { statusCode: 500 });
        remotePost.onThirdCall().yields(null, { statusCode: 200 },
            { results: [{ contributions, playerContributions: contributions, ticker }] });

        await queue.schedule(remoteRequest);

        await sleep(200);

        expect(remotePost.calledThrice).to.be.true;
        expect(await queue.isEmpty()).to.be.true;
    });

    it("request retransmitted only once by any worker", async () => {
        new RemoteRequestQueue(getRedisPool(), JackpotInstanceService, tickerService).retransmit();
        new RemoteRequestQueue(getRedisPool(), JackpotInstanceService, tickerService).retransmit();

        remotePost.onFirstCall().yields(null, { statusCode: 500 });
        remotePost.onSecondCall().yields(null, { statusCode: 500 });
        remotePost.onThirdCall().yields(null, { statusCode: 200 },
            { results: [{ contributions, playerContributions: contributions, ticker }] });

        await queue.schedule(remoteRequest);

        await sleep(200);

        expect(remotePost.calledThrice).to.be.true;
        expect(await queue.isEmpty()).to.be.true;
    });
});
describe("Remote Request Queue with superagent", async () => {
    let sandbox: SinonSandbox;
    const region: JackpotRegion = {
        code: "eu",
        url: "http://jpn.eu",
        secureOptions: { ca: "rootCA" }
    };
    const jpInstance: JackpotInternalInstance = {
        ...JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT,
        regionCode: region.code,
        region
    };

    const contributions: Contribution[] = [
        {
            pool: "small",
            seed: 0.01,
            progressive: 0.02,
        },
        {
            pool: "medium",
            seed: 0.1,
            progressive: 0.2
        }
    ];
    const ticker = {
        id: "JP-TEST",
        currency: "EUR",
        pools:
            {
                small: { amount: 10, initialSeed: 10, seed: 0, progressive: 0 },
                medium: { amount: 100, initialSeed: 100, seed: 0, progressive: 0 },
                large: { amount: 1000, initialSeed: 1000, seed: 0, progressive: 0 }
            },
        seqId: 2
    };
    const contributionsPayout: ContributionPayout[] = convertContributionsToContributionsPayout(contributions);
    const remoteRequest: RemoteGameFlowRequest = {
        transactionId: "remotetrx",
        playerInfo: {
            playerCode: "PL-CODE",
            brandId: 1,
            region: "eu",
            currency: "CNY",
            gameCode: "test"
        },
        requestRegion: "asia",
        request: {
            transactionId: "trx",
            roundId: "1"
        },
        results: [{
            jackpotId: "id",
            contributions: contributionsPayout,
            playerContributions: contributions
        }]
    };

    let lookup: SinonStub;
    let superagentMock: any;

    let queueConfig;
    let queue: RemoteRequestQueue;
    let tickerService: RemoteTickerService;

    before(async () => {
        sandbox = createSandbox();
        config.enableSuperagentLib = true;
        queueConfig = config.remoteRequestQueue;
        config.remoteRequestQueue = {
            ...queueConfig,
            retransmitTimeout: 10
        };
        lookup = stub(JackpotInstanceService, "findInternal") as any;
        tickerService = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);
        queue = new RemoteRequestQueue(getRedisPool(), JackpotInstanceService, tickerService);
        queue.retransmit();

        await flushDb();
        await JackpotRegionService.create(region);
        superagentMock = {
            set: sandbox.stub().returnsThis(),
            ca: sandbox.stub().returnsThis(),
            key: sandbox.stub().returnsThis(),
            cert: sandbox.stub().returnsThis(),
            query: sandbox.stub().returnsThis(),
            agent: sandbox.stub().returnsThis(),
            send: sandbox.stub().returnsThis(),
            end: sandbox.stub(),
        };
        sandbox.stub(superagent, "post").returns(superagentMock);
    });

    after(() => {
        config.enableSuperagentLib = false;
        config.remoteRequestQueue = queueConfig;
        lookup.restore();
        sandbox.restore();
    });

    beforeEach(async () => {
        await flush();
        lookup.resetBehavior();
        lookup.returns(jpInstance);
        superagentMock.end.resetHistory();
    });

    it("request scheduled", async () => {
        superagentMock.end.callsFake((cb: Function) => {
            cb(null, { status: 200, body: { results: [{ contributions, playerContributions: contributions, ticker }] } });
        });
        await queue.schedule(remoteRequest);

        await sleep(5);

        expect(superagentMock.end.calledOnce).to.be.true;
        expect(await queue.isEmpty()).to.be.true;
    });

    it("request retransmitted", async () => {
        superagentMock.end.onFirstCall().callsFake((cb: Function) => {
            cb(null, { status: 500, body: {} });
        });
        superagentMock.end.onSecondCall().callsFake((cb: Function) => {
            cb(null, { status: 500, body: {} });
        });
        superagentMock.end.onThirdCall().callsFake((cb: Function) => {
            cb(null, { status: 200, body: { results: [{ contributions, playerContributions: contributions, ticker }] } });
        });
        await queue.schedule(remoteRequest);

        await sleep(200);

        expect(superagentMock.end.calledThrice).to.be.true;
        expect(await queue.isEmpty()).to.be.true;
    });

    it("request retransmitted only once by any worker", async () => {
        new RemoteRequestQueue(getRedisPool(), JackpotInstanceService, tickerService).retransmit();
        new RemoteRequestQueue(getRedisPool(), JackpotInstanceService, tickerService).retransmit();

        superagentMock.end.onFirstCall().callsFake((cb: Function) => {
            cb(null, { status: 500, body: {} });
        });
        superagentMock.end.onSecondCall().callsFake((cb: Function) => {
            cb(null, { status: 500, body: {} });
        });
        superagentMock.end.onThirdCall().callsFake((cb: Function) => {
            cb(null, { status: 200, body: { results: [{ contributions, playerContributions: contributions, ticker }] } });
        });

        await queue.schedule(remoteRequest);

        await sleep(200);

        expect(superagentMock.end.calledThrice).to.be.true;
        expect(await queue.isEmpty()).to.be.true;
    });
});
