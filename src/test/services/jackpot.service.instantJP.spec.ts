import { createTestJPGame, flush, JP_INSTANT } from "../helpers";
import { AuthRequest, PlayerInformation } from "@skywind-group/sw-jpn-core";
import { BaseWalletParams } from "../../skywind/modules/walletParams";
import { JackpotService } from "../../skywind/services/jackpot.service";
import wallet from "../../skywind/services/wallet.service";
import contextService from "../../skywind/services/jackpot.context";
import { SinonSpy, SinonStub, spy, stub } from "sinon";
import * as gameService from "../../skywind/services/game.service";
import { init as initCurrencyRates } from "../../skywind/services/currency.service";
import { JackpotWallet } from "../../skywind/modules/jackpotWallet";
import { expect } from "chai";
import { CURRENT_JACKPOT_CONTEXT_VERSION } from "../../definition";
import JackpotInstanceService from "../../skywind/services/jackpot.instance";

describe("Jackpot Service - Instant JP", () => {

    const jpInstance = JP_INSTANT;
    const playerInfo: PlayerInformation = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        currency: "EUR",
        gameCode: "test"
    };
    const auth: AuthRequest = {
        ...playerInfo,
        jackpotIds: [jpInstance.id],
    };
    const walletParams: BaseWalletParams = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        playerCurrency: "EUR",
        gameCode: "test",
        gameId: "test",
        roundId: "1"
    };
    let jpGame;
    const jackpotService: JackpotService = new JackpotService(JackpotInstanceService, wallet, contextService);

    let lookup: SinonStub;
    let applyPendingPool: SinonStub;
    let findOrCreateTest: SinonStub;
    let load: SinonStub;
    let jpCheckWin: SinonStub;
    let jpMiniGame: SinonStub;
    let jpWinJackpot: SinonStub;
    let walletFindCommitted: SinonStub;
    let spyJPContribute: SinonSpy;

    before(async () => {
        lookup = stub(JackpotInstanceService, "findInternal");
        applyPendingPool = stub(JackpotInstanceService, "applyPendingPool");
        findOrCreateTest = stub(JackpotInstanceService, "findOrCreateTest");
        load = stub(gameService, "load");
        walletFindCommitted = stub(wallet, "findCommittedTransaction");
        await initCurrencyRates();
    });

    beforeEach(async () => {
        await flush();

        const jpModule = new JackpotWallet(walletParams, jpInstance, 1, wallet);

        jpGame = await createTestJPGame(jpModule);
        spyJPContribute = spy(jpGame, "getContributions");

        jpCheckWin = stub(jpGame, "checkWin");
        jpMiniGame = stub(jpGame, "winMiniGame");
        jpWinJackpot = stub(jpGame, "winJackpot");

        lookup.resetBehavior();
        lookup.returns(jpInstance);
        applyPendingPool.resetBehavior();
        applyPendingPool.returns(jpInstance);
        findOrCreateTest.resetBehavior();
        load.resetBehavior();
        load.returns(jpGame);
        jpCheckWin.resetHistory();
        jpMiniGame.resetHistory();
        jpWinJackpot.resetHistory();
    });

    after(() => {
        lookup.restore();
        applyPendingPool.restore();
        findOrCreateTest.restore();
        load.restore();
        jpCheckWin.restore();
        jpMiniGame.restore();
        jpWinJackpot.restore();
        walletFindCommitted.restore();
    });

    it("checks win and wins jackpot", async () => {

        jpCheckWin.returns({
            type: "win",
            pool: "D",
        });

        const trxId = await wallet.generateTransactionId();
        let events = await jackpotService.checkWin(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "sw-instant-jp",
                    "jackpotBaseType": "sw-instant-jp",
                    "transactionId": trxId,
                    "pool": "D",
                    "title": "D",
                    "amount": 10,
                    "progressive": 0,
                    "seed": 10
                }
            ],
        });

        let context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "D",
                "type": "win"
            },
            "win": [
                {
                    "type": "player",
                    "pool": "D",
                    "amount": 10,
                    "playerAmount": 10,
                    "seed": 10,
                    "progressive": 0,
                    "exchangeRate": 1,
                }
            ],
        });

        // confirms win
        events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance.id,
                    "jackpotType": "sw-instant-jp",
                    "jackpotBaseType": "sw-instant-jp",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(jpInstance.id, auth, trxId);
        expect(context).to.not.exist;

        const ticker = await jackpotService.getTicker(auth, { exchangeRates: { "EUR": 1 } });
        expect(ticker).to.deep.equal([
            {
                "jackpotId": jpInstance.id,
                "jackpotType": "sw-instant-jp",
                "jackpotBaseType": "sw-instant-jp",
                "pools": {
                    "D": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": -10
                    },
                    "C": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": 0
                    },
                    "B": {
                        "amount": 1000,
                        "progressive": 0,
                        "seed": 0
                    },
                    "A": {
                        "amount": 10000,
                        "progressive": 0,
                        "seed": 0
                    },
                },
            }
        ]);
    });

});
