import { expect, use } from "chai";
import {
    RemoteGameFlowProcessResult,
    RemoteGameFlowRequest,
    RemoteJackpotService
} from "../../skywind/services/remoteJackpotService";
import JackpotInstanceService from "../../skywind/services/jackpot.instance";
import wallet from "../../skywind/services/wallet.service";
import { createTestJPGame, flush, JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT } from "../helpers";
import { SinonStub, stub } from "sinon";
import { AuthRequest, JackpotGameWinResult } from "@skywind-group/sw-jpn-core";
import { JackpotWallet } from "../../skywind/modules/jackpotWallet";
import * as gameService from "../../skywind/services/game.service";
import { init as initCurrencyRates } from "../../skywind/services/currency.service";
import { InsufficientJackpotBalance, JackpotInstanceNotFound, ValidationError } from "../../skywind/errors";
import { BaseWalletParams } from "../../skywind/modules/walletParams";

use(require("chai-as-promised"));

describe("Remote Jackpot service", () => {
    const service = new RemoteJackpotService(JackpotInstanceService, wallet);
    const jpInstance = JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT;
    const playerInfo: AuthRequest = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        jackpotIds: [jpInstance.id],
        currency: "EUR",
        gameCode: "test"
    };
    const walletParams: BaseWalletParams = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        playerCurrency: "EUR",
        gameCode: "test",
        gameId: "test",
        roundId: "1"
    };
    let jpGame;

    let lookup: SinonStub;
    let createJp: SinonStub;
    let load: SinonStub;

    before(async () => {
        lookup = stub(JackpotInstanceService, "findInternal");
        createJp = stub(JackpotInstanceService, "create");
        load = stub(gameService, "load");
        await initCurrencyRates();
    });

    beforeEach(async () => {
        await flush();

        const jpModule = new JackpotWallet(walletParams, jpInstance, 1, wallet);

        jpGame = await createTestJPGame(jpModule);

        lookup.resetBehavior();
        lookup.returns(jpInstance);
        load.resetBehavior();
        load.returns(jpGame);
    });

    after(() => {
        lookup.restore();
        createJp.restore();
        load.restore();
    });

    it("generates batch of transaction ids", async () => {
        const batch = await service.generateTrxIds(10);
        expect(batch.transactionIds.length).to.equal(10);
    });

    it("find remote jackpot", async () => {
        const remoteInstance = await service.findOrCreateJackpot(jpInstance);
        expect(remoteInstance).to.deep.equal(jpInstance);
    });

    it("find remote jackpot - type not match", async () => {
        lookup.returns({ ...jpInstance, type: "other" });
        await expect(service.findOrCreateJackpot(jpInstance)).to.be.rejectedWith(ValidationError);
    });

    it("find remote jackpot - isTest not match", async () => {
        lookup.returns({ ...jpInstance, isTest: true });
        await expect(service.findOrCreateJackpot(jpInstance)).to.be.rejectedWith(ValidationError);
    });

    it("find and create remote jackpot", async () => {
        lookup.throws(new JackpotInstanceNotFound("AUTO_CREATED_test"));
        const info = {
            id: "AUTO_CREATED_test",
            type: "test",
            isTest: true,
            isGlobal: true
        };
        createJp.returns(info);
        const remoteInstance = await service.findOrCreateJackpot(info, true);
        expect(remoteInstance).to.deep.equal(info);
    });

    describe("Remote ticker", () => {

        it("gets ticker", async () => {
            const ticker = await service.getTicker(jpInstance.id);
            expect(ticker).to.deep.equal({
                id: "JP-TEST",
                currency: "EUR",
                pools:
                    {
                        small: { seed: 0, progressive: 0 },
                        medium: { seed: 0, progressive: 0 },
                        large: { seed: 0, progressive: 0 }
                    },
                seqId: 0
            });
        });

        it("gets ticker, after contribution", async () => {
            const jpModule = new JackpotWallet(walletParams, jpInstance, 1, wallet);
            const contributions = [
                { pool: "small", seed: 1, progressive: 2 },
                { pool: "medium", seed: 3, progressive: 4 },
                { pool: "large", seed: 5, progressive: 6 }
            ];
            const transactionId = await wallet.generateTransactionId();
            await jpModule.contribute(transactionId, {
                type: "contribution",
                jackpotIds: [jpInstance.id],
                amount: 21,
                transactionId,
                roundId: "1"
            }, contributions, contributions, undefined);
            const ticker = await service.getTicker(jpInstance.id);
            expect(ticker).to.deep.equal({
                id: "JP-TEST",
                currency: "EUR",
                pools:
                    {
                        small: { seed: 1, progressive: 2 },
                        medium: { seed: 3, progressive: 4 },
                        large: { seed: 5, progressive: 6 }
                    },
                seqId: 1
            });
        });
    });

    describe("process remote game flow", () => {
        it("process contributions", async () => {
            const transactionId = await wallet.generateTransactionId();
            const request: RemoteGameFlowRequest = {
                playerInfo,
                transactionId,
                requestRegion: "asia",
                request: {
                    amount: 10,
                    roundId: "1",
                    transactionId: transactionId
                },
                results: [
                    {
                        jackpotId: "JP-TEST",
                        contributions: [
                            {
                                pool: "small",
                                seed: 0.7,
                                progressive: 0.5,
                                totalSeed: 0.7,
                                totalProgressive: 0.5
                            }
                        ],
                        playerContributions: [
                            {
                                pool: "small",
                                seed: 1,
                                progressive: 0.5
                            }

                        ]
                    }
                ]
            };
            const result = await service.processRemoteGameFlow(request);
            expect(result).to.not.equal(undefined);
            expect(result.results.length).to.equal(1);
            expect(result.results[0].contributions).deep.equal([
                    {
                        pool: "small",
                        seed: 0.7,
                        progressive: 0.5,
                        totalSeed: 0,
                        totalProgressive: 0
                    }
                ]
            );
            expect(result.results[0].playerContributions).deep.equal([
                {
                    pool: "small",
                    seed: 1,
                    progressive: 0.5
                }

            ]);
        });
        it("process single win", async () => {
            const transactionId = await wallet.generateTransactionId();
            const request: RemoteGameFlowRequest = {
                playerInfo,
                transactionId,
                requestRegion: "asia",
                request: {
                    amount: 10,
                    roundId: "1",
                    transactionId: transactionId
                },
                results: [
                    {
                        jackpotId: "JP-TEST",
                        gameResult: {
                            type: "win",
                            pool: "medium"
                        } as JackpotGameWinResult
                    }
                ]
            };
            const result = await service.processRemoteGameFlow(request);
            expect(result).deep.equal({
                results: [
                    {
                        contributions: undefined,
                        playerContributions: undefined,
                        gameResult: {
                            pool: "medium",
                            type: "win"
                        },
                        jackpotId: "JP-TEST",
                        ticker: {
                            currency: "EUR",
                            id: "JP-TEST",
                            pools: {
                                large: {
                                    progressive: 0,
                                    seed: 0
                                },
                                medium: {
                                    progressive: 0,
                                    seed: -100
                                },
                                small: {
                                    progressive: 0,
                                    seed: 0
                                }
                            },
                            seqId: 0
                        },
                        winPayouts: [
                            {
                                currencyRate: 1,
                                initialSeed: 100,
                                pool: "medium",
                                title: "medium",
                                progressive: 0,
                                progressiveSinceLastWin: 0,
                                seed: 100,
                                seedSinceLastWin: 0,
                                totalProgressive: 0,
                                totalSeed: 0,
                                winAmount: 100,
                                info: null,
                            }
                        ],
                        wins: [
                            {
                                type: "player",
                                amount: 100,
                                exchangeRate: 1,
                                playerAmount: 100,
                                pool: "medium",
                                progressive: 0,
                                seed: 100,
                            }
                        ]
                    }
                ]
            });
        });
        it("process wins", async () => {
            const transactionId = await wallet.generateTransactionId();
            const gameResult: JackpotGameWinResult[] = [
                { type: "win", pool: "medium" },
                { type: "win", pool: "medium" },
                { type: "win", pool: "large" }
            ];
            const request: RemoteGameFlowRequest = {
                playerInfo,
                transactionId,
                requestRegion: "asia",
                request: {
                    amount: 10,
                    roundId: "1",
                    transactionId: transactionId,
                    exchangeRate: 0.5
                },
                results: [
                    {
                        jackpotId: "JP-TEST",
                        gameResult
                    }
                ]
            };
            const result = await service.processRemoteGameFlow(request);
            expect(result.results[0].gameResult).deep.equal(gameResult);
            expect(result.results[0].wins).deep.equal([
                {
                    type: "player",
                    amount: 100,
                    exchangeRate: 0.5,
                    playerAmount: 200,
                    pool: "medium",
                    progressive: 0,
                    seed: 100,
                },
                {
                    type: "player",
                    amount: 100,
                    exchangeRate: 0.5,
                    playerAmount: 200,
                    pool: "medium",
                    progressive: 0,
                    seed: 100,
                },
                {
                    type: "player",
                    amount: 1000,
                    exchangeRate: 0.5,
                    playerAmount: 2000,
                    pool: "large",
                    progressive: 0,
                    seed: 1000,
                }
            ]);
            expect(result.results[0].winPayouts).deep.equal([
                {
                    currencyRate: 0.5,
                    initialSeed: 100,
                    pool: "medium",
                    title: "medium",
                    progressive: 0,
                    progressiveSinceLastWin: 0,
                    seed: 100,
                    seedSinceLastWin: 0,
                    totalProgressive: 0,
                    totalSeed: 0,
                    winAmount: 200,
                    info: null,
                },
                {
                    currencyRate: 0.5,
                    initialSeed: 100,
                    pool: "medium",
                    title: "medium",
                    progressive: 0,
                    progressiveSinceLastWin: 0,
                    seed: 100,
                    seedSinceLastWin: 0,
                    totalProgressive: 0,
                    totalSeed: 0,
                    winAmount: 200,
                    info: null,
                },
                {
                    currencyRate: 0.5,
                    initialSeed: 1000,
                    pool: "large",
                    title: "large",
                    progressive: 0,
                    progressiveSinceLastWin: 0,
                    seed: 1000,
                    seedSinceLastWin: 0,
                    totalProgressive: 0,
                    totalSeed: 0,
                    winAmount: 2000,
                    info: null,
                }
            ]);
        });
        it("process contributions + wins", async () => {
            const transactionId = await wallet.generateTransactionId();
            const gameResult: JackpotGameWinResult[] = [
                { type: "win", pool: "medium" },
                { type: "win", pool: "medium" },
                { type: "win", pool: "large" }
            ];
            const request: RemoteGameFlowRequest = {
                playerInfo,
                transactionId,
                requestRegion: "asia",
                request: {
                    amount: 10,
                    roundId: "1",
                    transactionId: transactionId,
                    exchangeRate: 0.5
                },
                results: [
                    {
                        jackpotId: "JP-TEST",
                        gameResult,
                        contributions: [
                            {
                                pool: "medium",
                                seed: 100,
                                progressive: 500,
                                totalSeed: 100,
                                totalProgressive: 500
                            },
                            {
                                pool: "large",
                                seed: 1000,
                                progressive: 5000,
                                totalSeed: 1000,
                                totalProgressive: 5000
                            }
                        ]
                    }
                ]
            };
            const result = await service.processRemoteGameFlow(request);
            const remoteResult: RemoteGameFlowProcessResult = result.results[0];
            expect(remoteResult.gameResult).deep.equal(gameResult);
            expect(remoteResult.wins).to.not.equal(undefined);
            expect(remoteResult.contributions).to.not.equal(undefined);
            expect(remoteResult.winPayouts).deep.equal([
                {
                    currencyRate: 0.5,
                    initialSeed: 100,
                    pool: "medium",
                    title: "medium",
                    progressive: 500,
                    progressiveSinceLastWin: 500,
                    seed: 100,
                    seedSinceLastWin: 100,
                    totalProgressive: 500,
                    totalSeed: 100,
                    winAmount: 1200,
                    info: null,
                },
                {
                    currencyRate: 0.5,
                    initialSeed: 100,
                    pool: "medium",
                    title: "medium",
                    progressive: 0,
                    progressiveSinceLastWin: 0,
                    seed: 100,
                    seedSinceLastWin: 0,
                    totalProgressive: 500,
                    totalSeed: 100,
                    winAmount: 200,
                    info: null,
                },
                {
                    currencyRate: 0.5,
                    initialSeed: 1000,
                    pool: "large",
                    title: "large",
                    progressive: 5000,
                    progressiveSinceLastWin: 5000,
                    seed: 1000,
                    seedSinceLastWin: 1000,
                    totalProgressive: 5000,
                    totalSeed: 1000,
                    winAmount: 12000,
                    info: null,
                }
            ]);
        });
        describe("retries to win jackpot on insufficient balance", () => {

            let releaseStub;

            beforeEach(() => {
                releaseStub = stub(JackpotWallet.prototype, "releaseWin");
            });

            it("retries to win jackpot on insufficient balance", async () => {
                releaseStub.onFirstCall().throws(new InsufficientJackpotBalance());
                releaseStub.onSecondCall().throws(new InsufficientJackpotBalance());
                releaseStub.onThirdCall()
                    .returns({ wins: [{ bbb: 2 }], winPayouts: [{ aaa: 1 }] });
                const transactionId = await wallet.generateTransactionId();
                const gameResult: JackpotGameWinResult[] = [
                    { type: "win", pool: "medium" },
                    { type: "win", pool: "medium" },
                    { type: "win", pool: "large" }
                ];
                const request: RemoteGameFlowRequest = {
                    playerInfo,
                    transactionId,
                    requestRegion: "asia",
                    request: {
                        amount: 10,
                        roundId: "1",
                        transactionId: transactionId,
                        exchangeRate: 0.5
                    },
                    results: [
                        {
                            jackpotId: "JP-TEST",
                            gameResult,
                            contributions: [
                                {
                                    pool: "medium",
                                    seed: 100,
                                    progressive: 500,
                                    totalSeed: 100,
                                    totalProgressive: 500
                                },
                                {
                                    pool: "large",
                                    seed: 1000,
                                    progressive: 5000,
                                    totalSeed: 1000,
                                    totalProgressive: 5000
                                }
                            ]
                        },
                    ]
                };
                const result = await service.processRemoteGameFlow(request);
                const remoteResult: RemoteGameFlowProcessResult = result.results[0];
                expect(remoteResult.contributions).to.not.equal(undefined);
                expect(remoteResult.winPayouts).deep.equal([{ aaa: 1 }]);
                expect(remoteResult.wins).deep.equal([{ bbb: 2 }]);
            });

            afterEach(() => {
                releaseStub.restore();
            });
        });

        describe("validates win result", () => {

            let validateCheckWinStub;

            beforeEach(() => {
                validateCheckWinStub = stub(jpGame, "validateCheckWin");
            });

            afterEach(() => {
                validateCheckWinStub.restore();
            });

            it("rejects win", async () => {
                validateCheckWinStub.returns(undefined);
                const transactionId = await wallet.generateTransactionId();
                const gameResult: JackpotGameWinResult[] = [
                    { type: "win", pool: "medium" },
                    { type: "win", pool: "medium" },
                    { type: "win", pool: "large" }
                ];
                const request: RemoteGameFlowRequest = {
                    playerInfo,
                    transactionId,
                    requestRegion: "asia",
                    request: {
                        amount: 10,
                        roundId: "1",
                        transactionId: transactionId,
                        exchangeRate: 0.5
                    },
                    results: [
                        {
                            jackpotId: "JP-TEST",
                            gameResult,
                            contributions: [
                                {
                                    pool: "medium",
                                    seed: 100,
                                    progressive: 500,
                                    totalSeed: 100,
                                    totalProgressive: 500
                                },
                                {
                                    pool: "large",
                                    seed: 1000,
                                    progressive: 5000,
                                    totalSeed: 1000,
                                    totalProgressive: 5000
                                }
                            ]
                        },
                    ]
                };
                const result = await service.processRemoteGameFlow(request);
                const remoteResult: RemoteGameFlowProcessResult = result.results[0];
                expect(remoteResult.contributions).to.not.equal(undefined);
                expect(remoteResult.winPayouts).be.undefined;
                expect(remoteResult.wins).be.undefined;
            });
        });
    });
});
