import { expect, use } from "chai";
import { flushDb } from "../helpers";
import JackpotRegionService from "../../skywind/services/regionService";
import { JackpotRegion } from "../../skywind/api/model";
import * as Errors from "../../skywind/errors";

use(require("chai-as-promised"));

describe("Jackpot Region Service", () => {

    const region: JackpotRegion = {
        code: "eu",
        url: "http://jpn.eu",
        secureOptions: { key: "key" }
    };

    beforeEach(async() => {
        await flushDb();
    });

    it("crud region", async() => {
        const created = await JackpotRegionService.create(region);
        expect(created).to.deep.equal(region);

        const list = await JackpotRegionService.findAll();
        expect(list).to.deep.equal([region]);

        const data = await JackpotRegionService.find(region.code);
        expect(data).to.deep.equal(region);

        const update = { url: "http://jpn.eu.2" } as JackpotRegion;
        const updated = await JackpotRegionService.update(region.code, update);
        expect(updated).to.deep.equal({ ...region, ...update });

        await JackpotRegionService.remove(region.code);
    });

    it("fails to create invalid region", async() => {
        await expect(JackpotRegionService.create({} as JackpotRegion))
            .to.be.rejectedWith(Errors.ValidationError);

        await expect(JackpotRegionService.create({ code: "eu" } as JackpotRegion))
            .to.be.rejectedWith(Errors.ValidationError);

        await expect(JackpotRegionService.create({ code: "eu" } as JackpotRegion))
            .to.be.rejectedWith(Errors.ValidationError);

        await expect(JackpotRegionService.create({ code: "eu", url: "http" } as JackpotRegion))
            .to.be.rejectedWith(Errors.ValidationError);

        await expect(JackpotRegionService.create({ code: "eu", url: "http://url", secureOptions: [] } as JackpotRegion))
            .to.be.rejectedWith(Errors.ValidationError);
    });

    it("fails to update unknown region", async() => {
        await expect(JackpotRegionService.update("unknown", {} as JackpotRegion))
            .to.be.rejectedWith(Errors.RegionNotFoundError);
    });

    it("fails to find unknown region", async() => {
        await expect(JackpotRegionService.find("unknown")).to.be.rejectedWith(Errors.RegionNotFoundError);
    });

    it("fails to remove unknown region", async() => {
        await expect(JackpotRegionService.remove("unknown")).to.be.rejectedWith(Errors.RegionNotFoundError);
    });
});
