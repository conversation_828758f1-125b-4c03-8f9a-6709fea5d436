import { expect, use } from "chai";
import { JackpotWallet } from "../../skywind/modules/jackpotWallet";
import {
    createTestJPGame,
    flush,
    getLastTransaction,
    getSpiedPlayerContributions,
    JP_WITH_DYNAMIC_CONTRIBUTION
} from "../helpers";
import { SinonSpy, SinonStub, spy, stub } from "sinon";
import JackpotInstanceService from "../../skywind/services/jackpot.instance";
import * as gameService from "../../skywind/services/game.service";
import { JackpotService } from "../../skywind/services/jackpot.service";
import { AuthRequest, BaseRequest, JackpotGame, PlayerInformation } from "@skywind-group/sw-jpn-core";
import wallet from "../../skywind/services/wallet.service";
import contextService from "../../skywind/services/jackpot.context";
import { init as initCurrencyRates } from "../../skywind/services/currency.service";
import { CheatingRandomGenerator } from "@skywind-group/sw-random-cs-cheating";
import * as _ from "lodash";
import { CURRENT_JACKPOT_CONTEXT_VERSION, JackpotContextService, JackpotWin } from "../../definition";
import * as Errors from "../../skywind/errors";
import * as JackpotGameFlows from "../../skywind/services/jackpotGameFlow";
import { JackpotGameFlow } from "../../skywind/services/jackpotGameFlow";
import { JackpotModule } from "../../skywind/modules/jackpotModule";
import { BaseWalletParams } from "../../skywind/modules/walletParams";
use(require("chai-as-promised"));

describe("Jackpot Service Multiple IDs", () => {

    const jpInstance1: any = _.merge({}, JP_WITH_DYNAMIC_CONTRIBUTION, { internalId: 1, id: "JP_1", jpGameId: "test1" });
    const jpInstance2: any = _.merge({}, JP_WITH_DYNAMIC_CONTRIBUTION, { internalId: 2, id: "JP_2", jpGameId: "test2" });
    const jpInstance3: any = _.merge({}, JP_WITH_DYNAMIC_CONTRIBUTION, { internalId: 3, id: "JP_3", jpGameId: "test3" });
    jpInstance3.definition.currency =  "CNY";
    const playerInfo: PlayerInformation = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        currency: "EUR",
        gameCode: "test"
    };
    const auth: AuthRequest = {
        ...playerInfo,
        jackpotIds: [jpInstance1.id, jpInstance2.id],
    };
    const walletParams: BaseWalletParams = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        playerCurrency: "EUR",
        gameCode: "test",
        gameId: "test",
        roundId: "1"
    };
    const cnyPlayerInfo: PlayerInformation = {
        playerCode: "PL-CODE-CNY",
        brandId: 1,
        region: "eu",
        currency: "CNY",
        gameCode: "test"
    };
    const cnyPlayer: AuthRequest = {
        ...cnyPlayerInfo,
        jackpotIds: [jpInstance1.id, jpInstance2.id],
    };
    let jpGame1;
    let jpGame2;
    let jpGame3;
    const jackpotService: JackpotService = new JackpotService(JackpotInstanceService, wallet, contextService);

    let lookup: SinonStub;
    let load: SinonStub;
    let jpCheckWin1: SinonStub;
    let jpMiniGame1: SinonStub;
    let jpCheckWin2: SinonStub;
    let jpMiniGame2: SinonStub;
    let walletFindCommitted: SinonStub;
    let spyJPGame1Contribution: SinonSpy;
    let spyJPGame2Contribution: SinonSpy;

    before(async () => {
        const jp1 = new JackpotWallet(walletParams, jpInstance1, 1, wallet);
        jpGame1 = await createTestJPGame(jp1);
        spyJPGame1Contribution = spy(jpGame1, "getContributions");
        const jp2 = new JackpotWallet(walletParams, jpInstance2, 1, wallet);
        jpGame2 = await createTestJPGame(jp2);
        spyJPGame2Contribution = spy(jpGame2, "getContributions");
        const jp3 = new JackpotWallet(walletParams, jpInstance3, 1, wallet);
        jpGame3 = await createTestJPGame(jp3);

        lookup = stub(JackpotInstanceService, "findInternal").callsFake((id) => {
            if (id === jpInstance1.id) {
                return jpInstance1;
            } else if (id === jpInstance2.id) {
                return jpInstance2;
            } else if (id === jpInstance3.id) {
                return jpInstance3;
            } else {
                throw new Error("not found");
            }
        });
        load = stub(gameService, "load").callsFake((gameId, jp) => {
            if (gameId === jpInstance1.jpGameId) {
                return jpGame1;
            } else if (gameId === jpInstance2.jpGameId) {
                return jpGame2;
            } else if (gameId === jpInstance3.jpGameId) {
                return jpGame3;
            } else {
                throw new Error("not found");
            }
        });
        jpCheckWin1 = stub(jpGame1, "checkWin");
        jpMiniGame1 = stub(jpGame1, "winMiniGame");
        jpCheckWin2 = stub(jpGame2, "checkWin");
        jpMiniGame2 = stub(jpGame2, "winMiniGame");
        walletFindCommitted = stub(wallet, "findCommittedTransaction");
        await initCurrencyRates();
    });

    beforeEach(async () => {
        spyJPGame1Contribution.resetHistory();
        spyJPGame2Contribution.resetHistory();
        jpCheckWin1.reset();
        jpMiniGame1.reset();
        jpCheckWin2.reset();
        jpMiniGame2.reset();
        jpCheckWin1.resetBehavior();
        jpMiniGame1.resetBehavior();
        jpCheckWin2.resetBehavior();
        jpMiniGame2.resetBehavior();
        walletFindCommitted.resetHistory();

        await flush();

        load.resetHistory();
    });

    after(() => {
        spyJPGame1Contribution.restore();
        spyJPGame2Contribution.restore();
        lookup.restore();
        load.restore();
        jpCheckWin1.restore();
        jpMiniGame1.restore();
        jpCheckWin2.restore();
        jpMiniGame2.restore();
        walletFindCommitted.restore();
    });

    it("gets ticker", async () => {
        const ticker = await jackpotService.getTicker(auth, {});
        expect(ticker.length).to.equal(2);
        expect(ticker).to.deep.include.members([
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": 0
                    },
                },
            },
            {
                "jackpotId": jpInstance2.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": 0
                    },
                },
            },
        ]);
    });

    it("gets ticker in different currency", async () => {
        const ticker = await jackpotService.getTicker(cnyPlayer, {});

        expect(ticker).to.deep.include.members([
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 7965.12,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 796.51,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 79.65,
                        "progressive": 0,
                        "seed": 0
                    },
                },
            },
            {
                "jackpotId": jpInstance2.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 7965.12,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 796.51,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 79.65,
                        "progressive": 0,
                        "seed": 0
                    },
                },
            },
        ]);
    });

    it("gets ticker with different currency rates", async () => {
        const player: AuthRequest = {
            ...cnyPlayerInfo,
            jackpotIds: [jpInstance1.id, jpInstance2.id, jpInstance3.id],
        };
        const ticker = await jackpotService.getTicker(player, {
            exchangeRates: {
                "EUR": 0.1255473866,
                "CNY": 1
            }
        });

        expect(ticker).to.deep.include.members([
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 7965.12,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 796.51,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 79.65,
                        "progressive": 0,
                        "seed": 0
                    },
                },
            },
            {
                "jackpotId": jpInstance2.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 7965.12,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 796.51,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 79.65,
                        "progressive": 0,
                        "seed": 0
                    },
                },
            },
            {
                "jackpotId": jpInstance3.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000,
                        "progressive": 0,
                        "seed": 0
                    },
                    "medium": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": 0
                    },
                    "small": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": 0
                    },
                },
            },
        ]);
    });

    it("contributes", async () => {
        jpCheckWin1.returns(undefined);
        jpCheckWin2.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        const events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events.events.length).to.equal(2);
        expect(events.events).to.deep.include.members([
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame1Contribution),
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
            {
                "jackpotId": jpInstance2.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame2Contribution),
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
        ]);

        const context1 = await contextService.find(jpInstance1.id, auth, "123");
        expect(context1).to.not.exist;
        const context2 = await contextService.find(jpInstance2.id, auth, "123");
        expect(context2).to.not.exist;
    });

    it("contributes to specified jackpotId", async () => {
        jpCheckWin1.returns(undefined);
        jpCheckWin2.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        const events = await jackpotService.contribute(auth, {
            externalId: "123",
            jackpotIds: [jpInstance1.id],
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events.events.length).to.equal(1);
        expect(events.events).to.deep.include.members([
            {
                "jackpotId": jpInstance1.id,
                "contributions": getSpiedPlayerContributions(spyJPGame1Contribution),
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            }
        ]);

        const context1 = await contextService.find(jpInstance1.id, auth, "123");
        expect(context1).to.not.exist;
        const context2 = await contextService.find(jpInstance2.id, auth, "123");
        expect(context2).to.not.exist;
    });

    it("contributes to specified jackpotIds", async () => {
        jpCheckWin1.returns(undefined);
        jpCheckWin2.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        const events = await jackpotService.contribute(auth, {
            externalId: "123",
            jackpotIds: [jpInstance1.id, jpInstance2.id],
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events.events.length).to.equal(2);
        expect(events.events).to.deep.include.members([
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame1Contribution),
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            }
        ]);

        const context1 = await contextService.find(jpInstance1.id, auth, "123");
        expect(context1).to.not.exist;
        const context2 = await contextService.find(jpInstance2.id, auth, "123");
        expect(context2).to.not.exist;
    });

    it("cheating not allowed", async () => {
        jpCheckWin1.returns(undefined);
        jpCheckWin2.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1",
            cheats: [2, 3],
        });

        const cheatingRng1 = load.args[0][3];
        const cheatingRng2 = load.args[1][3];

        expect(cheatingRng1).not.to.be.instanceof(CheatingRandomGenerator);
        expect(cheatingRng2).not.to.be.instanceof(CheatingRandomGenerator);

        expect(cheatingRng1.get()).not.equal(2).and.not.equal(3);
        expect(cheatingRng2.get()).not.equal(2).and.not.equal(3);
    });

    describe("cheating allowed", () => {

        const cheatsConfig = require("../../../resources/cheatsConfig.json");

        before(() => {
            cheatsConfig.allowSetPositionsByClient = true;
        });

        after(() => {
            cheatsConfig.allowSetPositionsByClient = false;
        });

        it("cheats", async () => {
            jpCheckWin1.returns(undefined);
            jpCheckWin2.returns(undefined);

            const trxId = await wallet.generateTransactionId();
            await jackpotService.contribute(auth, {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1",
                cheats: [2, 3],
            });

            const cheatingRng1 = load.args[0][3];
            const cheatingRng2 = load.args[1][3];

            expect(cheatingRng1).to.be.instanceof(CheatingRandomGenerator);
            expect(cheatingRng2).to.be.instanceof(CheatingRandomGenerator);

            expect(cheatingRng1.get()).equal(2);
            expect(cheatingRng2.get()).equal(3);
        });
    });

    it("contributes in different currency", async () => {
        jpCheckWin1.returns(undefined);
        jpCheckWin2.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        const events = await jackpotService.contribute(cnyPlayer, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events.events.length).to.equal(2);
        expect(events.events).to.deep.include.members([
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame1Contribution,
                    0.125547319179561),
                "pools":
                    {
                        "large":
                            {
                                "amount":
                                    7965.19,
                                "progressive": 0.008788312,
                                "seed": 0.00376642
                            }
                        ,
                        "medium":
                            {
                                "amount":
                                    796.55,
                                "progressive": 0.005021893,
                                "seed": 0.002510946
                            }
                        ,
                        "small":
                            {
                                "amount":
                                    79.68,
                                "progressive": 0.00376642,
                                "seed": 0.001255473
                            }
                        ,
                    }
                ,
                "type":
                    "contribution",
                "totalContribution":
                    0.2
            },
            {
                "jackpotId":
                jpInstance2.id,
                "contributions": getSpiedPlayerContributions(spyJPGame1Contribution, 0.125547319179561),
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools":
                    {
                        "large":
                            {
                                "amount":
                                    7965.19,
                                "progressive": 0.008788312,
                                "seed": 0.00376642
                            }
                        ,
                        "medium":
                            {
                                "amount":
                                    796.55,
                                "progressive": 0.005021893,
                                "seed": 0.002510946
                            }
                        ,
                        "small":
                            {
                                "amount":
                                    79.68,
                                "progressive": 0.00376642,
                                "seed": 0.001255473
                            }
                        ,
                    }
                ,
                "type":
                    "contribution",
                "totalContribution":
                    0.2
            }
            ,
        ])
        ;

        const context1 = await contextService.find(jpInstance1.id, cnyPlayer, "123");
        expect(context1).to.not.exist;
        const context2 = await contextService.find(jpInstance2.id, cnyPlayer, "123");
        expect(context2).to.not.exist;
    });

    it("contributes and wins jackpot", async () => {
        jpCheckWin1.returns({
            type: "win",
            pool: "small",
        });

        // contributes and wins
        const trxId = await wallet.generateTransactionId();
        let events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events.events.length).to.equal(3);
        expect(events.events).to.deep.include.members([
            {
                "type": "win",
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "transactionId": trxId,
                "pool": "small",
                "title": "friendly name",
                "amount": 10.03,
                "progressive": 0.03,
                "seed": 10
            },
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame1Contribution),
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": -9.99
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
            {
                "jackpotId": jpInstance2.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame2Contribution),
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
        ]);

        let context1 = await contextService.find(jpInstance1.id, auth, trxId);
        expect(context1).to.deep.equal({
            "jackpotId": jpInstance1.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "small",
                "type": "win"
            },
            "win": [
                {
                    "type": "player",
                    "amount": 10.03,
                    "exchangeRate": 1,
                    "playerAmount": 10.03,
                    "pool": "small",
                    "progressive": 0.03,
                    "seed": 10
                }
            ]
        });
        const context2 = await contextService.find(jpInstance2.id, auth, trxId);
        expect(context2).to.not.exist;

        // confirms win
        events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance1.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance1.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context1 = await contextService.find(jpInstance1.id, auth, trxId);
        expect(context1).to.not.exist;
    });

    it("wins multiple jackpots", async () => {
        jpCheckWin1.returns({
            type: "win",
            pool: "small",
        });

        jpCheckWin2.returns({
            type: "win",
            pool: "small",
        });

        // contributes and wins
        const trxId = await wallet.generateTransactionId();
        let events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });
        expect(events.events.length).to.equal(4);
        expect(events.events).to.deep.include.members([
            {
                "type": "win",
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "transactionId": trxId,
                "pool": "small",
                "title": "friendly name",
                "amount": 10.03,
                "progressive": 0.03,
                "seed": 10
            },
            {
                "type": "win",
                "jackpotId": jpInstance2.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "transactionId": trxId,
                "pool": "small",
                "title": "friendly name",
                "amount": 10.03,
                "progressive": 0.03,
                "seed": 10
            },
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame1Contribution),
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": -9.99
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
            {
                "jackpotId": jpInstance2.id,
                "contributions": getSpiedPlayerContributions(spyJPGame2Contribution),
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": -9.99
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
        ]);

        let context1 = await contextService.find(jpInstance1.id, auth, trxId);
        expect(context1).to.deep.equal({
            "jackpotId": jpInstance1.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "small",
                "type": "win"
            },
            "win": [
                {
                    "type": "player",
                    "amount": 10.03,
                    "exchangeRate": 1,
                    "playerAmount": 10.03,
                    "pool": "small",
                    "progressive": 0.03,
                    "seed": 10
                }
            ]
        });
        let context2 = await contextService.find(jpInstance2.id, auth, trxId);
        expect(context2).to.deep.equal({
            "jackpotId": jpInstance2.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "small",
                "type": "win"
            },
            "win": [
                {
                    "type": "player",
                    "amount": 10.03,
                    "exchangeRate": 1,
                    "playerAmount": 10.03,
                    "pool": "small",
                    "progressive": 0.03,
                    "seed": 10
                }
            ]
        });

        // confirms win
        events = await jackpotService.confirmWin(auth,
            { transactionId: trxId, jackpotId: [jpInstance1.id, jpInstance2.id], roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance1.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance2.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context1 = await contextService.find(jpInstance1.id, auth, trxId);
        expect(context1).to.not.exist;
        context2 = await contextService.find(jpInstance2.id, auth, trxId);
        expect(context2).to.not.exist;
    });

    describe("retries to win jackpot on insufficient balance", () => {

        let createJackpotGameFlowStub;
        let jpGetWinPayouts1: SinonStub;
        let jpGetWinPayouts2: SinonStub;

        before(() => {
            const createJackpotGameFlow = JackpotGameFlows.createJackpotGameFlow;

            createJackpotGameFlowStub = stub(JackpotGameFlows, "createJackpotGameFlow").callsFake(
                async (request: BaseRequest, jpModule: JackpotModule,
                       game: JackpotGame, contextService: JackpotContextService): Promise<JackpotGameFlow> => {
                    const flow = await createJackpotGameFlow(request, jpModule, game, contextService);

                    const jpWallet = jpModule["jpWallet"];
                    const jpModuleWin = jpWallet.releaseWin;

                    jpWallet.releaseWin = async (trxId: string,
                                                 externalId: string,
                                                 roundId: string,
                                                 wins: JackpotWin[]) => {
                        // concurrent win
                        const otherTrxId = await wallet.generateTransactionId();
                        const otherModule = new JackpotWallet(walletParams, jpModule.instance, 1, wallet);
                        await otherModule.releaseWin(otherTrxId, "concurrent", "concurrent", [wins[0]]);

                        return jpModuleWin.call(jpWallet, trxId, externalId, roundId, wins);
                    };

                    return flow;
                });
        });

        beforeEach(() => {
            jpGetWinPayouts1 = stub(jpGame1, "getWinPayouts");
            jpGetWinPayouts2 = stub(jpGame2, "getWinPayouts");
        });

        afterEach(() => {
            jpGetWinPayouts1.restore();
            jpGetWinPayouts2.restore();
        });

        after(() => {
            createJackpotGameFlowStub.restore();
        });

        it("retries to win jackpot on insufficient balance", async () => {
            jpCheckWin1.returns({
                type: "win",
                pool: "small",
            });
            jpGetWinPayouts1.onFirstCall().returns([{
                "pool": "small",
                "amount": 10.03,
                "progressive": 0.03,
                "seed": 10
            }]);
            jpGetWinPayouts1.onSecondCall().returns([{
                "pool": "small",
                "amount": 10,
                "progressive": 0,
                "seed": 10
            }]);

            // contributes and wins
            const trxId = await wallet.generateTransactionId();
            let events = await jackpotService.contribute(auth, {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1"
            });

            expect(events.events.length).to.equal(3);
            expect(events.events).to.deep.include.members([
                {
                    "type": "win",
                    "jackpotId": jpInstance1.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "pool": "small",
                    "title": "friendly name",
                    "amount": 10,
                    "progressive": 0.0,
                    "seed": 10
                },
                {
                    "jackpotId": jpInstance1.id,
                    "contributions": getSpiedPlayerContributions(spyJPGame1Contribution),
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10,
                            "progressive": 0,
                            "seed": -29.99
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
                {
                    "jackpotId": jpInstance2.id,
                    "contributions": getSpiedPlayerContributions(spyJPGame2Contribution),
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10.03,
                            "progressive": 0.03,
                            "seed": 0.01
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ]);

            let context = await contextService.find(jpInstance1.id, auth, trxId);
            expect(context).to.deep.equal({
                "jackpotId": jpInstance1.id,
                "transactionId": trxId,
                "externalId": "123",
                "playerInfo": playerInfo,
                "roundId": "1",
                "status": 3,
                "version": CURRENT_JACKPOT_CONTEXT_VERSION,
                "result": {
                    "pool": "small",
                    "type": "win"
                },
                "win": [
                    {
                        "type": "player",
                        "pool": "small",
                        "amount": 10,
                        "playerAmount": 10,
                        "exchangeRate": 1,
                        "progressive": 0,
                        "seed": 10
                    }
                ]
            });

            // confirms win
            events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance1.id, roundId: "1" });
            expect(events).to.deep.equal({
                "events": [
                    {
                        "type": "win-confirm",
                        "jackpotId": jpInstance1.id,
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "transactionId": trxId,
                    },
                ],
            });

            context = await contextService.find(jpInstance1.id, auth, trxId);
            expect(context).to.not.exist;
        });

        it("retries to win multiple jackpots on insufficient balance", async () => {
            jpCheckWin1.returns({
                type: "win",
                pool: "small",
            });
            jpGetWinPayouts1.onFirstCall().returns([{
                "pool": "small",
                "amount": 10.03,
                "progressive": 0.03,
                "seed": 10
            }]);
            jpGetWinPayouts1.onSecondCall().returns([{
                "pool": "small",
                "amount": 10,
                "progressive": 0,
                "seed": 10
            }]);
            jpCheckWin2.returns({
                type: "win",
                pool: "medium",
            });
            jpGetWinPayouts2.onFirstCall().returns([{
                "pool": "medium",
                "amount": 100.04,
                "progressive": 0.04,
                "seed": 100
            }]);
            jpGetWinPayouts2.onSecondCall().returns([{
                "pool": "medium",
                "amount": 100,
                "progressive": 0,
                "seed": 100
            }]);

            // contributes and wins
            const trxId = await wallet.generateTransactionId();
            let events = await jackpotService.contribute(auth, {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1"
            });

            expect(events.events.length).to.equal(4);
            expect(events.events).to.deep.include.members([
                {
                    "type": "win",
                    "jackpotId": jpInstance1.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "pool": "small",
                    "title": "friendly name",
                    "amount": 10,
                    "progressive": 0.0,
                    "seed": 10
                },
                {
                    "type": "win",
                    "jackpotId": jpInstance2.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "pool": "medium",
                    "title": "medium",
                    "amount": 100,
                    "progressive": 0.0,
                    "seed": 100
                },
                {
                    "jackpotId": jpInstance1.id,
                    "contributions": getSpiedPlayerContributions(spyJPGame1Contribution),
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100.04,
                            "progressive": 0.04,
                            "seed": 0.02
                        },
                        "small": {
                            "amount": 10,
                            "progressive": 0,
                            "seed": -29.99
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
                {
                    "jackpotId": jpInstance2.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "contributions": getSpiedPlayerContributions(spyJPGame2Contribution),
                    "pools": {
                        "large": {
                            "amount": 1000.07,
                            "progressive": 0.07,
                            "seed": 0.03
                        },
                        "medium": {
                            "amount": 100,
                            "progressive": 0,
                            "seed": -299.98
                        },
                        "small": {
                            "amount": 10.03,
                            "progressive": 0.03,
                            "seed": 0.01
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0.2
                },
            ]);

            let context = await contextService.find(jpInstance1.id, auth, trxId);
            expect(context).to.deep.equal({
                "jackpotId": jpInstance1.id,
                "transactionId": trxId,
                "externalId": "123",
                "playerInfo": playerInfo,
                "roundId": "1",
                "status": 3,
                "version": CURRENT_JACKPOT_CONTEXT_VERSION,
                "result": {
                    "pool": "small",
                    "type": "win"
                },
                "win": [
                    {
                        "type": "player",
                        "amount": 10,
                        "exchangeRate": 1,
                        "playerAmount": 10,
                        "pool": "small",
                        "progressive": 0,
                        "seed": 10
                    }
                ]
            });
            context = await contextService.find(jpInstance2.id, auth, trxId);
            expect(context).to.deep.equal({
                "jackpotId": jpInstance2.id,
                "transactionId": trxId,
                "externalId": "123",
                "playerInfo": playerInfo,
                "roundId": "1",
                "status": 3,
                "version": CURRENT_JACKPOT_CONTEXT_VERSION,
                "result": {
                    "pool": "medium",
                    "type": "win"
                },
                "win": [
                    {
                        "type": "player",
                        "pool": "medium",
                        "amount": 100,
                        "playerAmount": 100,
                        "exchangeRate": 1,
                        "progressive": 0,
                        "seed": 100
                    }
                ]
            });

            // confirms win
            events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance1.id, roundId: "1" });
            expect(events).to.deep.equal({
                "events": [
                    {
                        "type": "win-confirm",
                        "jackpotType": "test",
                        "jackpotBaseType": "base",
                        "jackpotId": jpInstance1.id,
                        "transactionId": trxId,
                    },
                ],
            });

            context = await contextService.find(jpInstance1.id, auth, trxId);
            expect(context).to.not.exist;
        });

        it("retries to win jackpot on insufficient balance till attempts exhausted", async () => {
            jpCheckWin1.returns({
                type: "win",
                pool: "small",
                amount: 10.03,
            });
            jpGetWinPayouts1.returns([{
                "pool": "small",
                "amount": 10.03,
                "progressive": 0.03,
                "seed": 10
            }]);

            // contributes and wins
            const trxId = await wallet.generateTransactionId();

            await expect(jackpotService.contribute(auth, {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1"
            })).to.be.rejectedWith(Errors.ConcurrentJackpotWin);

            const context = await contextService.find(jpInstance1.id, auth, trxId);
            expect(context).to.not.exist;
        });
    });

    it("contributes and wins jackpot in different currency", async () => {
        jpCheckWin1.returns({
            type: "win",
            pool: "small",
        });

        // contributes and wins
        const trxId = await wallet.generateTransactionId();
        let events = await jackpotService.contribute(cnyPlayer, {
            externalId: "123",
            transactionId: trxId,
            amount: 73.13,
            roundId: "1"
        });

        expect(events.events.length).to.equal(3);
        expect(events.events).to.deep.include.members([
            {
                "type": "win",
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "transactionId": trxId,
                "pool": "small",
                "title": "friendly name",
                "amount": 79.87,
                "progressive": 0.21938999717394295,
                "seed": 79.65124277721728
            },
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame1Contribution, 0.125547319179561),
                "pools":
                    {
                        "large":
                            {
                                "amount":
                                    7965.63,
                                "progressive": 0.064268928,
                                "seed": 0.027543826
                            }
                        ,
                        "medium":
                            {
                                "amount":
                                    796.8,
                                "progressive": 0.036725102,
                                "seed": 0.018362551
                            }
                        ,
                        "small":
                            {
                                "amount":
                                    79.65,
                                "progressive": 0,
                                "seed": -9.990818725
                            }
                        ,
                    }
                ,
                "type":
                    "contribution",
                "totalContribution":
                    1.4626
            },
            {
                "jackpotId":
                jpInstance2.id,
                "contributions":
                    getSpiedPlayerContributions(spyJPGame2Contribution, 0.125547319179561),
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools":
                    {
                        "large":
                            {
                                "amount":
                                    7965.63,
                                "progressive": 0.064268928,
                                "seed": 0.027543826
                            }
                        ,
                        "medium":
                            {
                                "amount":
                                    796.8,
                                "progressive": 0.036725102,
                                "seed": 0.018362551
                            }
                        ,
                        "small":
                            {
                                "amount":
                                    79.87,
                                "progressive": 0.027543826,
                                "seed": 0.009181275
                            }
                        ,
                    }
                ,
                "type":
                    "contribution",
                "totalContribution":
                    1.4626
            }
            ,
        ])
        ;

        let context = await contextService.find(jpInstance1.id, cnyPlayer, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance1.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": cnyPlayerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "small",
                "type": "win"
            },
            "win": [
                {
                    "type": "player",
                    "amount": 10.027543826,
                    "exchangeRate": 0.125547319179561,
                    "playerAmount": 79.87,
                    "pool": "small",
                    "progressive": 0.027543826,
                    "seed": 10
                }
            ]
        });

        // confirms win
        events = await jackpotService.confirmWin(cnyPlayer, { transactionId: trxId, jackpotId: jpInstance1.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance1.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(jpInstance1.id, cnyPlayer, trxId);
        expect(context).to.not.exist;
    });

    it("retries winning contribution", async () => {
        jpCheckWin1.returns({
            type: "win",
            pool: "small",
        });
        jpCheckWin2.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        const contribution = {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        };

        const expectedContext = {
            "jackpotId": jpInstance1.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "small",
                "type": "win"
            },
            "win": [
                {
                    "type": "player",
                    "amount": 10.03,
                    "exchangeRate": 1,
                    "playerAmount": 10.03,
                    "pool": "small",
                    "progressive": 0.03,
                    "seed": 10
                }
            ]
        };

        // contributes and wins
        let events = await jackpotService.contribute(auth, contribution);
        const expectedEvents = [
            {
                "type": "win",
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "transactionId": trxId,
                "pool": "small",
                "title": "friendly name",
                "amount": 10.03,
                "progressive": 0.03,
                "seed": 10
            },
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame1Contribution),
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10,
                        "progressive": 0,
                        "seed": -9.99
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
            {
                "jackpotId": jpInstance2.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame2Contribution),
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
        ];
        expect(events.events.length).to.equal(expectedEvents.length);
        expect(events.events).to.deep.include.members(expectedEvents);
        let context1 = await contextService.find(jpInstance1.id, auth, trxId);
        expect(context1).to.deep.equal(expectedContext);
        let context2 = await contextService.find(jpInstance2.id, auth, trxId);
        expect(context2).to.not.exist;

        const winTrx = await getLastTransaction();
        const contributes = [await getLastTransaction(), await getLastTransaction()];

        walletFindCommitted.withArgs(trxId, jpInstance1.internalId)
            .returns(contributes.find((data) => data.operation.operationId === jpInstance1.internalId));
        walletFindCommitted.withArgs(trxId, jpInstance2.internalId)
            .returns(contributes.find((data) => data.operation.operationId === jpInstance2.internalId));
        walletFindCommitted.withArgs(trxId, jpInstance1.internalId + 1000).returns(winTrx);

        // retries
        events = await jackpotService.contribute(auth, contribution);
        expect(events.events.length).to.equal(expectedEvents.length);
        expect(events.events).to.deep.include.members(expectedEvents);
        context1 = await contextService.find(jpInstance1.id, auth, trxId);
        expect(context1).to.deep.equal(expectedContext);
        context2 = await contextService.find(jpInstance2.id, auth, trxId);
        expect(context2).to.not.exist;
    });

    it("contributes and starts mini game", async () => {
        jpCheckWin1.returns({
            type: "start-mini-game",
        });

        jpMiniGame1.returns({
            type: "win",
            pool: "medium",
        });

        const trxId = await wallet.generateTransactionId();

        // contribute
        let events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events.events.length).to.equal(3);
        expect(events.events).to.deep.include.members([
            {
                "gameData": undefined,
                "type": "start-mini-game",
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "transactionId": trxId,
            },
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame1Contribution),
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
            {
                "jackpotId": jpInstance2.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame2Contribution),
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
        ]);

        let context = await contextService.find(jpInstance1.id, auth, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance1.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 1,
            "miniGame": {},
            "result": {
                "type": "start-mini-game"
            },
            "version": CURRENT_JACKPOT_CONTEXT_VERSION
        });

        // update mini game
        events = await jackpotService.updateMiniGame(auth, {
            transactionId: trxId,
            jackpotId: jpInstance1.id,
            roundId: "1"
        });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win",
                    "jackpotId": jpInstance1.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "amount": 100.04,
                    "progressive": 0.04,
                    "seed": 100,
                    "pool": "medium",
                    "title": "medium"
                },
                {
                    "type": "end-mini-game",
                    "jackpotId": jpInstance1.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(jpInstance1.id, auth, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance1.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "medium",
                "type": "win"
            },
            "win": [
                {
                    "type": "player",
                    "pool": "medium",
                    "amount": 100.04,
                    "playerAmount": 100.04,
                    "exchangeRate": 1,
                    "progressive": 0.04,
                    "seed": 100
                }
            ]
        });

        // confirms win
        events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance1.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance1.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(jpInstance1.id, auth, trxId);
        expect(context).to.not.exist;

        // jackpot updated
        const ticker = await jackpotService.getTicker(auth, { exchangeRates: { "EUR": 1 } });
        expect(ticker.length).to.equal(2);
        expect(ticker).to.deep.include.members([
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": -99.98
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
            },
            {
                "jackpotId": jpInstance2.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
            },
        ]);
    });

    it("retries to update mini game", async () => {
        jpCheckWin1.returns({
            type: "start-mini-game",
        });

        jpMiniGame1.returns({
            type: "win",
            pool: "medium",
        });

        const trxId = await wallet.generateTransactionId();

        // contribute
        let events = await jackpotService.contribute(auth, {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        });

        expect(events.events.length).to.equal(3);
        expect(events.events).to.deep.include.members([
            {
                "gameData": undefined,
                "type": "start-mini-game",
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "transactionId": trxId,
            },
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame1Contribution),
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
            {
                "jackpotId": jpInstance2.id,
                "contributions": getSpiedPlayerContributions(spyJPGame2Contribution),
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
        ]);

        let context = await contextService.find(jpInstance1.id, auth, trxId);
        expect(context).to.deep.equal({
            "jackpotId": jpInstance1.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 1,
            "miniGame": {},
            "result": {
                "type": "start-mini-game"
            },
            "version": CURRENT_JACKPOT_CONTEXT_VERSION
        });

        // update mini game
        const expectedMGEvents = {
            "events": [
                {
                    "type": "win",
                    "jackpotId": jpInstance1.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                    "amount": 100.04,
                    "progressive": 0.04,
                    "seed": 100,
                    "pool": "medium",
                    "title": "medium",
                },
                {
                    "type": "end-mini-game",
                    "jackpotId": jpInstance1.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        };
        const expectedContext = {
            "jackpotId": jpInstance1.id,
            "transactionId": trxId,
            "externalId": "123",
            "playerInfo": playerInfo,
            "roundId": "1",
            "status": 3,
            "version": CURRENT_JACKPOT_CONTEXT_VERSION,
            "result": {
                "pool": "medium",
                "type": "win"
            },
            "win": [
                {
                    "type": "player",
                    "pool": "medium",
                    "amount": 100.04,
                    "playerAmount": 100.04,
                    "exchangeRate": 1,
                    "progressive": 0.04,
                    "seed": 100
                }
            ]
        };

        // update #1
        events = await jackpotService.updateMiniGame(auth, {
            transactionId: trxId,
            jackpotId: jpInstance1.id, roundId: "1"
        });
        expect(events).to.deep.equal(expectedMGEvents);
        context = await contextService.find(jpInstance1.id, auth, trxId);
        expect(context).to.deep.equal(expectedContext);

        const winTrx = await getLastTransaction();
        walletFindCommitted.returns(winTrx);

        // update #2
        events = await jackpotService.updateMiniGame(auth, {
            transactionId: trxId,
            jackpotId: jpInstance1.id,
            roundId: "1"
        });
        expect(events).to.deep.equal(expectedMGEvents);
        context = await contextService.find(jpInstance1.id, auth, trxId);
        expect(context).to.deep.equal(expectedContext);

        // confirms win
        events = await jackpotService.confirmWin(auth, { transactionId: trxId, jackpotId: jpInstance1.id, roundId: "1" });
        expect(events).to.deep.equal({
            "events": [
                {
                    "type": "win-confirm",
                    "jackpotId": jpInstance1.id,
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "transactionId": trxId,
                },
            ],
        });

        context = await contextService.find(jpInstance1.id, auth, trxId);
        expect(context).to.not.exist;

        // jackpot updated
        const ticker = await jackpotService.getTicker(auth, { exchangeRates: { "EUR": 1 } });
        expect(ticker.length).to.equal(2);
        expect(ticker).to.deep.include.members([
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100,
                        "progressive": 0,
                        "seed": -99.98
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
            },
            {
                "jackpotId": jpInstance2.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
            },
        ]);
    });

    it("retries contribution and cannot win", async () => {
        jpCheckWin1.returns(undefined);
        jpCheckWin2.returns(undefined);

        const trxId = await wallet.generateTransactionId();
        const contribution = {
            externalId: "123",
            transactionId: trxId,
            amount: 10,
            roundId: "1"
        };

        const events1 = await jackpotService.contribute(auth, contribution);
        const expectedEvents = [
            {
                "jackpotId": jpInstance1.id,
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "contributions": getSpiedPlayerContributions(spyJPGame1Contribution),
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
            {
                "jackpotId": jpInstance2.id,
                "contributions": getSpiedPlayerContributions(spyJPGame1Contribution),
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "large": {
                        "amount": 1000.07,
                        "progressive": 0.07,
                        "seed": 0.03
                    },
                    "medium": {
                        "amount": 100.04,
                        "progressive": 0.04,
                        "seed": 0.02
                    },
                    "small": {
                        "amount": 10.03,
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                },
                "type": "contribution",
                "totalContribution": 0.2
            },
        ];
        expect(events1.events.length).to.equal(2);
        expect(events1.events).to.deep.include.members(expectedEvents);

        const trx2 = await getLastTransaction();
        const trx1 = await getLastTransaction();
        walletFindCommitted.withArgs().returns(trx1);
        walletFindCommitted.onSecondCall().returns(trx2);

        const events2 = await jackpotService.contribute(auth, contribution);
        expect(events2.events.length).to.equal(2);
        expect(events2.events).to.deep.include.members(expectedEvents);
    });

    it("cannot win if contribution is zero", async () => {
        const omrPlayer: AuthRequest = {
            playerCode: "PL-CODE",
            brandId: 1,
            region: "eu",
            jackpotIds: [jpInstance1.id],
            currency: "OMR",
            gameCode: "test"
        };

        const trxId = await wallet.generateTransactionId();
        const contribution = {
            externalId: "123",
            transactionId: trxId,
            amount: 0,
            roundId: "1"
        };

        const events = await jackpotService.contribute(omrPlayer, contribution);
        const expectedEvents = {
            "events": [
                {
                    "jackpotId": jpInstance1.id,
                    "contributions": getSpiedPlayerContributions(spyJPGame1Contribution),
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "large": {
                            "amount": 441.38,
                            "progressive": 0,
                            "seed": 0
                        },
                        "medium": {
                            "amount": 44.138,
                            "progressive": 0,
                            "seed": 0
                        },
                        "small": {
                            "amount": 4.413,
                            "progressive": 0,
                            "seed": 0
                        },
                    },
                    "type": "contribution",
                    "totalContribution": 0
                },
            ],
        };

        expect(events).to.deep.equal(expectedEvents);

        // verify we skipped jackpot win probability check
        expect(jpCheckWin1.notCalled).to.be.true;
    });

});
