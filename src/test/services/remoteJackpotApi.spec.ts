import { expect, use } from "chai";
import { JackpotRegion } from "../../skywind/api/model";
import { RemoteJackpotApiCache } from "../../skywind/services/remoteJackpotApi";

use(require("chai-as-promised"));

describe("Remote Jackpot Api", () => {

    const region1: JackpotRegion = {
        code: "eu",
        url: "http://jpn.eu"
    };
    const region2: JackpotRegion = {
        code: "asia",
        url: "http://jpn.asia"
    };

    it("created", async() => {
        const remoteApi1 = RemoteJackpotApiCache.getRemoteJackpotApi(region1);
        expect(remoteApi1).not.to.be.undefined;

        const remoteApi2 = RemoteJackpotApiCache.getRemoteJackpotApi(region2);
        expect(remoteApi2).not.to.be.undefined;

        expect(remoteApi1).not.equal(remoteApi2);
    });

    it("cached", async() => {
        const remoteApi1 = RemoteJackpotApiCache.getRemoteJackpotApi(region1);
        expect(remoteApi1).not.to.be.undefined;

        const remoteApi1Cached = RemoteJackpotApiCache.getRemoteJackpotApi(region1);
        expect(remoteApi1Cached).to.equal(remoteApi1);
    });

    it("invalidated", async() => {
        const remoteApi1 = RemoteJackpotApiCache.getRemoteJackpotApi(region1);
        expect(remoteApi1).not.to.be.undefined;

        const remoteApi1Cached = RemoteJackpotApiCache.getRemoteJackpotApi({ ...region1, url: "http://newjpn.com" });
        expect(remoteApi1Cached).not.to.equal(remoteApi1);
    });
});
