import { expect, use } from "chai";
import { JackpotWallet } from "../../skywind/modules/jackpotWallet";
import {
    createTestJPGame,
    flush, JP_WITH_DYNAMIC_CONTRIBUTION, TestJPGame,
} from "../helpers";
import { stub, SinonStub } from "sinon";
import JackpotInstanceService from "../../skywind/services/jackpot.instance";
import * as gameService from "../../skywind/services/game.service";
import { JackpotService, TransferPoolRequest } from "../../skywind/services/jackpot.service";
import { AuthRequest } from "@skywind-group/sw-jpn-core";
import wallet from "../../skywind/services/wallet.service";
import contextService from "../../skywind/services/jackpot.context";
import { init as initCurrencyRates } from "../../skywind/services/currency.service";
import {
    DuplicateTransactionError, InsufficientJackpotBalance, JpFeatureForbidden,
    ValidationError
} from "../../skywind/errors";
import { BaseWalletParams } from "../../skywind/modules/walletParams";

use(require("chai-as-promised"));

describe("Jackpot Service With Transfer", () => {

    const jpInstance = JP_WITH_DYNAMIC_CONTRIBUTION;
    const playerInfo: AuthRequest = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        jackpotIds: [jpInstance.id],
        currency: "EUR",
        gameCode: "test"
    };
    const walletParams: BaseWalletParams = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        playerCurrency: "EUR",
        gameCode: "test",
        gameId: "test",
        roundId: "1"
    };
    let jpGame;
    const jackpotService: JackpotService = new JackpotService(JackpotInstanceService, wallet, contextService);

    let lookup: SinonStub;
    let load: SinonStub;
    let jpCheckWin: SinonStub;
    let jpMiniGame: SinonStub;
    let jpWinJackpot: SinonStub;

    before(async() => {
        lookup = stub(JackpotInstanceService, "findInternal");
        load = stub(gameService, "load");
        await initCurrencyRates();
    });

    beforeEach(async() => {
        await flush();

        jpGame = await createTestJPGame(new JackpotWallet(walletParams, jpInstance, 1, wallet));

        jpCheckWin = stub(jpGame, "checkWin");
        jpMiniGame = stub(jpGame, "winMiniGame");
        jpWinJackpot = stub(jpGame, "winJackpot");

        lookup.resetBehavior();
        lookup.returns(jpInstance);
        load.resetBehavior();
        load.returns(jpGame);
        jpCheckWin.resetHistory();
        jpMiniGame.resetHistory();
        jpWinJackpot.resetHistory();
    });

    after(() => {
        lookup.restore();
        load.restore();
        jpCheckWin.restore();
        jpMiniGame.restore();
        jpWinJackpot.restore();
    });

    async function poolTransfer(fromPoolId: string, toPoolId: string, amount: number, init: boolean = true,
                                transactionId?: string): Promise<any> {

        const jackpotModule = new JackpotWallet(walletParams, jpInstance, 1, wallet);

        if (init) {
            const trxId = await wallet.generateTransactionId();

            await jackpotService.contribute(playerInfo, {
                externalId: "123",
                transactionId: trxId,
                amount: 10000000,
                exchangeRate: 1,
                roundId: "1"
            });

            const jpInfo1 = await jackpotModule.getTicker(true);
            expect(jpInfo1).deep.equal({
                "currency": "EUR",
                "id": "JP-TEST",
                "pools": {
                    "large": {
                        "progressive": 70000,
                        "seed": 30000,
                    },
                    "medium": {
                        "progressive": 40000,
                        "seed": 20000,
                    },
                    "small": {
                        "progressive": 30000,
                        "seed": 10000,
                    }
                },
                "seqId": 1
            });
        }

        transactionId = transactionId || (await jackpotService.getTransferTransactionId()).transactionId;

        const request: TransferPoolRequest = {
            fromPoolId: fromPoolId,
            toPoolId: toPoolId,
            transactionId: transactionId,
            amount: amount
        };

        try {
            await jackpotService.transferProgressive(playerInfo, request);
        } catch (error) {
            if (error instanceof DuplicateTransactionError) {
                // Duplicating transaction, It's Ok -  continue work.
            } else {
                return Promise.reject(error);
            }
        }

        return jackpotModule.getTicker(true);

    }

    it("Transfer fixed amount progressive from pool to pool - positive", async() => {
        const result = await poolTransfer("large", "medium", 10000);
        expect(result).deep.equal({
            "currency": "EUR",
            "id": "JP-TEST",
            "pools": {
                "large": {
                    "progressive": 60000,
                    "seed": 30000,
                },
                "medium": {
                    "progressive": 50000,
                    "seed": 20000,
                },
                "small": {
                    "progressive": 30000,
                    "seed": 10000,
                }
            },
            "seqId": 2
        });
    });

    it("Transfer all predefined amount progressive from pool to pool - positive", async() => {
        const result = await poolTransfer("large", "medium", 70000);
        expect(result).deep.equal({
            "currency": "EUR",
            "id": "JP-TEST",
            "pools": {
                "large": {
                    "progressive": 0,
                    "seed": 30000,
                },
                "medium": {
                    "progressive": 110000,
                    "seed": 20000,
                },
                "small": {
                    "progressive": 30000,
                    "seed": 10000,
                }
            },
            "seqId": 2
        });
    });

    it("Transfer all amount progressive from pool to pool - positive", async() => {
        const result = await poolTransfer("large", "medium", undefined);
        expect(result).deep.equal({
            "currency": "EUR",
            "id": "JP-TEST",
            "pools": {
                "large": {
                    "progressive": 0,
                    "seed": 30000,
                },
                "medium": {
                    "progressive": 110000,
                    "seed": 20000,
                },
                "small": {
                    "progressive": 30000,
                    "seed": 10000,
                }
            },
            "seqId": 2
        });
    });

    it("Transfer fixed amount and then return all back - positive", async() => {
        const result1 = await poolTransfer("large", "medium", 10000);
        expect(result1).deep.equal({
            "currency": "EUR",
            "id": "JP-TEST",
            "pools": {
                "large": {
                    "progressive": 60000,
                    "seed": 30000,
                },
                "medium": {
                    "progressive": 50000,
                    "seed": 20000,
                },
                "small": {
                    "progressive": 30000,
                    "seed": 10000,
                }
            },
            "seqId": 2
        });

        const result2 = await poolTransfer("medium", "large", undefined, false);

        expect(result1.pools.large.progressive + result1.pools.medium.progressive).deep.equal(110000);
        expect(result2.pools.large.progressive + result2.pools.medium.progressive).deep.equal(110000);

        expect(result2).deep.equal({
            "currency": "EUR",
            "id": "JP-TEST",
            "pools": {
                "large": {
                    "progressive": 110000,
                    "seed": 30000,
                },
                "medium": {
                    "progressive": 0,
                    "seed": 20000,
                },
                "small": {
                    "progressive": 30000,
                    "seed": 10000,
                }
            },
            "seqId": 3
        });
    });

    it("Transfer insufficient amount progressive from pool to pool - negative", async() => {
        await expect(poolTransfer("large", "medium", 70001)).to
            .be.rejectedWith(InsufficientJackpotBalance);
    });

    it("Zero amount progressive not affect on pools", async() => {
        const result = await poolTransfer("large", "medium", 0);
        expect(result).deep.equal({
            "currency": "EUR",
            "id": "JP-TEST",
            "pools": {
                "large": {
                    "progressive": 70000,
                    "seed": 30000,
                },
                "medium": {
                    "progressive": 40000,
                    "seed": 20000,
                },
                "small": {
                    "progressive": 30000,
                    "seed": 10000,
                }
            },
            "seqId": 2
        });
    });

    it("Negative amount progressive - negative", async() => {
        await expect(poolTransfer("large", "medium", -1)).to.be.rejectedWith(ValidationError);
    });

    it("Duplicate transaction - positive", async() => {
        const transactionId = (await jackpotService.getTransferTransactionId()).transactionId;
        const result1 = await poolTransfer("large", "medium", 10000, true, transactionId);

        const expectedResult = {
            "currency": "EUR",
            "id": "JP-TEST",
            "pools": {
                "large": {
                    "progressive": 60000,
                    "seed": 30000,
                },
                "medium": {
                    "progressive": 50000,
                    "seed": 20000,
                },
                "small": {
                    "progressive": 30000,
                    "seed": 10000,
                }
            },
            "seqId": 2
        };

        expect(result1).deep.equal(expectedResult);

        // Amount should't affect on duplicate transaction.
        const result2 = await poolTransfer("large", "medium", 11111, false, transactionId);
        expect(result2).deep.equal(expectedResult);
    });

    it("Concurrently transfer all amount - positive", async() => {
        const transactionId1 = (await jackpotService.getTransferTransactionId()).transactionId;
        const transactionId2 = (await jackpotService.getTransferTransactionId()).transactionId;

        // Init progressive pools. (Zero transfer is ok)
        expect(await poolTransfer("large", "medium", 0)).deep.equal({
            "currency": "EUR",
            "id": "JP-TEST",
            "pools": {
                "large": {
                    "progressive": 70000,
                    "seed": 30000,
                },
                "medium": {
                    "progressive": 40000,
                    "seed": 20000,
                },
                "small": {
                    "progressive": 30000,
                    "seed": 10000,
                }
            },
            "seqId": 2
        });

        // Make 2 parallel requests
        const result = await Promise.all([
            poolTransfer("large", "medium", undefined, false, transactionId1),
            poolTransfer("large", "medium", undefined, false, transactionId2)
        ]);

        expect(result[0]).deep.equal(result[1]);
        expect(result[0]).deep.equal({
            "currency": "EUR",
            "id": "JP-TEST",
            "pools": {
                "large": {
                    "progressive": 0,
                    "seed": 30000,
                },
                "medium": {
                    "progressive": 110000,
                    "seed": 20000,
                },
                "small": {
                    "progressive": 30000,
                    "seed": 10000,
                }
            },
            "seqId": 3
        });

    });

    it("Feature restriction", async() => {

        jpInstance.definition.features = undefined;

        const jackpotModule = new JackpotWallet(walletParams, jpInstance, 1, wallet);

        const jpInfo1 = await jackpotModule.getTicker();
        expect(jpInfo1).deep.equal({
            "currency": "EUR",
            "id": "JP-TEST",
            "pools": {
                "large": {
                    "progressive": 0,
                    "seed": 0,
                },
                "medium": {
                    "progressive": 0,
                    "seed": 0,
                },
                "small": {
                    "progressive": 0,
                    "seed": 0,
                }
            },
            "seqId": 0
        });

        const transactionId = (await jackpotService.getTransferTransactionId()).transactionId;

        const request: TransferPoolRequest = {
            fromPoolId: "large",
            toPoolId: "small",
            transactionId: transactionId,
            amount: 1
        };

        await expect(jackpotService.transferProgressive(playerInfo, request)).to.be.rejectedWith(JpFeatureForbidden);

        const transactionId2 = (await jackpotService.getTransferTransactionId()).transactionId;

        await expect(jackpotService.poolDeposit(playerInfo, "small",
            { transactionId: transactionId2, seed: 100 })).to.be.rejectedWith(JpFeatureForbidden);
    });

});
