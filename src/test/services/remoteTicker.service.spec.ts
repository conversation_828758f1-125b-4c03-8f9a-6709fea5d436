import { expect, use } from "chai";
import { createTestJPGame, flush, flushDb, hGetAll, JP_WITH_DYNAMIC_CONTRIBUTION } from "../helpers";
import { SinonFakeTimers, SinonStub, stub, useFakeTimers, createSandbox, SinonSandbox } from "sinon";
import JackpotInstanceService from "../../skywind/services/jackpot.instance";
import * as gameService from "../../skywind/services/game.service";
import { JackpotWallet } from "../../skywind/modules/jackpotWallet";
import wallet from "../../skywind/services/wallet.service";
import { RemoteTickerService } from "../../skywind/services/remoteTicker.service";
import * as request from "request";
import { remoteTickerPool } from "../../skywind/storage/redis";
import { JackpotRegion } from "../../skywind/api/model";
import JackpotRegionService from "../../skywind/services/regionService";
import config from "../../skywind/config";
import { sleep } from "@skywind-group/sw-utils";
import { RemoteJackpotWalletError } from "../../skywind/errors";
import { BaseWalletParams } from "../../skywind/modules/walletParams";
import * as superagent from "superagent";

use(require("chai-as-promised"));

describe("Remote Ticker service", () => {
    const region: JackpotRegion = {
        code: "eu",
        url: "http://jpn.eu",
        secureOptions: { ca: "rootCA" }
    };
    const jpInstance = { ...JP_WITH_DYNAMIC_CONTRIBUTION, regionCode: "eu", region };
    const walletParams: BaseWalletParams = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        playerCurrency: "EUR",
        gameCode: "test",
        gameId: "test",
        roundId: "1"
    };
    let jpGame;

    let lookup: SinonStub;
    let load: SinonStub;
    let getRemoteTicker: SinonStub;
    let clock: SinonFakeTimers;

    before(async () => {
        lookup = stub(JackpotInstanceService, "findInternal");
        load = stub(gameService, "load");
        getRemoteTicker = stub(request, "get");
        await flushDb();
        await JackpotRegionService.create(region);

    });

    beforeEach(async () => {
        await flush();

        const jpModule = new JackpotWallet(walletParams, jpInstance, 1, wallet);

        jpGame = await createTestJPGame(jpModule);

        lookup.resetBehavior();
        lookup.returns(jpInstance);
        load.resetBehavior();
        load.returns(jpGame);
        clock = useFakeTimers();
        getRemoteTicker.resetHistory();
    });

    afterEach(() => {
        clock.restore();
    });

    after(() => {
        lookup.restore();
        load.restore();
        clock.restore();
        getRemoteTicker.restore();
    });

    it("returns new ticker and update db", async () => {
        const expected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 2
        };
        const ts = 111;
        clock.setSystemTime(ts);
        getRemoteTicker.yields(null,
            { statusCode: 200 }, expected);
        const service = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);
        const ticker = await service.getTicker(jpInstance.id);
        expect(ticker).deep.equal(ticker);
        expect(getRemoteTicker.callCount).equal(1);
        delete getRemoteTicker.args[0][0].headers;
        delete getRemoteTicker.args[0][0].agent;
        expect(getRemoteTicker.args[0][0]).deep.equal({
            baseUrl: "http://jpn.eu",
            url: "/api/v2/jpn/remote/ticker",
            json: true,
            ca: "rootCA",
            qs: { jackpotId: "JP-TEST" }
        });
        const remoteTicker = await hGetAll(remoteTickerPool.get(), "remote-ticker:JP-TEST");
        expect(remoteTicker).deep.equals({
            "isDisabled": "0",
            "seqId": "2",
            "ts": ts.toString(),
            "small:progressive": "0",
            "small:seed": "0",
            "medium:progressive": "0",
            "medium:seed": "0",
            "large:progressive": "0",
            "large:seed": "0",
        });
    });

    it("skip request of new ticker, get the old one", async () => {
        const expected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 2,
            isDisabled: false
        };
        const ts = 111;
        clock.setSystemTime(ts);
        getRemoteTicker.yields(null,
            { statusCode: 200 }, expected);
        const service = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);
        await service.getTicker(jpInstance.id);
        getRemoteTicker.yields(null,
            { statusCode: 200 }, {
                id: "JP-TEST",
                currency: "EUR",
                pools:
                    {
                        small: { seed: 0, progressive: 0 },
                        medium: { seed: 0, progressive: 0 },
                        large: { seed: 0, progressive: 0 }
                    },
                seqId: 4
            });
        const ticker = await service.getTicker(jpInstance.id);
        expect(ticker).deep.equal(expected);
        expect(getRemoteTicker.callCount).equal(1);
        delete getRemoteTicker.args[0][0].headers;
        delete getRemoteTicker.args[0][0].agent;
        expect(getRemoteTicker.args[0][0]).deep.equal({
            baseUrl: "http://jpn.eu",
            url: "/api/v2/jpn/remote/ticker",
            json: true,
            ca: "rootCA",
            qs: { jackpotId: "JP-TEST" }
        });
        const remoteTicker = await hGetAll(remoteTickerPool.get(), "remote-ticker:JP-TEST");
        expect(remoteTicker).deep.equals({
            "seqId": "2",
            "isDisabled": "0",
            "ts": ts.toString(),
            "small:progressive": "0",
            "small:seed": "0",
            "medium:progressive": "0",
            "medium:seed": "0",
            "large:progressive": "0",
            "large:seed": "0",
        });
    });

    it("update ticker with new request in background", async () => {
        const expected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 2,
            isDisabled: false
        };
        let ts = 111;
        clock.setSystemTime(ts);
        getRemoteTicker.yields(null,
            { statusCode: 200 }, expected);
        const service = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);
        await service.getTicker(jpInstance.id);
        const newExpected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 4,
            isDisabled: false
        };
        getRemoteTicker.yields(null,
            { statusCode: 200 }, newExpected);
        ts = ts + config.remoteTicker.forceRefreshTimeout + 1;
        clock.setSystemTime(ts);
        let ticker = await service.getTicker(jpInstance.id);
        expect(ticker).deep.equal(expected);
        clock.restore();
        await sleep(100);
        ticker = await service.getTicker(jpInstance.id);
        expect(ticker).deep.equal(newExpected);
        expect(getRemoteTicker.callCount).equal(2);
        delete getRemoteTicker.args[0][0].headers;
        delete getRemoteTicker.args[0][0].agent;
        expect(getRemoteTicker.args[0][0]).deep.equal({
            baseUrl: "http://jpn.eu",
            url: "/api/v2/jpn/remote/ticker",
            json: true,
            ca: "rootCA",
            qs: { jackpotId: "JP-TEST" }
        });
        const remoteTicker = await hGetAll(remoteTickerPool.get(), "remote-ticker:JP-TEST");
        delete remoteTicker.ts;
        expect(remoteTicker).deep.equals({
            "isDisabled": "0",
            "large:progressive": "0",
            "large:seed": "0",
            "medium:progressive": "0",
            "medium:seed": "0",
            "seqId": "4",
            "small:progressive": "0",
            "small:seed": "0"
        });
    });

    it("failed to return ticker - CantGameTicker", async () => {
        getRemoteTicker.yields(null,
            { statusCode: 500 }, { code: 300, message: "SystemError" });
        const service = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);
        await expect(service.getTicker(jpInstance.id)).to.be.rejectedWith(RemoteJackpotWalletError);
    });

    it("updates ticker value", async () => {
        const expected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 2
        };
        const ts = 111;
        clock.setSystemTime(ts);
        const service = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);

        let ticker = await service.updateTicker(expected);
        expect(ticker).deep.equal(expected);
        let remoteTicker = await hGetAll(remoteTickerPool.get(), "remote-ticker:JP-TEST");
        expect(remoteTicker).deep.equals({
            "seqId": "2",
            "isDisabled": "0",
            "ts": ts.toString(),
            "small:progressive": "0",
            "small:seed": "0",
            "medium:progressive": "0",
            "medium:seed": "0",
            "large:progressive": "0",
            "large:seed": "0",
        });

        const newExpected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 4
        };
        ticker = await service.updateTicker(newExpected);
        expect(ticker).deep.equal(newExpected);
        remoteTicker = await hGetAll(remoteTickerPool.get(), "remote-ticker:JP-TEST");
        expect(remoteTicker).deep.equals({
            "isDisabled": "0",
            "large:progressive": "0",
            "large:seed": "0",
            "medium:progressive": "0",
            "medium:seed": "0",
            "seqId": "4",
            "small:progressive": "0",
            "small:seed": "0",
            "ts": ts.toString()
        });
    });

    it("skip updating ticker value", async () => {
        const expected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 4,
            isDisabled: false
        };
        const ts = 111;
        clock.setSystemTime(ts);
        const service = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);

        let ticker = await service.updateTicker(expected);
        expect(ticker).deep.equal(expected);
        let remoteTicker = await hGetAll(remoteTickerPool.get(), "remote-ticker:JP-TEST");
        expect(remoteTicker).deep.equals({
            "isDisabled": "0",
            "seqId": "4",
            "ts": ts.toString(),
            "small:progressive": "0",
            "small:seed": "0",
            "medium:progressive": "0",
            "medium:seed": "0",
            "large:progressive": "0",
            "large:seed": "0",
        });

        const newExpected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 4
        };
        ticker = await service.updateTicker(newExpected);
        expect(ticker).deep.equal(expected);
        remoteTicker = await hGetAll(remoteTickerPool.get(), "remote-ticker:JP-TEST");
        expect(remoteTicker).deep.equals({
            "isDisabled": "0",
            "seqId": "4",
            "ts": ts.toString(),
            "small:progressive": "0",
            "small:seed": "0",
            "medium:progressive": "0",
            "medium:seed": "0",
            "large:progressive": "0",
            "large:seed": "0",
        });
    });

    it("disable/enable ticker value", async () => {
        const expected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 10,
            isDisabled: true

        };
        const ts = 111;
        clock.setSystemTime(ts);
        const service = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);

        let ticker = await service.updateTicker(expected);
        expect(ticker).deep.equal(expected);
        let remoteTicker = await hGetAll(remoteTickerPool.get(), "remote-ticker:JP-TEST");
        expect(remoteTicker).deep.equals({
            "seqId": "10",
            "isDisabled": "1",
            "ts": ts.toString(),
            "small:progressive": "0",
            "small:seed": "0",
            "medium:progressive": "0",
            "medium:seed": "0",
            "large:progressive": "0",
            "large:seed": "0",
        });

        const newExpected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 11,
            isDisabled: false
        };
        ticker = await service.updateTicker(newExpected);
        expect(ticker).deep.equal(newExpected);
        remoteTicker = await hGetAll(remoteTickerPool.get(), "remote-ticker:JP-TEST");
        expect(remoteTicker).deep.equals({
            "isDisabled": "0",
            "large:progressive": "0",
            "large:seed": "0",
            "medium:progressive": "0",
            "medium:seed": "0",
            "seqId": "11",
            "small:progressive": "0",
            "small:seed": "0",
            "ts": ts.toString()
        });
    });
});
describe("Remote Ticker service with superagent", () => {
    let sandbox: SinonSandbox;
    let superagentMock: any;
    const region: JackpotRegion = {
        code: "eu",
        url: "http://jpn.eu",
        secureOptions: { ca: "rootCA" }
    };
    const jpInstance = { ...JP_WITH_DYNAMIC_CONTRIBUTION, regionCode: "eu", region };
    const walletParams: BaseWalletParams = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        playerCurrency: "EUR",
        gameCode: "test",
        gameId: "test",
        roundId: "1"
    };
    let jpGame;

    let lookup: SinonStub;
    let load: SinonStub;
    let clock: SinonFakeTimers;

    before(async () => {
        sandbox = createSandbox();
        config.enableSuperagentLib = true;
        lookup = stub(JackpotInstanceService, "findInternal");
        load = stub(gameService, "load");
        await flushDb();
        await JackpotRegionService.create(region);
        superagentMock = {
            ...superagent,
            set: sandbox.stub().returnsThis(),
            ca: sandbox.stub().returnsThis(),
            key: sandbox.stub().returnsThis(),
            cert: sandbox.stub().returnsThis(),
            query: sandbox.stub().returnsThis(),
            agent: sandbox.stub().returnsThis(),
            send: sandbox.stub().returnsThis(),
            end: sandbox.stub(),
        };
        sandbox.stub(superagent, "get").returns(superagentMock);

    });

    beforeEach(async () => {
        await flush();

        const jpModule = new JackpotWallet(walletParams, jpInstance, 1, wallet);

        jpGame = await createTestJPGame(jpModule);

        lookup.resetBehavior();
        lookup.returns(jpInstance);
        load.resetBehavior();
        load.returns(jpGame);
        clock = useFakeTimers();
        superagentMock.end.resetHistory();
    });

    afterEach(() => {
        clock.restore();
    });

    after(() => {
        lookup.restore();
        load.restore();
        clock.restore();
        sandbox.restore();
    });

    it("returns new ticker and update db", async () => {
        const expected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 2
        };
        const ts = 111;
        clock.setSystemTime(ts);
        superagentMock.end.yields(null, { status: 200, body: expected });

        const service = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);
        const ticker = await service.getTicker(jpInstance.id);
        expect(ticker).deep.equal(ticker);
        expect(superagentMock.end.callCount).equal(1);

        const remoteTicker = await hGetAll(remoteTickerPool.get(), "remote-ticker:JP-TEST");
        expect(remoteTicker).deep.equals({
            "isDisabled": "0",
            "seqId": "2",
            "ts": ts.toString(),
            "small:progressive": "0",
            "small:seed": "0",
            "medium:progressive": "0",
            "medium:seed": "0",
            "large:progressive": "0",
            "large:seed": "0",
        });
    });

    it("skip request of new ticker, get the old one", async () => {
        const expected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 2,
            isDisabled: false
        };
        const ts = 111;
        clock.setSystemTime(ts);
        superagentMock.end.yields(null, { status: 200, body: expected });
        const service = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);
        await service.getTicker(jpInstance.id);

        superagentMock.end.yields(null, { status: 200, body: {
                id: "JP-TEST",
                currency: "EUR",
                pools:
                    {
                        small: { seed: 0, progressive: 0 },
                        medium: { seed: 0, progressive: 0 },
                        large: { seed: 0, progressive: 0 }
                    },
                seqId: 4
            } });

        const ticker = await service.getTicker(jpInstance.id);
        expect(ticker).deep.equal(expected);
        expect(superagentMock.end.callCount).equal(1);

        const remoteTicker = await hGetAll(remoteTickerPool.get(), "remote-ticker:JP-TEST");
        expect(remoteTicker).deep.equals({
            "seqId": "2",
            "isDisabled": "0",
            "ts": ts.toString(),
            "small:progressive": "0",
            "small:seed": "0",
            "medium:progressive": "0",
            "medium:seed": "0",
            "large:progressive": "0",
            "large:seed": "0",
        });
    });

    it("update ticker with new request in background", async () => {
        const expected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 2,
            isDisabled: false
        };
        let ts = 111;
        clock.setSystemTime(ts);
        superagentMock.end.yields(null, { status: 200, body: expected });

        const service = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);
        await service.getTicker(jpInstance.id);
        const newExpected = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { seed: 0, progressive: 0 },
                    medium: { seed: 0, progressive: 0 },
                    large: { seed: 0, progressive: 0 }
                },
            seqId: 4,
            isDisabled: false
        };
        superagentMock.end.yields(null, { status: 200, body: newExpected });

        ts = ts + config.remoteTicker.forceRefreshTimeout + 1;
        clock.setSystemTime(ts);
        let ticker = await service.getTicker(jpInstance.id);
        expect(ticker).deep.equal(expected);
        clock.restore();
        await sleep(100);
        ticker = await service.getTicker(jpInstance.id);
        expect(ticker).deep.equal(newExpected);
        expect(superagentMock.end.callCount).equal(2);

        const remoteTicker = await hGetAll(remoteTickerPool.get(), "remote-ticker:JP-TEST");
        delete remoteTicker.ts;
        expect(remoteTicker).deep.equals({
            "isDisabled": "0",
            "large:progressive": "0",
            "large:seed": "0",
            "medium:progressive": "0",
            "medium:seed": "0",
            "seqId": "4",
            "small:progressive": "0",
            "small:seed": "0"
        });
    });

    it("failed to return ticker - CantGameTicker", async () => {
        superagentMock.end.yields(null, { status: 500, body: { code: 300, message: "SystemError" } });

        const service = new RemoteTickerService(remoteTickerPool.get(), JackpotInstanceService);
        await expect(service.getTicker(jpInstance.id)).to.be.rejectedWith(RemoteJackpotWalletError);
    });
});
