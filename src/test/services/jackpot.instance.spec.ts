import { expect, use, should } from "chai";
import { stub, SinonStub } from "sinon";
import { delay, flush, flushDb, TestJPGame } from "../helpers";
import JackpotTypeService from "../../skywind/services/jackpot.type";
import JackpotInstanceService, { JackpotInstanceService as JPI } from "../../skywind/services/jackpot.instance";
import * as GameService from "../../skywind/services/game.service";
import {
    JackpotAuditType, JackpotDisableMode, JackpotInstance, JackpotRegion, JackpotType
} from "../../skywind/api/model";
import * as Errors from "../../skywind/errors";
import { init as initCurrencyRates } from "../../skywind/services/currency.service";
import {
    CurrencyNotFoundError,
    JackpotInstanceAlreadyExist,
    JackpotInstanceNotFound,
    ValidationError
} from "../../skywind/errors";
import JackpotRegionService from "../../skywind/services/regionService";
import * as request from "request";
import { JackpotAuditService } from "../../skywind/services/jackpotAudits";
import { get as getCache } from "../../skywind/services/jackpotCache";
import { JackpotWallet, JP_FEATURE_OVERRIDE_POOLS_BY_INSTANCE } from "../../skywind/modules/jackpotWallet";
import wallet from "../../skywind/services/wallet.service";
import { GameAction, JackpotDefinition, JackpotGame } from "@skywind-group/sw-jpn-core";

should();
use(require("chai-as-promised"));

describe("Jackpot Instance Service", () => {

    const type: JackpotType = {
        name: "test",
        baseType: "base",
        jpGameId: "test",
        definition: {
            currency: "EUR",
            list: [
                {
                    id: "pool",
                    seed: {
                        amount: 100,
                    },
                    contribution: [
                        {
                            seed: 0.01,
                            progressive: 0.02
                        }
                    ],
                }
            ],
        },
        configurable: true,
        overridable: true,
        canBeDisabled: true
    };

    const typeWithCap: JackpotType = {
        ...type,
        name: "testCap",
        supportsWinCap: true
    };
    const region: JackpotRegion = {
        code: "eu",
        url: "http://jpn.eu",
        secureOptions: { key: "key" }
    };
    const instance: JackpotInstance = {
        id: "testid",
        type: "test",
    };
    let jpGameExists: SinonStub;
    let jpGameLoad: SinonStub;
    let getExternalJackpots: SinonStub;

    const complexType: JackpotType = {
        name: "complexTest",
        baseType: "base",
        jpGameId: "complexTest",
        definition: {
            currency: "EUR",
            list: [
                {
                    id: "a",
                    seed: {
                        amount: 100,
                    },
                    contribution: [],
                },
                {
                    id: "b",
                    seed: {
                        amount: 1000,
                    },
                    contribution: [],
                },
                {
                    id: "c",
                    seed: {
                        amount: 10000,
                    },
                    contribution: [],
                }
            ],
        },
        configurable: true,
        overridable: true
    };
    const complexTypeForInstantJP: JackpotType = {
        name: "complexTestForInstantJP",
        baseType: "base",
        jpGameId: "complexTest",
        definition: {
            currency: "EUR",
            list: [
                {
                    id: "D",
                    seed: {
                        amount: 100,
                    },
                    contribution: [],
                },
                {
                    id: "C",
                    seed: {
                        amount: 1000,
                    },
                    contribution: [],
                },
                {
                    id: "B",
                    seed: {
                        amount: 10000,
                    },
                    contribution: [],
                },
                {
                    id: "A",
                    seed: {
                        amount: 100000,
                    },
                    contribution: [],
                }
            ],
        },
        configurable: true,
        overridable: true
    };
    const complexInstance: JackpotInstance = {
        id: "complexTestid",
        type: "complexTest",
    };
    const overridableType: JackpotType = {
        ...type,
        name: "overridable",
        definition: {
            ...type.definition,
            features: [JP_FEATURE_OVERRIDE_POOLS_BY_INSTANCE]
        }
    };

    let getRemoteTicker;
    let findOrCreateRemote;

    before(async () => {
        await flushDb();

        jpGameExists = stub(GameService, "exists");
        jpGameExists.returns(true);
        jpGameLoad = stub(GameService, "load").callsFake((jpGameId, jackpot) => {
            return new TestJPGame(jackpot);
        });
        getExternalJackpots = stub(JackpotInstanceService, "getExternalJackpots");
        await JackpotTypeService.create(type);
        await JackpotTypeService.create(typeWithCap);
        await JackpotTypeService.create(complexType);
        await JackpotTypeService.create(overridableType);

        await JackpotRegionService.create(region);

        getRemoteTicker = stub(request, "get");
        findOrCreateRemote = stub(request, "post");
    });

    after(() => {
        jpGameExists.restore();
        jpGameLoad.restore();
        getRemoteTicker.restore();
        findOrCreateRemote.restore();
        getExternalJackpots.restore();
    });

    beforeEach(async () => {
        await JackpotAuditService.model.truncate({ force: true });
        await JPI.model.truncate({ force: true });
        await flush();
        getCache().invalidateAll();
        getRemoteTicker.resetHistory();
        findOrCreateRemote.resetHistory();
        getExternalJackpots.resetBehavior();
    });

    async function verifyAudit(jackpotId, auditType: JackpotAuditType) {
        const audit = await new JackpotAuditService().findAll({ jackpotId, type: auditType });
        expect(audit.length).to.eq(1);
    }

    it("creates jackpot instance", async () => {
        const createdInstance = await JackpotInstanceService.create(instance);
        expect(createdInstance).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            isTest: null,
            definition: type.definition,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: createdInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("testid", JackpotAuditType.CREATE);
    });

    it("creates jackpot instance with region", async () => {
        findOrCreateRemote.yields(null, { statusCode: 200 }, {
            id: "testid",
            type: "test"
        });
        const instanceWithRegion: JackpotInstance = {
            id: "testid",
            type: "test",
            regionCode: region.code
        };
        const createdInstance = await JackpotInstanceService.create(instanceWithRegion);
        expect(createdInstance).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: type.definition,
            regionCode: region.code,
            isTest: null,
            disableMode: 0,
            isDisabled: false,
            isGlobal: true,
            isOwned: false,
            isLocal: false,
            createdAt: createdInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("testid", JackpotAuditType.CREATE);

        expect(findOrCreateRemote.args[0][0].json).to.deep.equal({
            id: "testid",
            type: "test",
            regionCode: undefined,
            isGlobal: true
        });
        expect(findOrCreateRemote.args[0][0].qs).to.deep.equal({
            autoCreate: false
        });
    });

    it("creates jackpot instance with region and auto create remote", async () => {
        findOrCreateRemote.yields(null, { statusCode: 200 }, {
            id: "testid",
            type: "test"
        });
        const instanceWithRegion: JackpotInstance = {
            id: "testid",
            type: "test",
            regionCode: region.code
        };
        const createdInstance = await JackpotInstanceService.create(instanceWithRegion, true);
        expect(createdInstance).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: type.definition,
            regionCode: region.code,
            isTest: null,
            disableMode: 0,
            isDisabled: false,
            isGlobal: true,
            isOwned: false,
            isLocal: false,
            createdAt: createdInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("testid", JackpotAuditType.CREATE);

        expect(findOrCreateRemote.args[0][0].json).to.deep.equal({
            id: "testid",
            type: "test",
            regionCode: undefined,
            isGlobal: true
        });
        expect(findOrCreateRemote.args[0][0].qs).to.deep.equal({
            autoCreate: true
        });
    });

    it("clone test jackpot instance", async () => {
        const instanceOrigin: JackpotInstance = {
            id: "testid",
            type: "test"
        };
        const createdInstance = await JackpotInstanceService.create(instanceOrigin);
        expect(createdInstance).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: type.definition,
            isTest: null,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: createdInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("testid", JackpotAuditType.CREATE);

        const testInstance = await JackpotInstanceService.findOrCreateTest("testid");
        expect(testInstance).to.deep.equal({
            internalId: testInstance.internalId,
            id: "testid_test",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: type.definition,
            isTest: true,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            createdFromId: "testid",
            migratedAt: null,
            precision: 1000000000,
            region: undefined,
            isOwned: false,
            isLocal: false,
            createdAt: testInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("testid_test", JackpotAuditType.CLONE);

        const testInstance2 = await JackpotInstanceService.findOrCreateTest("testid");
        expect(testInstance2).to.deep.equal(testInstance);
    });

    it("clone test jackpot instance - global", async () => {
        findOrCreateRemote.yields(null, { statusCode: 200 }, {
            id: "testid",
            type: "test"
        });
        const instanceWithRegion: JackpotInstance = {
            id: "testid",
            type: "test",
            regionCode: region.code
        };
        const createdInstance = await JackpotInstanceService.create(instanceWithRegion);
        expect(createdInstance).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: type.definition,
            regionCode: region.code,
            isTest: null,
            disableMode: 0,
            isDisabled: false,
            isGlobal: true,
            isOwned: false,
            isLocal: false,
            createdAt: createdInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("testid", JackpotAuditType.CREATE);

        const testInstance = await JackpotInstanceService.findOrCreateTest("testid");
        expect(testInstance).to.deep.equal({
            internalId: testInstance.internalId,
            id: "testid_test",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: type.definition,
            regionCode: region.code,
            isTest: true,
            disableMode: 0,
            isDisabled: false,
            isGlobal: true,
            createdFromId: "testid",
            migratedAt: null,
            precision: 1000000000,
            region: {
                code: "eu",
                secureOptions: {
                    "key": "key"
                },
                url: "http://jpn.eu"
            },
            isOwned: false,
            isLocal: false,
            createdAt: testInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("testid_test", JackpotAuditType.CLONE);

        const testInstance2 = await JackpotInstanceService.findOrCreateTest("testid");
        expect(testInstance2).to.deep.equal(testInstance);

        expect(findOrCreateRemote.args[1][0].json).to.deep.equal({
            id: "testid_test",
            type: "test",
            definition: undefined,
            isDisabled: false,
            disableMode: 0,
            isGlobal: true,
            isTest: true,
            isOwned: false,
            isLocal: false
        });
        expect(findOrCreateRemote.args[1][0].qs).to.deep.equal({
            autoCreate: true
        });
    });

    it("clone test jackpot instance - global failed", async () => {
        findOrCreateRemote.yields(null, { statusCode: 200 }, {
            id: "testid",
            type: "test",
        });

        const instanceWithRegion: JackpotInstance = {
            id: "testid",
            type: "test",
            regionCode: region.code
        };
        const createdInstance = await JackpotInstanceService.create(instanceWithRegion);
        expect(createdInstance).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: type.definition,
            regionCode: region.code,
            isTest: null,
            disableMode: 0,
            isDisabled: false,
            isGlobal: true,
            isOwned: false,
            isLocal: false,
            createdAt: createdInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("testid", JackpotAuditType.CREATE);

        findOrCreateRemote.yields(null, { statusCode: 404 }, {});

        await expect(JackpotInstanceService.findOrCreateTest("testid")).to.be.rejectedWith(Errors.ValidationError);
    });

    it("clone test jackpot instance failed", async () => {
        const createdInstance = await JackpotInstanceService.create(instance);
        expect(createdInstance).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: type.definition,
            isTest: null,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: createdInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("testid", JackpotAuditType.CREATE);

        const fakeTestInstance = await JackpotInstanceService.create({
            id: "testid_test",
            type: "test"
        });
        expect(fakeTestInstance).to.deep.equal({
            id: "testid_test",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: type.definition,
            isTest: null,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: fakeTestInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("testid_test", JackpotAuditType.CREATE);

        await expect(JackpotInstanceService.findOrCreateTest("testid")).to.be.rejectedWith(Errors.TestJackpotError);
    });

    it("updates jackpot instance as global", async () => {
        const instanceWithRegion: JackpotInstance = {
            id: "testid",
            type: "test"
        };
        const createdInstance = await JackpotInstanceService.create(instanceWithRegion);
        expect(createdInstance).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: type.definition,
            isTest: null,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: createdInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        let updatedInstance = await JackpotInstanceService.update("testid", {
            isGlobal: true
        } as any);
        expect(updatedInstance).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: type.definition,
            isTest: null,
            disableMode: 0,
            isDisabled: false,
            isGlobal: true,
            isOwned: false,
            isLocal: false,
            createdAt: updatedInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        updatedInstance = await JackpotInstanceService.update("testid", {
            isGlobal: false
        } as any);
        expect(updatedInstance).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: type.definition,
            isTest: null,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: updatedInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("testid", JackpotAuditType.CREATE);
    });

    it("creates jackpot instance owned by operator", async () => {
        const createdInstance = await JackpotInstanceService.create({
            id: "test-owned",
            type: "test",
            isOwned: true
        });
        expect(createdInstance).to.deep.equal({
            id: "test-owned",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            isTest: null,
            definition: type.definition,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: true,
            isLocal: false,
            createdAt: createdInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("test-owned", JackpotAuditType.CREATE);
    });

    it("creates jackpot instance without seed pot", async () => {
        const typeWtSeed: JackpotType = {
            name: "no-seed",
            baseType: "base",
            jpGameId: "test",
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        contribution: [],
                    }
                ],
            },
        };
        await JackpotTypeService.create(typeWtSeed);

        const createdInstance = await JackpotInstanceService.create({
            id: "no-seed-id",
            type: "no-seed"
        });
        expect(createdInstance).to.deep.equal({
            id: "no-seed-id",
            type: "no-seed",
            baseType: "base",
            jpGameId: typeWtSeed.jpGameId,
            definition: typeWtSeed.definition,
            isTest: null,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: createdInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("no-seed-id", JackpotAuditType.CREATE);
    });

    it("create jackpot instance with definition override", async () => {

        const created = await JackpotInstanceService.create({
            id: "testid",
            type: "test",
            baseType: "base",
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 1000,
                        },
                        contribution: [
                            {
                                seed: 0.1,
                                progressive: 0.2
                            }
                        ],
                        additionalProp: "prop"
                    }
                ],
            }
        });
        expect(created).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: "test",
            isTest: null,
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 1000,
                        },
                        contribution: [
                            {
                                seed: 0.1,
                                progressive: 0.2
                            }
                        ],
                        additionalProp: "prop"
                    }
                ],
            },
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: created.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("testid", JackpotAuditType.CREATE);

        const updated = await JackpotInstanceService.update(created.id, {
            definition: {
                list: [
                    {
                        id: "pool",
                        additionalProp: "prop1",
                        additionalPropX: "propX"
                    }
                ],
            }
        } as any);
        expect(updated).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: "test",
            isTest: null,
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 1000,
                        },
                        contribution: [
                            {
                                seed: 0.1,
                                progressive: 0.2
                            }
                        ],
                        additionalProp: "prop1",
                        additionalPropX: "propX"
                    }
                ],
            },
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: updated.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await verifyAudit("testid", JackpotAuditType.UPDATE);
    });

    it("create jackpot instance with invalid definition", async () => {

        const jpInstance = {
            id: "testid",
            type: "test",
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        contribution: [
                            {
                                seed: -0.1,
                                progressive: -0.2
                            }
                        ],
                    }
                ],
            }
        };

        await expect(JackpotInstanceService.create(jpInstance)).to.be.rejectedWith(Errors.ValidationError);
    });

    it("create jackpot instance with winCapping", async () => {

        const jpInstance = {
            id: "cap",
            type: "testCap",
            definition: {
                winCapping: 500,
                ...typeWithCap.definition
            }
        };

        const result = await JackpotInstanceService.create(jpInstance);

        expect(result).to.be.deep.equal({
            id: "cap",
            type: "testCap",
            baseType: "base",
            jpGameId: typeWithCap.jpGameId,
            isTest: null,
            definition: {
                ...typeWithCap.definition,
                winCapping: 500
            },
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: result.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });
    });

    it("create jackpot instance with wrong winCapping", async () => {

        const jpInstance = {
            id: "cap",
            type: "testCap",
            definition: {
                winCapping: -500,
                ...typeWithCap.definition
            }
        };

        await JackpotInstanceService.create(jpInstance).should.eventually.rejectedWith(Errors.ValidationError);
    });

    it("create jackpot instance for unsupported win capping", async () => {

        const jpInstance = {
            id: "cap",
            type: "test",
            definition: {
                winCapping: 500,
                ...type.definition
            }
        };

        await JackpotInstanceService.create(jpInstance).should.eventually.rejectedWith(Errors.ValidationError);
    });

    it("create jackpot instance with wrong currency - negative", async () => {

        const wrongJpInstance: JackpotInstance = {
            id: "testid",
            type: "test",
            definition: {
                currency: "FAKE",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 100,
                        },
                        contribution: [],
                    }
                ],
            }
        };
        await expect(JackpotInstanceService.create(wrongJpInstance)).to.be.rejectedWith(CurrencyNotFoundError);
    });

    it("create jackpot instance 2 times with the same id - negative", async () => {

        const jpInstance: JackpotInstance = {
            id: "testid",
            type: "test",
            definition: {
                currency: "USD",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 100,
                        },
                        contribution: [],
                    }
                ],
            }
        };
        await JackpotInstanceService.create(jpInstance);
        await expect(JackpotInstanceService.create(jpInstance)).to.be.rejectedWith(JackpotInstanceAlreadyExist);
    });

    it("create then delete and then create again jackpot instance", async () => {

        const jpInstance: JackpotInstance = {
            id: "testid",
            type: "test",
            definition: {
                currency: "USD",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 100,
                        },
                        contribution: [],
                    }
                ],
            }
        };
        const createdInstance = await JackpotInstanceService.create(jpInstance);
        await JackpotInstanceService.remove(createdInstance.id);

        await expect(JackpotInstanceService.find(createdInstance.id)).to.be
            .rejectedWith(JackpotInstanceNotFound);

        await expect(JackpotInstanceService.create(jpInstance)).to.be.rejectedWith(JackpotInstanceAlreadyExist);

        await verifyAudit("testid", JackpotAuditType.ARCHIVE);
    });

    it("disable/enable jackpot instance", async () => {
        const created = await JackpotInstanceService.create(instance);
        expect(created).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            isTest: null,
            jpGameId: type.jpGameId,
            definition: type.definition,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: created.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await JackpotInstanceService.disable(created.id, JackpotDisableMode.IMMEDIATE);
        let disabled = await JackpotInstanceService.find(created.id);
        expect(disabled).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            isTest: null,
            jpGameId: type.jpGameId,
            definition: type.definition,
            disableMode: 0,
            isDisabled: true,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: disabled.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });
        let ticker = await JackpotInstanceService.getTicker(created.id);
        expect(ticker.isDisabled).to.be.true;

        await verifyAudit("testid", JackpotAuditType.DISABLE);

        await JackpotInstanceService.enable(created.id);
        let enabled = await JackpotInstanceService.find(created.id);
        expect(enabled).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            isTest: null,
            jpGameId: type.jpGameId,
            definition: type.definition,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: enabled.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });
        ticker = await JackpotInstanceService.getTicker(created.id);
        expect(ticker.isDisabled).to.be.undefined;

        await verifyAudit("testid", JackpotAuditType.ENABLE);

        await JackpotInstanceService.disable(created.id, JackpotDisableMode.NEXT_WIN);
        disabled = await JackpotInstanceService.find(created.id);
        expect(disabled).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            isTest: null,
            jpGameId: type.jpGameId,
            definition: type.definition,
            disableMode: 1,
            isDisabled: true,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: disabled.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });
        ticker = await JackpotInstanceService.getTicker(created.id);
        expect(ticker.isDisabled).to.be.undefined;

        await JackpotInstanceService.enable(created.id);
        enabled = await JackpotInstanceService.find(created.id);
        expect(enabled).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            isTest: null,
            jpGameId: type.jpGameId,
            definition: type.definition,
            disableMode: 1,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: enabled.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });
        ticker = await JackpotInstanceService.getTicker(created.id);
        expect(ticker.isDisabled).to.be.undefined;
    });

    it("disable/enable jackpot instance - not allowed", async () => {
        await JackpotTypeService.create({ ...type, name: "non_disabled", canBeDisabled: false });

        const created = await JackpotInstanceService.create({ ...instance, type: "non_disabled" });
        expect(created).to.deep.equal({
            id: "testid",
            type: "non_disabled",
            baseType: "base",
            isTest: null,
            jpGameId: type.jpGameId,
            definition: type.definition,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: created.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });

        await expect(JackpotInstanceService.disable(created.id, JackpotDisableMode.IMMEDIATE))
            .to.be.rejectedWith(Errors.JackpotCantBeDisabledError);

        await expect(JackpotInstanceService.enable(created.id))
            .to.be.rejectedWith(Errors.JackpotCantBeDisabledError);
    });

    it("fais to create jackpot instance if type not found", async () => {
        const invalidInstance: JackpotInstance = {
            id: "testid",
            type: "unknown",
        };
        await expect(JackpotInstanceService.create(invalidInstance)).to.be.rejectedWith(Errors.JackpotTypeNotFound);
    });

    it("gets jackpot instance", async () => {
        await JackpotInstanceService.create(instance);
        const createdInstance = await JackpotInstanceService.find(instance.id);
        expect(createdInstance).to.deep.equal({
            id: "testid",
            type: "test",
            baseType: "base",
            isTest: null,
            jpGameId: type.jpGameId,
            definition: type.definition,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: createdInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });
    });

    it("gets jackpot ticker", async () => {
        await JackpotInstanceService.create(instance);
        const ticker = await JackpotInstanceService.getTicker(instance.id);
        expect(ticker).to.deep.equal({
            id: "testid",
            currency: "EUR",
            pools: {
                pool: {
                    progressive: 0,
                    seed: 0,
                },
            },
            seqId: 0
        });
    });

    it("fails to get jackpot instance if not exist", async () => {
        await expect(JackpotInstanceService.find(instance.id)).to.be.rejectedWith(Errors.JackpotInstanceNotFound);
    });

    it("gets all jackpot instances", async () => {
        await JackpotInstanceService.create(instance);
        const instances = await JackpotInstanceService.findAll();
        expect(instances).to.deep.equal([
            {
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: type.jpGameId,
                isTest: null,
                definition: type.definition,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: instances[0].createdAt,
                jackpotConfigurationLevel: null,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            }
        ]);
    });

    it("gets jackpot instances by single id", async () => {
        await JackpotInstanceService.create(instance);
        const instances = await JackpotInstanceService.findAll(["testid"]);
        expect(instances).to.deep.equal([
            {
                id: "testid",
                type: "test",
                baseType: "base",
                isTest: null,
                jpGameId: type.jpGameId,
                definition: type.definition,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: instances[0].createdAt,
                jackpotConfigurationLevel: null,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            }
        ]);
    });

    it("gets jackpot instances by wrong id", async () => {
        await JackpotInstanceService.create(instance);
        const instances = await JackpotInstanceService.findAll(["wrong_id"]);
        expect(instances).to.deep.equal([]);
    });

    it("gets jackpot instances by ids", async () => {
        await JackpotInstanceService.create({ id: "testid1", type: "test" });
        await JackpotInstanceService.create({ id: "testid2", type: "test" });
        await JackpotInstanceService.create({ id: "testid3", type: "test" });
        const instances = await JackpotInstanceService.findAll(["testid1", "testid3"]);
        expect(instances).to.deep.equal([
            {
                id: "testid1",
                type: "test",
                baseType: "base",
                isTest: null,
                jpGameId: type.jpGameId,
                definition: type.definition,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: instances[0].createdAt,
                jackpotConfigurationLevel: null,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            }, {
                id: "testid3",
                type: "test",
                baseType: "base",
                isTest: null,
                jpGameId: type.jpGameId,
                definition: type.definition,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: instances[1].createdAt,
                jackpotConfigurationLevel: null,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            }
        ]);
    });

    it("gets jackpot instances by empty id", async () => {
        await JackpotInstanceService.create(instance);
        const S = ",".split(",");
        expect(S).deep.equal(["", ""]);
        const instances = await JackpotInstanceService.findAll(["", ""]);
        expect(instances).to.deep.equal([]);
    });

    it("gets external jackpot instances by id", async () => {
        getExternalJackpots.returns([{
            jackpotId: "jp1",
            jackpotType: "jp1_type"
        }, {
            jackpotId: "jp2",
            jackpotType: "jp2_type"
        }]);
        const instances = await JackpotInstanceService.findAll(["PP;jp1;sc1_wg1_EUR", "PP;jp2;sc1_wg2_EUR"]);
        expect(getExternalJackpots.lastCall.args[0]).to.equal("PP");
        expect(getExternalJackpots.lastCall.args[1]).to.deep.equal(["PP;jp1;sc1_wg1_EUR", "PP;jp2;sc1_wg2_EUR"]);
        expect(instances).to.deep.equal([
            {
                id: "jp1",
                type: "jp1_type"
            },
            {
                id: "jp2",
                type: "jp2_type"
            }
        ]);
    });

    it("updates jackpot instance", async () => {
        await JackpotInstanceService.create(instance);

        const instanceUpdate: any = {
            definition: {
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 100,
                        },
                        contribution: [],
                    }
                ],
            },
            regionCode: region.code,
            isOwned: true
        };
        let updated = await JackpotInstanceService.update(instance.id, instanceUpdate);
        let expectedUpdate: JackpotInstance = {
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 100,
                        },
                        contribution: [],
                    }
                ],
            },
            isTest: null,
            regionCode: region.code,
            disableMode: 0,
            isDisabled: false,
            isGlobal: true,
            isOwned: true,
            isLocal: false,
            createdAt: updated.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        };
        expect(updated).to.deep.equal(expectedUpdate);
        expect(await JackpotInstanceService.find(instance.id)).to.deep.equal(expectedUpdate);

        const remoteTicker = {
            id: "JP-TEST",
            currency: "EUR",
            pools:
                {
                    small: { amount: 10, initialSeed: 10, seed: 0, progressive: 0 },
                    medium: { amount: 100, initialSeed: 100, seed: 0, progressive: 0 },
                    large: { amount: 1000, initialSeed: 1000, seed: 0, progressive: 0 }
                },
            seqId: 2
        };
        getRemoteTicker.yields(null, { statusCode: 200 }, remoteTicker);
        const ticker = await JackpotInstanceService.getTicker(instance.id);
        expect(ticker).to.deep.equal(remoteTicker);

        await verifyAudit("testid", JackpotAuditType.UPDATE);

        // remove region
        updated = await JackpotInstanceService.update(instance.id, { regionCode: null, isGlobal: false } as any);
        expectedUpdate = {
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            isTest: null,
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 100,
                        },
                        contribution: [],
                    }
                ],
            },
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: true,
            isLocal: false,
            createdAt: updated.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        };
        expect(updated).to.deep.equal(expectedUpdate);
        expect(await JackpotInstanceService.find(instance.id)).to.deep.equal(expectedUpdate);

        // make global
        updated = await JackpotInstanceService.update(instance.id, { isGlobal: true } as any);
        expectedUpdate = {
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            isTest: null,
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 100,
                        },
                        contribution: [],
                    }
                ],
            },
            disableMode: 0,
            isDisabled: false,
            isGlobal: true,
            isOwned: true,
            isLocal: false,
            createdAt: updated.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        };
        expect(updated).to.deep.equal(expectedUpdate);
        expect(await JackpotInstanceService.find(instance.id)).to.deep.equal(expectedUpdate);
    });

    it("updates jackpot instance - new pools in instance should be ignored", async () => {
        const instanceWithPoolDefinition: JackpotInstance = {
            id: "testidOverride",
            type: complexType.jpGameId,
            definition: {
                list: [{ id: "a", seed: { amount: 100500 } }]
            } as JackpotDefinition
        };
        await JackpotInstanceService.create(instanceWithPoolDefinition);

        const instanceUpdate: any = {
            definition: {
                list: [
                    {
                        id: "b",
                        seed: {
                            amount: 100500,
                        },
                        contribution: [],
                    }
                ],
            },
            regionCode: region.code,
            isOwned: true
        };
        const updated = await JackpotInstanceService.update(instanceWithPoolDefinition.id, instanceUpdate);
        // Changes skipped
        expect(updated.definition.list[1]).deep.equal({
            id: "b",
            seed: {
                amount: 1000,
            },
            contribution: [],
        });
    });

    it("fails to update jackpot instance with currency", async () => {
        await JackpotInstanceService.create(instance);

        const instanceUpdate: any = {
            definition: {
                currency: "USD",
            },
        };
        await expect(JackpotInstanceService.update(instance.id, instanceUpdate))
            .to.be.rejectedWith(Errors.ValidationError);
    });

    it("fails to update jackpot instance with not overridable jackpot", async () => {
        await JackpotTypeService.create({ ...type, name: "not-overridable", overridable: false });

        await JackpotInstanceService.create({ ...instance, type: "not-overridable" });

        const instanceUpdate: any = {
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 1000,
                        },
                        contribution: [],
                    }
                ],
            },
        };
        await expect(JackpotInstanceService.update(instance.id, instanceUpdate))
            .to.be.rejectedWith(Errors.ValidationError);
    });

    it("update jackpot instance with new initial seed", async () => {
        await JackpotInstanceService.create(instance);

        const instanceUpdate: any = {
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 1000,
                        },
                        contribution: [],
                    }
                ],
            },
        };

        const updated = await JackpotInstanceService.update(instance.id, instanceUpdate);
        expect(updated).deep.equal(
            {
                "definition": {
                    "currency": "EUR",
                    "list": [
                        {
                            "contribution": [],
                            "id": "pool",
                            "seed": {
                                "amount": 1000
                            }
                        }
                    ]
                },
                "id": "testid",
                "jpGameId": "test",
                isTest: null,
                "type": "test",
                "baseType": "base",
                "disableMode": 0,
                "isDisabled": false,
                "isGlobal": false,
                "isOwned": false,
                "isLocal": false,
                createdAt: updated.createdAt,
                jackpotConfigurationLevel: null,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            }
        );

        await verifyAudit("testid", JackpotAuditType.UPDATE);
    });

    it("update jackpot type with new initial seed", async () => {
        const typeWithSeed = { ...type, name: "with-seed" };
        await JackpotTypeService.create(typeWithSeed);

        await JackpotInstanceService.create({ ...instance, type: "with-seed" });

        const typeUpdate: any = {
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 1000,
                        },
                        contribution: [],
                    }
                ],
            },
        };

        await JackpotTypeService.update("with-seed", typeUpdate);

        await verifyAudit("testid", JackpotAuditType.TYPE_UPDATE);
    });

    it("update jackpot type with virtual seed", async () => {
        const typeWithSeed = { ...type, name: "with-virtual-seed" };
        await JackpotTypeService.create(typeWithSeed);

        await JackpotInstanceService.create({ ...instance, type: "with-virtual-seed" });

        const typeUpdate: any = {
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        virtualSeed: {
                            amount: 500
                        },
                        seed: {
                            amount: 0,
                        },
                        contribution: [],
                    }
                ],
            },
        };

        await JackpotTypeService.update("with-virtual-seed", typeUpdate);

        await verifyAudit("testid", JackpotAuditType.TYPE_UPDATE);
    });

    it("update jackpot instance with virtual seed", async () => {
        await JackpotInstanceService.create(instance);
        const ticker = await JackpotInstanceService.getTicker(instance.id);
        expect(ticker).deep.equal({
            "currency": "EUR",
            "id": "testid",
            "pools": {
                "pool": {
                    "progressive": 0,
                    "seed": 0
                }
            },
            "seqId": 0
        });

        const instanceUpdate: any = {
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 0,
                        },
                        virtualSeed: {
                            amount: 500,
                        },
                        contribution: []
                    }
                ],
            }
        };

        const updated = await JackpotInstanceService.update(instance.id, instanceUpdate);
        expect(updated).deep.equal({
            ...instanceUpdate,
            id: "testid",
            jpGameId: "test",
            isTest: null,
            type: "test",
            baseType: "base",
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: updated.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });
    });

    it("fails to update jackpot type with jackpot override", async () => {
        await JackpotInstanceService.create(instance);

        const instanceUpdate: any = {
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 1000,
                        },
                        contribution: [],
                    }
                ],
            },
        };

        await JackpotInstanceService.update(instance.id, instanceUpdate);

        await expect(JackpotTypeService.update(type.name, { overridable: false } as any))
            .to.be.rejectedWith(Errors.ValidationError);
    });

    it("fails to update jackpot isTest parameter", async () => {
        await JackpotInstanceService.create({ ...instance, isTest: false });

        await expect(JackpotInstanceService.update(instance.id, { isTest: true } as any))
            .to.be.rejectedWith(Errors.ValidationError);
    });

    it("update jackpot isTest parameter via special method", async () => {
        await JackpotInstanceService.create({ ...instance, isTest: false });

        const updatedJackpotInstance = await JackpotInstanceService.updateIsTest(instance.id, true);

        expect(updatedJackpotInstance.isTest).to.be.true;
    });

    it("update complex jackpot instance with new initial seeds, redis should be updated", async () => {
        await JackpotInstanceService.create(complexInstance);
        const ticker = await JackpotInstanceService.getTicker(complexInstance.id);
        expect(ticker).deep.equal({
            currency: "EUR",
            id: "complexTestid",
            pools: {
                a: {
                    "progressive": 0,
                    "seed": 0
                },
                b: {
                    "progressive": 0,
                    "seed": 0
                },
                c: {
                    "progressive": 0,
                    "seed": 0
                }
            },
            "seqId": 0
        });

        const instanceUpdate: any = {
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "a",
                        seed: {
                            amount: 200,
                        },
                        contribution: [],
                    },
                    {
                        id: "b",
                        seed: {
                            amount: 1000,
                        },
                        contribution: [],
                    },
                    {
                        id: "c",
                        seed: {
                            amount: 20000,
                        },
                        contribution: [],
                    }
                ],
            }
        };

        const updated = await JackpotInstanceService.update(complexInstance.id, instanceUpdate);
        expect(updated).deep.equal({
            ...instanceUpdate,
            id: "complexTestid",
            jpGameId: "complexTest",
            isTest: null,
            type: "complexTest",
            baseType: "base",
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: updated.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });
        const updatedTicker = await JackpotInstanceService.getTicker(complexInstance.id);
        expect(updatedTicker).deep.equal({
            currency: "EUR",
            id: "complexTestid",
            pools: {
                a: {
                    "progressive": 0,
                    "seed": 0
                },
                b: {
                    "progressive": 0,
                    "seed": 0
                },
                c: {
                    "progressive": 0,
                    "seed": 0
                }
            },
            "seqId": 0
        });
    });

    it("deletes jackpot instance", async () => {
        await JackpotInstanceService.create(instance);
        await JackpotInstanceService.remove(instance.id);
    });

    it("fails to delete jackpot instance if not exist", async () => {
        await expect(JackpotInstanceService.remove(instance.id)).to.be.rejectedWith(Errors.JackpotInstanceNotFound);
    });

    it("uses cache to find jackpot instances", async () => {
        const createdInstance = await JackpotInstanceService.create(instance);

        const expectCached = {
            id: "testid",
            type: "test",
            baseType: "base",
            isTest: null,
            jpGameId: type.jpGameId,
            definition: type.definition,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: createdInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        };
        const cachedInstance1 = await JackpotInstanceService.find(instance.id);
        expect(cachedInstance1).to.deep.equal(expectCached);

        const cachedInstance2 = await JackpotInstanceService.find(instance.id);
        expect(cachedInstance2).to.deep.equal(expectCached);

        // cache invalidated when jackpot update
        const instanceUpdate: any = {
            definition: {
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 100,
                        },
                        contribution: [],
                    }
                ],
            },
        };
        const updatedInstance = await JackpotInstanceService.update(instance.id, instanceUpdate);
        const expectUpdated = {
            id: "testid",
            type: "test",
            baseType: "base",
            jpGameId: type.jpGameId,
            definition: {
                currency: "EUR",
                list: [
                    {
                        id: "pool",
                        seed: {
                            amount: 100,
                        },
                        contribution: [],
                    }
                ],
            },
            isTest: null,
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: updatedInstance.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        };
        expect(updatedInstance).to.deep.equal(expectUpdated);
        await delay(100);
        const instanceInvalidated = await JackpotInstanceService.find(instance.id);
        expect(instanceInvalidated).to.deep.equal(expectUpdated);

        // cache invalidated when jackpot removed
        await JackpotInstanceService.remove(instance.id);
        await delay(100);
        await expect(JackpotInstanceService.find(instance.id))
            .to.be.rejectedWith(Errors.JackpotInstanceNotFound);
    });

    it("finds jackpot instances with internal ids", async () => {
        await JackpotInstanceService.create(instance);

        const internal = await JackpotInstanceService.findInternal(instance.id);
        expect(internal.internalId).to.exist;
    });

    it("cache invalidated when jackpot type changed", async () => {
        const createdInstance = await JackpotInstanceService.create(instance);

        try {
            await JackpotTypeService.update(type.name, { jpGameId: "test2" } as JackpotType);
            await delay(100);
            const typeInvalidated = await JackpotInstanceService.find(instance.id);
            expect(typeInvalidated).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test2",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: createdInstance.createdAt,
                jackpotConfigurationLevel: null,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            });

            await verifyAudit("testid", JackpotAuditType.TYPE_UPDATE);
        } finally {
            // restore previous value
            await JackpotTypeService.update(type.name, { jpGameId: type.jpGameId } as JackpotType);
        }
    });

    it("gets jackpot ticker in EUR currency", async () => {
        await JackpotInstanceService.create({ ...instance, isTest: true });
        await new JackpotWallet(undefined, await JackpotInstanceService.findInternal(instance.id), 1, wallet)
            .updatePool("pool", await wallet.generateTransactionId(), {
                progressive: 5,
                seed: 10
            });

        const ticker = await JackpotInstanceService.getTicker(instance.id, "EUR");
        expect(ticker).to.deep.equal({
            id: "testid",
            currency: "EUR",
            pools: {
                pool: {
                    progressive: 5,
                    seed: 10,
                },
            },
            seqId: 1
        });
    });

    it("gets jackpot ticker in USD currency", async () => {
        await JackpotInstanceService.create({ ...instance, isTest: true });
        await new JackpotWallet(undefined, await JackpotInstanceService.findInternal(instance.id), 1, wallet)
            .updatePool("pool", await wallet.generateTransactionId(), {
                progressive: 5,
                seed: 10
            });
        await initCurrencyRates();

        const ticker = await JackpotInstanceService.getTicker(instance.id, "USD");
        expect(ticker).to.deep.equal({
            id: "testid",
            currency: "USD",
            pools: {
                pool: {
                    progressive: 5.732367238374759,
                    seed: 11.464734476749518,
                },
            },
            seqId: 1,
        });
    });

    it("gets jackpot ticker in JP currencies", async () => {
        await JackpotInstanceService.create({ ...instance, isTest: true });
        await new JackpotWallet(undefined, await JackpotInstanceService.findInternal(instance.id), 1, wallet)
            .updatePool("pool", await wallet.generateTransactionId(), {
                progressive: 5,
                seed: 10
            });
        await initCurrencyRates();

        const ticker = await JackpotInstanceService.getTicker(instance.id, "JPY");
        expect(ticker).to.deep.equal({
            id: "testid",
            currency: "JPY",
            pools: {
                pool: {
                    progressive: 646.3801374621664,
                    seed: 1292.7602749243329,
                },
            },
            seqId: 1,
        });
    });

    it("create jackpot instance with fully definition override, keep features", async () => {

        const created = await JackpotInstanceService.create({
            id: "overrideTestId",
            type: "overridable",
            definition: {
                currency: "CNY",
                maxBet: 500,
                features: [],
                list: [
                    {
                        id: "pool1",
                        seed: {
                            amount: 100,
                        },
                        contribution: [
                            {
                                seed: 0.1,
                                progressive: 0.2
                            }
                        ],
                        additionalProp: "prop"
                    },
                    {
                        id: "pool2",
                        seed: {
                            amount: 100,
                        },
                        contribution: [
                            {
                                seed: 0.1,
                                progressive: 0.2
                            }
                        ]
                    },
                    {
                        id: "pool3",
                        seed: {
                            amount: 100,
                        },
                        contribution: [
                            {
                                seed: 0.1,
                                progressive: 0.2
                            }
                        ]
                    }
                ],
            }
        } as any);
        expect(created).to.deep.equal({
            id: "overrideTestId",
            type: "overridable",
            baseType: "base",
            jpGameId: "test",
            isTest: null,
            definition: {
                currency: "CNY",
                maxBet: 500,
                features: ["overridableByInstance"],
                list: [
                    {
                        id: "pool1",
                        seed: {
                            amount: 100,
                        },
                        contribution: [
                            {
                                seed: 0.1,
                                progressive: 0.2
                            }
                        ],
                        additionalProp: "prop"
                    },
                    {
                        id: "pool2",
                        seed: {
                            amount: 100,
                        },
                        contribution: [
                            {
                                seed: 0.1,
                                progressive: 0.2
                            }
                        ]
                    },
                    {
                        id: "pool3",
                        seed: {
                            amount: 100,
                        },
                        contribution: [
                            {
                                seed: 0.1,
                                progressive: 0.2
                            }
                        ]
                    }
                ],
            },
            disableMode: 0,
            isDisabled: false,
            isGlobal: false,
            isOwned: false,
            isLocal: false,
            createdAt: created.createdAt,
            jackpotConfigurationLevel: null,
            entityId: null,
            jurisdictionCode: null,
            info: null,
        });
    });

    it("update jackpot instance with fully definition override, definition data should be updated, renaming of pools skipped",
        async () => {

            const created = await JackpotInstanceService.create({
                id: "overrideTestId",
                type: "overridable",
                definition: {
                    currency: "CNY",
                    minBet: 0,
                    maxBet: 500,
                    features: [],
                    list: [
                        {
                            id: "pool1",
                            seed: {
                                amount: 100,
                            },
                            contribution: [
                                {
                                    seed: 0.1,
                                    progressive: 0.2
                                }
                            ],
                            additionalProp: "prop"
                        },
                        {
                            id: "pool2",
                            seed: {
                                amount: 100,
                            },
                            bbb: 1,
                            contribution: [
                                {
                                    seed: 0.1,
                                    progressive: 0.2
                                }
                            ]
                        },
                        {
                            id: "pool3",
                            seed: {
                                amount: 100,
                            },
                            contribution: [
                                {
                                    seed: 0.1,
                                    progressive: 0.2
                                }
                            ]
                        }
                    ],
                }
            } as any);

            const update: any = {
                definition: {
                    minBet: 0.5,
                    maxBet: 5,
                    list: [
                        {
                            id: "pool2",
                            seed: {
                                amount: 100500,
                            },
                            aaa: 2,
                            contribution: [],
                        },
                        {
                            id: "try-to-rename",
                            seed: {
                                amount: 1,
                            },
                            contribution: [],
                        }
                    ]
                }
            };
            const updated = await JackpotInstanceService.update(created.id, update);
            expect(updated).deep.equal({
                ...created,
                definition: {
                    currency: "CNY",
                    features: created.definition.features,
                    minBet: 0.5,
                    maxBet: 5,
                    list: [
                        created.definition.list[0],
                        {
                            id: "pool2",
                            bbb: 1,
                            seed: {
                                amount: 100500,
                            },
                            aaa: 2,
                            contribution: []
                        },
                        created.definition.list[2],
                    ]
                }
            });
        });

    it("process game action - game not supported", async () => {
        await JackpotInstanceService.create(instance);
        const action: GameAction = { action: "stop-pools", payload: { pools: ["hourly-1"] } };
        await expect(JackpotInstanceService.performGameAction(instance.id, action)).to.be.rejectedWith(ValidationError);
    });

    it("process game action - instance not found", async () => {
        const action: GameAction = { action: "stop-pools", payload: { pools: ["hourly-1"] } };
        await expect(JackpotInstanceService.performGameAction("aa", action))
            .to
            .be
            .rejectedWith(JackpotInstanceNotFound);
    });

    it("process game action - empty actions", async () => {
        await JackpotInstanceService.create(instance);
        const game: JackpotGame = new TestJPGame(instance);
        jpGameLoad.restore();
        jpGameLoad = stub(GameService, "load").callsFake(() => {
            return game;
        });
        game.performAction = () => [];
        const action: GameAction = { action: "stop-pools", payload: { pools: ["hourly-1"] } };
        const result = await JackpotInstanceService.performGameAction(instance.id, action);
        expect(result).deep.equal([]);
    });

    describe("Jackpot Update Info", () => {

        before(async () => {
            jpGameExists.restore();
            jpGameLoad.restore();
            jpGameExists = stub(GameService, "exists");
            jpGameExists.returns(true);
            jpGameLoad = stub(GameService, "load").callsFake((jpGameId, jackpot) => {
                return new TestJPGame(jackpot);
            });
        });

        it("updates info via special method", async () => {
            const id = "updateInfoTestId";
            await JackpotInstanceService.create({ id, type: "overridable", definition: {
                    currency: complexType.definition.currency,
                    list: complexType.definition.list
                }
            });

            let externalId = "updateInfoTestId_hash";
            let externalStartDate = new Date();

            let updatedJackpotInstance = await JackpotInstanceService.updateInfo(id, { externalId, externalStartDate: externalStartDate.toISOString() });
            expect(updatedJackpotInstance.info.externalId).to.be.equal(externalId);
            expect(updatedJackpotInstance.info.externalStartDate).to.be.equal(externalStartDate.toISOString());

            externalId = "updateInfoTestId_hash2";
            externalStartDate = new Date();

            updatedJackpotInstance = await JackpotInstanceService.updateInfo(id, { externalId, externalStartDate: externalStartDate.toISOString() });
            expect(updatedJackpotInstance.info.externalId).to.be.equal(externalId);
            expect(updatedJackpotInstance.info.externalStartDate).to.be.equal(externalStartDate.toISOString());
        });
    });

    describe("Jackpot Pending Pools", () => {

        before(async () => {
            jpGameExists.restore();
            jpGameLoad.restore();
            jpGameExists = stub(GameService, "exists");
            jpGameExists.returns(true);
            jpGameLoad = stub(GameService, "load").callsFake((jpGameId, jackpot) => {
                return new TestJPGame(jackpot);
            });
        });

        it("applies pending pool via special method", async () => {
            const id = "applyPendingPoolTestId";
            await JackpotInstanceService.create({ id, type: "overridable", definition: {
                    currency: complexTypeForInstantJP.definition.currency,
                    list: complexTypeForInstantJP.definition.list,
                    pendingList: [
                        {
                            id: "A",
                            seed: {
                                amount: 100001,
                            },
                            contribution: [],
                        },
                        {
                            id: "B",
                            seed: {
                                amount: 10001,
                            },
                            contribution: [],
                        },
                    ]
                }
            });

            const updatedJackpotInstance = await JackpotInstanceService.applyPendingPool(id, "A");
            const updatedPool = updatedJackpotInstance.definition.list.find(pool => pool.id === "A");
            expect(updatedPool.seed.amount).to.be.equal(100001);
            expect(updatedJackpotInstance.definition.pendingList.length).to.be.equal(1);
            // to check that we do save updates in DB
            const jackpotInstance = await JackpotInstanceService.find(id);
            const jackpotInstancePool = jackpotInstance.definition.list.find(pool => pool.id === "A");
            expect(jackpotInstancePool.seed.amount).to.be.equal(100001);
        });
    });

    describe("Jackpot Configuration Level", () => {
        before(async () => {
            jpGameExists.restore();
            jpGameLoad.restore();
            jpGameExists = stub(GameService, "exists");
            jpGameExists.returns(true);
            jpGameLoad = stub(GameService, "load").callsFake((jpGameId, jackpot) => {
                return new TestJPGame(jackpot);
            });
        });

        it("creates instance with invalid jackpotConfigurationLevel", async () => {
            let promise = JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 0
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: "something" as any
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("creates instance with jackpotConfigurationLevel 6", async () => {
            const jpInstance = await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6
            });
            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: true,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 6,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("creates instance with jackpotConfigurationLevel 6 - isGlobal = false", async () => {
            const promise = JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6,
                isGlobal: false
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("creates instance with jackpotConfigurationLevel 6 - isLocal = true", async () => {
            const promise = JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6,
                isLocal: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("creates instance with jackpotConfigurationLevel 1, 2, 3, 4, 5 - isGlobal = true", async () => {
            let promise = JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 1,
                isGlobal: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 2,
                isGlobal: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 3,
                isGlobal: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 4,
                isGlobal: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 5,
                isGlobal: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("creates instance with jackpotConfigurationLevel 1, 2, 4 - isOwned = true", async () => {
            let jpInstance = await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 1,
                isOwned: true
            });
            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: true,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 1,
                entityId: 1,
                jurisdictionCode: null,
                info: null,
            });

            jpInstance = await JackpotInstanceService.create({
                ...instance,
                id: "test2",
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK",
                isOwned: true
            });
            expect(jpInstance).to.deep.equal({
                id: "test2",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: true,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK",
                info: null,
            });

            jpInstance = await JackpotInstanceService.create({
                ...instance,
                id: "test3",
                jackpotConfigurationLevel: 4,
                entityId: 1,
                isOwned: true
            });
            expect(jpInstance).to.deep.equal({
                id: "test3",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: true,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 4,
                entityId: 1,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("creates instance with jackpotConfigurationLevel 3, 5, 6 - isOwned = true", async () => {
            let promise = JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK",
                isOwned: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.create({
                ...instance,
                id: "test2",
                jackpotConfigurationLevel: 5,
                isOwned: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.create({
                ...instance,
                id: "test3",
                jackpotConfigurationLevel: 6,
                isOwned: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("creates instance with jackpotConfigurationLevel 1, 2, 4 without entityId", async () => {
            let promise = JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 1
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.create({
                ...instance,
                id: "test2",
                jackpotConfigurationLevel: 2
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.create({
                ...instance,
                id: "test3",
                jackpotConfigurationLevel: 4
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("creates instance with jackpotConfigurationLevel 2, 3 without jurisdictionCode", async () => {
            let promise = JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 2
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.create({
                ...instance,
                id: "test2",
                jackpotConfigurationLevel: 3
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance with invalid jackpotConfigurationLevel", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 1
            });

            let promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 0
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: "something" as any
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance with jackpotConfigurationLevel 6 - isLocal = true", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6
            });
            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                isLocal: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance with jackpotConfigurationLevel 1, 2, 3, 4, 5 - isGlobal = true", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6,
                isGlobal: true
            });

            let promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 1,
                isGlobal: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 2,
                isGlobal: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 3,
                isGlobal: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 4,
                isGlobal: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 5,
                isGlobal: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance with jackpotConfigurationLevel 1, 2, 4 - isOwned = true", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 1,
                isOwned: false
            });
            let jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                isOwned: true
            });
            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: true,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 1,
                entityId: 1,
                jurisdictionCode: null,
                info: null,
            });

            await JackpotInstanceService.create({
                ...instance,
                id: "test2",
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK",
                isOwned: false
            });
            jpInstance = await JackpotInstanceService.update("test2", {
                ...instance,
                id: "test2",
                isOwned: true
            });
            expect(jpInstance).to.deep.equal({
                id: "test2",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: true,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK",
                info: null,
            });

            await JackpotInstanceService.create({
                ...instance,
                id: "test3",
                jackpotConfigurationLevel: 4,
                entityId: 1,
                isOwned: false
            });
            jpInstance = await JackpotInstanceService.update("test3", {
                ...instance,
                id: "test2",
                isOwned: true
            });
            expect(jpInstance).to.deep.equal({
                id: "test3",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: true,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 4,
                entityId: 1,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance with jackpotConfigurationLevel 3, 5, 6 - isOwned = true", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK",
                isOwned: false
            });

            let promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                isOwned: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            await JackpotInstanceService.create({
                ...instance,
                id: "test2",
                jackpotConfigurationLevel: 5,
                isOwned: false
            });

            promise = JackpotInstanceService.update("test2", {
                ...instance,
                id: "test2",
                isOwned: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);

            await JackpotInstanceService.create({
                ...instance,
                id: "test3",
                jackpotConfigurationLevel: 6,
                isOwned: false
            });

            promise = JackpotInstanceService.update("test2", {
                ...instance,
                id: "test3",
                isOwned: true
            });
            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 1 to 2", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 1
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 2,
                jurisdictionCode: "UK"
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK",
                info: null,
            });
        });

        it("updates instance from level 1 to 2 without jurisdictionCode", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 1
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 2
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 1 to 3", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 1
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK"
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 3,
                entityId: null,
                jurisdictionCode: "UK",
                info: null,
            });
        });

        it("updates instance from level 1 to 3 without jurisdictionCode", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 1
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 3
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 1 to 4", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 1
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 4,
                entityId: 2
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 4,
                entityId: 2,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 1 to 5", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 1
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 5
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 5,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 1 to 6", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 1
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 6
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 6,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 2 to 1", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK"
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 1
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 1,
                entityId: 1,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 2 to 3", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK"
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 3
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 3,
                entityId: null,
                jurisdictionCode: "UK",
                info: null,
            });
        });

        it("updates instance from level 2 to 4", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK"
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 4
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 4,
                entityId: 1,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 2 to 5", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK"
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 5
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 5,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 2 to 6", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK"
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 6
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 6,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 3 to 1", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK"
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 1
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 1,
                entityId: 1,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 3 to 1 without entityId", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK"
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 1
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 3 to 2", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK"
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 2,
                entityId: 1
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK",
                info: null,
            });
        });

        it("updates instance from level 3 to 2 without entityId", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK"
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 2
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 3 to 4", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK"
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 4,
                entityId: 1
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 4,
                entityId: 1,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 3 to 4 without entityId", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK"
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 4
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 3 to 5", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK"
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 5
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 5,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 3 to 6", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK"
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 6
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 6,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 4 to 1", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 4,
                entityId: 1
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 2
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 1,
                entityId: 2,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 4 to 2", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 4,
                entityId: 1
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 2,
                entityId: 2,
                jurisdictionCode: "UK"
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 2,
                entityId: 2,
                jurisdictionCode: "UK",
                info: null,
            });
        });

        it("updates instance from level 4 to 2 without jurisdictionCode", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 4,
                entityId: 1
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 2,
                entityId: 2
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 4 to 3", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 4,
                entityId: 1
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK"
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 3,
                entityId: null,
                jurisdictionCode: "UK",
                info: null,
            });
        });

        it("updates instance from level 4 to 3 without jurisdictionCode", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 4,
                entityId: 1
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 3
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 4 to 5", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 4,
                entityId: 1
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 5
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 5,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 4 to 6", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 4,
                entityId: 1
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 6
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 6,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 5 to 1", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 5
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 1
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 1,
                entityId: 1,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 5 to 1 without entityId", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 5
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 1
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 5 to 2", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 5
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK"
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK",
                info: null,
            });
        });

        it("updates instance from level 5 to 2 without entityId", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 5
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 2,
                jurisdictionCode: "UK"
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 5 to 2 without jurisdictionCode", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 5
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 2,
                entityId: 1
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 5 to 3", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 5
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK"
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 3,
                entityId: null,
                jurisdictionCode: "UK",
                info: null,
            });
        });

        it("updates instance from level 5 to 3 without jurisdictionCode", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 5
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 3
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 5 to 4", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 5
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 4,
                entityId: 1
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 4,
                entityId: 1,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 5 to 4 without entityId", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 5
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 4
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 5 to 6", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 5
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 6
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 6,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 6 to 1", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 1,
                entityId: 1
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 1,
                entityId: 1,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 6 to 1 without entityId", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 1
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 6 to 2", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK"
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 2,
                entityId: 1,
                jurisdictionCode: "UK",
                info: null,
            });
        });

        it("updates instance from level 6 to 2 without entityId", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 2,
                jurisdictionCode: "UK"
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 6 to 2 without jurisdictionCode", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 2,
                entityId: 1
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 6 to 3", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 3,
                jurisdictionCode: "UK"
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 3,
                entityId: null,
                jurisdictionCode: "UK",
                info: null,
            });
        });

        it("updates instance from level 6 to 3 without jurisdictionCode", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 3
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 6 to 4", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 4,
                entityId: 1
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 4,
                entityId: 1,
                jurisdictionCode: null,
                info: null,
            });
        });

        it("updates instance from level 6 to 4 without entityId", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6
            });

            const promise = JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 4
            });

            await expect(promise).to.eventually.be.rejectedWith(ValidationError);
        });

        it("updates instance from level 6 to 5", async () => {
            await JackpotInstanceService.create({
                ...instance,
                jackpotConfigurationLevel: 6
            });

            const jpInstance = await JackpotInstanceService.update(instance.id, {
                ...instance,
                jackpotConfigurationLevel: 5
            });

            expect(jpInstance).to.deep.equal({
                id: "testid",
                type: "test",
                baseType: "base",
                jpGameId: "test",
                definition: type.definition,
                isTest: null,
                disableMode: 0,
                isDisabled: false,
                isGlobal: false,
                isOwned: false,
                isLocal: false,
                createdAt: jpInstance.createdAt,
                jackpotConfigurationLevel: 5,
                entityId: null,
                jurisdictionCode: null,
                info: null,
            });
        });
    });
});
