import { expect } from "chai";
import { ValidationError } from "../../skywind/errors";
import { IpFamily, Valida<PERSON> } from "../../skywind/utils/validators";
import { validateAudit } from "../../skywind/api/model.router";

type ExpressEmulatedHandler = (req: any, response: any, next: (err?: any) => any) => void;

async function emulateExpressRouter(handler: ExpressEmulatedHandler, req: any): Promise<any> {
    return new Promise<any>((resolve, reject) => {
        handler(req, {}, (err?: any) => {
            return resolve(err);
        });
    });
}

describe("Model.router", () => {

    it("Correct audit type", async () => {
        const request = {
            query: {
                initiatorType: "user"
            }
        };

        const result = await emulateExpressRouter(validateAudit, request);
        expect(result).deep.equals(undefined); // No error

    });

    it("Incorrect audit type", async () => {
        const request = {
            query: {
                initiatorType: "userss"
            }
        };

        const result = await emulateExpressRouter(validateAudit, request);
        expect(result).not.equals(undefined);
        expect(result).instanceof(ValidationError);

    });

    it("Empty audit type", async () => {
        const request = {
            query: {}
        };

        const result = await emulateExpressRouter(validateAudit, request);
        expect(result).deep.equals(undefined); // No error

    });

    it("IP validator", () => {
        expect(Validators.validateIP("127.0.0.1")).deep.equals(IpFamily.IP_V4);
        expect(Validators.validateIP("***************")).deep.equals(IpFamily.IP_V4);
        expect(Validators.validateIP("::ffff:**************")).deep.equals(IpFamily.IP_V6);
        expect(Validators.validateIP("2001:0db8:85a3:0000:0000:8a2e:0370:7334")).deep.equals(IpFamily.IP_V6);

        expect(Validators.validateIP("2001:0db8:85a3:0000:0000:8a2e:0370:7334:256.0.0.1")).deep.equals(IpFamily.NONE);
        expect(Validators.validateIP("256.255.255.255")).deep.equals(IpFamily.NONE);
    });

});
