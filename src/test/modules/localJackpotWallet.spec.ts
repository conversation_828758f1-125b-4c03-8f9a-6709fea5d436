import { expect, use } from "chai";
import {
    convertContributionsToContributionsPayoutWithTotals,
    createTestJPGame,
    flush,
    flushDb,
    getLastTransaction,
    JP_INSTANT,
    JP_WITH_DYNAMIC_CONTRIBUTION,
    JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT,
    JP_WITH_INFO_AND_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT,
    JP_WITH_LIMITED_CONTRIBUTION,
    JP_WITH_WIN_TRANSFERS,
    unloadLastTransaction
} from "../helpers";
import { DEFAULT_PRECISION, JackpotWallet } from "../../skywind/modules/jackpotWallet";
import wallet from "../../skywind/services/wallet.service";
import inMemoryWallet from "../../pov/wallet/wallet.inmemory";
import { InsufficientJackpotBalance, InvalidJackpotResult, ValidationError } from "../../skywind/errors";
import {
    InternalTransferPoolManipulation,
    JackpotGame,
    JackpotOperationStatus,
    Operation,
    PoolManipulation,
    PoolManipulationType,
    PoolManipulationTransferType as TransferType,
    PropertyChange,
    TransferBetweenPoolsManipulation,
    WinPayout
} from "@skywind-group/sw-jpn-core";
import { IAccount, ITransaction, IWallet, MAX_CAPACITY_REACHED } from "@skywind-group/sw-wallet";
import { contributionLogDb } from "../../skywind/history/contribution.log";
import { winLogDb } from "../../skywind/history/win.log";
import { transferLogDb } from "../../skywind/history/transfer.log";
import { getWalletKey, JackpotPlayerWin, JackpotWinTransfer, JackpotWinType } from "../../definition";
import { stub } from "sinon";
import { createPlayerContributions } from "../../skywind/services/jackpotGameFlow";
import {
    BaseWalletParams,
    convertContributionsToContributionsPayout,
    META_INF_ACCOUNT
} from "../../skywind/modules/walletParams";
import config from "../../skywind/config";
import { JP_TEST } from "../services/jackpotUpdatePool.spec";
import * as _ from "lodash";
import { WinConsumer } from "../../skywind/history/win.consumer";
import * as redis from "../../skywind/storage/redis";

use(require("chai-as-promised"));

describe("Local Jackpot Wallet", () => {

    const jpInstance = JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT;
    const jpInstanceWithInfo = JP_WITH_INFO_AND_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT;

    const params: BaseWalletParams = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        playerCurrency: "CNY",
        gameCode: "test",
        gameId: "test",
        roundId: "1"
    };
    const exchangeRate = 0.1368;
    let walletFindCommitted;

    let client;

    before(async () => {
        client = await redis.get();
        walletFindCommitted = stub(wallet, "findCommittedTransaction");
    });

    after(async () => {
        walletFindCommitted.restore();
        await redis.release(client);
    });

    beforeEach(async () => {
        await flush();
        await flushDb();
        walletFindCommitted.reset();
    });

    async function getContributions() {
        const logs = await contributionLogDb.findAll();
        return logs.map((v: any) => {
            delete v.id;
            delete v.trxDate;
            if (v.status === null) {
                delete v.status;
            }
            v.seed = Number(v.seed);
            v.progressive = Number(v.progressive);
            v.playerSeed = Number(v.playerSeed);
            v.playerProgressive = Number(v.playerProgressive);
            v.contributionAmount = Number(v.contributionAmount);
            v.currencyRate = Number(v.currencyRate);
            return v;
        });
    }

    async function getWins() {
        const logs = await winLogDb.findAll();
        return logs.map((v: any) => {
            delete v.id;
            delete v.trxDate;
            if (v.status === null) {
                delete v.status;
            }
            v.initialSeed = Number(v.initialSeed);
            v.seed = Number(v.seed);
            v.progressive = Number(v.progressive);
            v.totalSeed = Number(v.totalSeed);
            v.totalProgressive = Number(v.totalProgressive);
            v.seedSinceLastWin = Number(v.seedSinceLastWin);
            v.progressiveSinceLastWin = Number(v.progressiveSinceLastWin);
            v.winAmount = Number(v.winAmount);
            v.currencyRate = Number(v.currencyRate);
            v.betAmount = v.betAmount ? +v.betAmount : v.betAmount;
            return v;
        });
    }

    async function getTransfers() {
        const logs = await transferLogDb.findAll();
        return logs.map((v: any) => {
            delete v.id;
            delete v.trxDate;
            return v;
        });
    }

    describe("flow", () => {

        let game: JackpotGame;

        it("initializes jackpot wallet", async () => {
            const jp = new JackpotWallet(params, jpInstance, 1, wallet);
            const result = await jp.getTicker();
            game = await createTestJPGame(jp);
            expect(result).to.deep.equal({
                "currency": "EUR",
                "id": "JP-TEST",
                "pools": {
                    "large": {
                        "progressive": 0,
                        "seed": 0,
                    },
                    "medium": {
                        "progressive": 0,
                        "seed": 0,
                    },
                    "small": {
                        "progressive": 0,
                        "seed": 0,
                    },
                },
                "seqId": 0
            });
        });

        it("gets ticker", async () => {
            const jp = new JackpotWallet(params, jpInstance, 1, wallet);
            const result = await jp.getTicker();
            expect(result).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 0,
                        "progressive": 0
                    },
                    "medium": {
                        "seed": 0,
                        "progressive": 0
                    },
                    "small": {
                        "seed": 0,
                        "progressive": 0
                    },
                },
                "seqId": 0
            });
        });

        it("gets ticker with info", async () => {
            const instanceWithInfo = {
                "internalId": 11,
                "id": "JP-TEST-INFO",
                "type": "test",
                "jpGameId": "test",
                "precision": DEFAULT_PRECISION,
                "definition": {
                    "currency": "EUR",
                    "list": [
                        {
                            "id": "bet10",
                            "seed": {
                                "amount": 10,
                            },
                            "contribution": [
                                {
                                    "seed": 0.1,
                                    "progressive": 0.2,
                                },
                            ],
                            "info": {
                                "bet": {
                                    "$lte": 10,
                                },
                            },
                        }, {
                            "id": "bet20",
                            "seed": {
                                "amount": 20,
                            },
                            "contribution": [
                                {
                                    "seed": 0.2,
                                    "progressive": 0.3,
                                },
                            ],
                            "info": {
                                "bet": {
                                    "$gt": 10, "$lte": 20,
                                },
                            },
                        }, {
                            "id": "bet30",
                            "seed": {
                                "amount": 30,
                            },
                            "contribution": [
                                {
                                    "seed": 0.3,
                                    "progressive": 0.4,
                                },
                            ],
                            "info": {
                                "bet": {
                                    "$gt": 20,
                                },
                            },
                        },
                    ],
                },
            };
            const playerWithInfo: BaseWalletParams = {
                playerCode: "PL-CODE-INFO",
                brandId: 1,
                region: "eu",
                playerCurrency: "CNY",
                gameCode: "test",
                gameId: "test",
                roundId: "1"
            };
            const jp = new JackpotWallet(playerWithInfo, instanceWithInfo, 1, wallet);
            const result = await jp.getTicker();
            expect(result).to.deep.equal({
                "id": instanceWithInfo.id,
                "currency": "EUR",
                "pools": {
                    "bet10": {
                        "seed": 0,
                        "progressive": 0,
                    },
                    "bet20": {
                        "seed": 0,
                        "progressive": 0,
                    },
                    "bet30": {
                        "seed": 0,
                        "progressive": 0,
                    },
                },
                "seqId": 0
            });
        });

        it("contributes", async () => {
            const jp = new JackpotWallet(params, jpInstance, 1, wallet);
            const trxId = await wallet.generateTransactionId();
            const data = {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = game.getContributions(10, data, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            const contributed = await jp.contribute(data.transactionId, data, contributions, playerContributions);
            const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 0.03,
                        "progressive": 0.07
                    },
                    "medium": {
                        "seed": 0.02,
                        "progressive": 0.04
                    },
                    "small": {
                        "seed": 0.01,
                        "progressive": 0.03
                    },
                },
                "seqId": 1
            });
            await unloadLastTransaction();
            const logs = await getContributions();
            expect(logs).to.deep.equal([
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "jackpotId": "JP-TEST",
                    "pool": "small",
                    "currency": "EUR",
                    "seed": 0.01,
                    "progressive": 0.03,
                    "playerSeed": 0.01,
                    "playerProgressive": 0.03,
                    "insertedAt": logs[0].insertedAt,
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "roundId": "1",
                    "contributionAmount": 10,
                    "currencyRate": 1,
                    "gameData": {
                        "amount": 10,
                        "externalId": "123",
                        "transactionId": trxId,
                        "roundId": "1"
                    },
                    "totalProgressive": "0",
                    "totalSeed": "0",
                },
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "jackpotId": "JP-TEST",
                    "pool": "medium",
                    "currency": "EUR",
                    "insertedAt": logs[1].insertedAt,
                    "seed": 0.02,
                    "progressive": 0.04,
                    "playerProgressive": 0.04,
                    "playerSeed": 0.02,
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "roundId": "1",
                    "contributionAmount": 10,
                    "currencyRate": 1,
                    "gameData": {
                        "amount": 10,
                        "externalId": "123",
                        "transactionId": trxId,
                        "roundId": "1"
                    },
                    "totalProgressive": "0",
                    "totalSeed": "0",
                },
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "jackpotId": "JP-TEST",
                    "pool": "large",
                    "currency": "EUR",
                    "insertedAt": logs[2].insertedAt,
                    "seed": 0.03,
                    "progressive": 0.07,
                    "playerSeed": 0.03,
                    "playerProgressive": 0.07,
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "roundId": "1",
                    "contributionAmount": 10,
                    "currencyRate": 1,
                    "gameData": {
                        "amount": 10,
                        "externalId": "123",
                        "transactionId": trxId,
                        "roundId": "1"
                    },
                    "totalProgressive": "0",
                    "totalSeed": "0",
                },
            ]);
        });

        it("contributes and sets properties", async () => {
            const jp = new JackpotWallet(params, jpInstance, 1, wallet);
            const trxId = await wallet.generateTransactionId();
            const data = {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = game.getContributions(10, data, ticker.pools);

            for (const contribution of contributions) {
                contribution.properties = {
                    "testProp": (contribution.seed + contribution.progressive) * 100
                };
            }

            const playerContributions = createPlayerContributions(contributions);
            const contributed = await jp.contribute(data.transactionId, data, contributions, playerContributions);
            const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 0.03,
                        "progressive": 0.07,
                        "testProp": 10
                    },
                    "medium": {
                        "seed": 0.02,
                        "progressive": 0.04,
                        "testProp": 6
                    },
                    "small": {
                        "seed": 0.01,
                        "progressive": 0.03,
                        "testProp": 4
                    },
                },
                "seqId": 1
            });
            await unloadLastTransaction();
            const logs = await getContributions();
            expect(logs).to.deep.equal([
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "jackpotId": "JP-TEST",
                    "pool": "small",
                    "currency": "EUR",
                    "seed": 0.01,
                    "progressive": 0.03,
                    "playerSeed": 0.01,
                    "playerProgressive": 0.03,
                    "insertedAt": logs[0].insertedAt,
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "roundId": "1",
                    "contributionAmount": 10,
                    "currencyRate": 1,
                    "gameData": {
                        "amount": 10,
                        "externalId": "123",
                        "transactionId": trxId,
                        "roundId": "1"
                    },
                    "totalProgressive": "0",
                    "totalSeed": "0",
                },
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "jackpotId": "JP-TEST",
                    "pool": "medium",
                    "currency": "EUR",
                    "insertedAt": logs[1].insertedAt,
                    "seed": 0.02,
                    "progressive": 0.04,
                    "playerProgressive": 0.04,
                    "playerSeed": 0.02,
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "roundId": "1",
                    "contributionAmount": 10,
                    "currencyRate": 1,
                    "gameData": {
                        "amount": 10,
                        "externalId": "123",
                        "transactionId": trxId,
                        "roundId": "1"
                    },
                    "totalProgressive": "0",
                    "totalSeed": "0",
                },
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "jackpotId": "JP-TEST",
                    "pool": "large",
                    "currency": "EUR",
                    "insertedAt": logs[2].insertedAt,
                    "seed": 0.03,
                    "progressive": 0.07,
                    "playerSeed": 0.03,
                    "playerProgressive": 0.07,
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "roundId": "1",
                    "contributionAmount": 10,
                    "currencyRate": 1,
                    "gameData": {
                        "amount": 10,
                        "externalId": "123",
                        "transactionId": trxId,
                        "roundId": "1"
                    },
                    "totalProgressive": "0",
                    "totalSeed": "0",
                },
            ]);
        });

        it("contributes and sets invalid properties", async () => {
            const jp = new JackpotWallet(params, jpInstance, 1, wallet);
            const trxId = await wallet.generateTransactionId();
            const data = {
                externalId: "123",
                transactionId: trxId,
                amount: 10,
                roundId: "1"
            };
            const ticker = await jp.getTicker();
            const contributions = game.getContributions(10, data, ticker.pools);

            for (const contribution of contributions) {
                contribution.properties = {
                    "seed": 100000,
                    "progressive": 100000000
                };
            }

            const playerContributions = createPlayerContributions(contributions);

            await expect(jp.contribute(data.transactionId, data, contributions, playerContributions))
                .to.be.rejectedWith(InvalidJackpotResult);
        });

        it("contributes retries with the same transaction", async () => {
            const jp = new JackpotWallet(params, jpInstance, exchangeRate, wallet);
            const trxId = await wallet.generateTransactionId();
            const data = {
                externalId: "123",
                transactionId: trxId,
                amount: 73.1,
                exchangeRate,
                roundId: "1",
            };
            let ticker = await jp.getTicker();
            const contributions = game.getContributions(10, data, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            const gameResult = { type: "win", pool: "small" };
            const contributed = await jp.contribute(
                data.transactionId,
                data,
                contributions,
                playerContributions,
                gameResult
            );
            const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult, playerContributions }
            );

            const lastTrx = await getLastTransaction();
            walletFindCommitted.returns(lastTrx);

            const contributed2 = await jp.contribute(data.transactionId, data, contributions, playerContributions);
            ticker = await jp.getTicker();
            expectedContributionsPayment.forEach(item => {
                item.totalProgressive = ticker.pools[item.pool].progressive || 0;
                item.totalSeed = ticker.pools[item.pool].seed || 0;
            });
            expect(contributed2).deep.equals(
                { contributions: expectedContributionsPayment, gameResult, playerContributions }
            );
        });

        it("releases win", async () => {
            const jp = new JackpotWallet(params, jpInstance, exchangeRate, wallet);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 73099.41,
                exchangeRate,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = game.getContributions(10000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            const contributed = await jp.contribute(request.transactionId, request, contributions, playerContributions);
            const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 30,
                        "progressive": 70
                    },
                    "medium": {
                        "seed": 20,
                        "progressive": 40
                    },
                    "small": {
                        "seed": 10,
                        "progressive": 30
                    },
                },
                "seqId": 1
            });

            // release win
            const payouts = await game.getWinPayouts([{ type: "win", pool: "medium" }], ticker.pools);
            expect(payouts.length).to.equal(1);
            expect(payouts[0]).to.deep.equal({
                "amount": 140,
                "pool": "medium",
                "progressive": 40,
                "seed": 100
            });
            const win: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...payouts[0],
                playerAmount: 1023.39,
                exchangeRate,
            };
            await jp.releaseWin(trxId, "123", "1", [win], 5);
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 30,
                        "progressive": 70
                    },
                    "medium": {
                        "seed": -80,
                        "progressive": 0
                    },
                    "small": {
                        "seed": 10,
                        "progressive": 30
                    },
                },
                "seqId": 2
            });

            await unloadLastTransaction();
            const logs = await getWins();
            expect(logs[0]).to.deep.equal({
                "trxId": trxId,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "currency": "EUR",
                "jackpotId": "JP-TEST",
                "pool": "medium",
                "eventId": 0,
                "initialSeed": 100,
                "insertedAt": logs[0].insertedAt,
                "seed": 100,
                "progressive": 40,
                "totalSeed": 20,
                "totalProgressive": 40,
                "seedSinceLastWin": 20,
                "progressiveSinceLastWin": 40,
                "brandId": 1,
                "region": "eu",
                "gameId": "test",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "winAmount": 1023.39,
                "currencyRate": 0.1368,
                "roundId": "1",
                "gameCode": "test",
                "betAmount": 5,
                "info": null,
            });
        });

        it("releases win with info", async () => {
            const jp = new JackpotWallet(params, jpInstanceWithInfo, exchangeRate, wallet);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 73099.41,
                exchangeRate,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = game.getContributions(10000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            const contributed = await jp.contribute(request.transactionId, request, contributions, playerContributions);
            const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 30,
                        "progressive": 70
                    },
                    "medium": {
                        "seed": 20,
                        "progressive": 40
                    },
                    "small": {
                        "seed": 10,
                        "progressive": 30
                    },
                },
                "seqId": 1
            });

            // release win
            const payouts = await game.getWinPayouts([{ type: "win", pool: "medium" }], ticker.pools);
            expect(payouts.length).to.equal(1);
            expect(payouts[0]).to.deep.equal({
                "amount": 140,
                "pool": "medium",
                "progressive": 40,
                "seed": 100
            });
            const win: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...payouts[0],
                playerAmount: 1023.39,
                exchangeRate,
            };
            await jp.releaseWin(trxId, "123", "1", [win], 5);
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 30,
                        "progressive": 70
                    },
                    "medium": {
                        "seed": -80,
                        "progressive": 0
                    },
                    "small": {
                        "seed": 10,
                        "progressive": 30
                    },
                },
                "seqId": 2
            });

            await unloadLastTransaction();
            const logs = await getWins();
            expect(logs[0]).to.deep.equal({
                "trxId": trxId,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "currency": "EUR",
                "jackpotId": "JP-TEST",
                "pool": "medium",
                "eventId": 0,
                "initialSeed": 100,
                "insertedAt": logs[0].insertedAt,
                "seed": 100,
                "progressive": 40,
                "totalSeed": 20,
                "totalProgressive": 40,
                "seedSinceLastWin": 20,
                "progressiveSinceLastWin": 40,
                "brandId": 1,
                "region": "eu",
                "gameId": "test",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "winAmount": 1023.39,
                "currencyRate": 0.1368,
                "roundId": "1",
                "gameCode": "test",
                "betAmount": 5,
                "info": {
                    "external": {
                        "externalId": "JP-TEST_hash1",
                        "externalStartDate": "2023-10-17T08:22:45.400Z",
                    }
                },
            });
        });

        it("releases win and sets properties", async () => {
            const jp = new JackpotWallet(params, jpInstance, exchangeRate, wallet);

            // release win
            const trxId = await wallet.generateTransactionId();
            let ticker = await jp.getTicker();
            const payouts = await game.getWinPayouts([{ type: "win", pool: "medium" }], ticker.pools);
            expect(payouts.length).to.equal(1);
            expect(payouts[0]).to.deep.equal({
                "amount": 100,
                "pool": "medium",
                "progressive": 0,
                "seed": 100
            });
            payouts[0].properties = { "testProp": 99 };
            const win: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...payouts[0],
                playerAmount: 1023.39,
                exchangeRate,
            };
            await jp.releaseWin(trxId, "123", "1", [win], 5);
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 0,
                        "progressive": 0
                    },
                    "medium": {
                        "seed": -100,
                        "progressive": 0,
                        "testProp": 99
                    },
                    "small": {
                        "seed": 0,
                        "progressive": 0
                    },
                },
                "seqId": 1
            });

            await unloadLastTransaction();
            const logs = await getWins();
            expect(logs[0]).to.deep.equal({
                "trxId": trxId,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "currency": "EUR",
                "jackpotId": "JP-TEST",
                "pool": "medium",
                "eventId": 0,
                "initialSeed": 100,
                "insertedAt": logs[0].insertedAt,
                "seed": 100,
                "progressive": 0,
                "totalSeed": 0,
                "totalProgressive": 0,
                "seedSinceLastWin": 0,
                "progressiveSinceLastWin": 0,
                "brandId": 1,
                "region": "eu",
                "gameId": "test",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "winAmount": 1023.39,
                "currencyRate": 0.1368,
                "roundId": "1",
                "gameCode": "test",
                "betAmount": 5,
                "info": null,
            });
        });

        it("releases multiple wins", async () => {
            const jp = new JackpotWallet(params, jpInstance, exchangeRate, wallet);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 73099.41,
                exchangeRate,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = game.getContributions(10000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            const contributed = await jp.contribute(request.transactionId, request, contributions, playerContributions);
            const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 30,
                        "progressive": 70
                    },
                    "medium": {
                        "seed": 20,
                        "progressive": 40
                    },
                    "small": {
                        "seed": 10,
                        "progressive": 30
                    },
                },
                "seqId": 1
            });

            const payouts = await game.getWinPayouts([{ type: "win", pool: "medium" }, { type: "win", pool: "small" }],
                ticker.pools);

            expect(payouts.length).to.equal(2);
            expect(payouts[0]).to.deep.equal({
                "amount": 140,
                "pool": "medium",
                "progressive": 40,
                "seed": 100
            });
            expect(payouts[1]).to.deep.equal({
                "amount": 40,
                "pool": "small",
                "progressive": 30,
                "seed": 10
            });

            // release win
            const win1: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...payouts[0],
                playerAmount: 1023.39,
                exchangeRate: 0.1368,
            };
            const win2: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...payouts[1],
                playerAmount: 292.4,
                exchangeRate: 0.1368,
            };
            await jp.releaseWin(trxId, "123", "1", [win1, win2], 5);
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 30,
                        "progressive": 70
                    },
                    "medium": {
                        "seed": -80,
                        "progressive": 0
                    },
                    "small": {
                        "seed": 0,
                        "progressive": 0
                    },
                },
                "seqId": 2,
            });

            await unloadLastTransaction();
            const logs = await getWins();
            expect(logs[0]).to.deep.equal({
                "trxId": trxId,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "currency": "EUR",
                "jackpotId": "JP-TEST",
                "pool": "medium",
                "eventId": 0,
                "initialSeed": 100,
                "insertedAt": logs[0].insertedAt,
                "seed": 100,
                "progressive": 40,
                "totalSeed": 20,
                "totalProgressive": 40,
                "brandId": 1,
                "region": "eu",
                "gameId": "test",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "winAmount": 1023.39,
                "currencyRate": 0.1368,
                "roundId": "1",
                "gameCode": "test",
                "progressiveSinceLastWin": 40,
                "seedSinceLastWin": 20,
                "betAmount": 5,
                "info": null
            });
            expect(logs[1]).to.deep.equal({
                "trxId": trxId,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "currency": "EUR",
                "jackpotId": "JP-TEST",
                "pool": "small",
                "eventId": 1,
                "initialSeed": 10,
                "insertedAt": logs[0].insertedAt,
                "seed": 10,
                "progressive": 30,
                "totalSeed": 10,
                "totalProgressive": 30,
                "brandId": 1,
                "region": "eu",
                "gameId": "test",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "winAmount": 292.4,
                "currencyRate": 0.1368,
                "roundId": "1",
                "gameCode": "test",
                "seedSinceLastWin": 10,
                "progressiveSinceLastWin": 30,
                "betAmount": 5,
                "info": null
            });
        });

        it("releases win and transfers to another pool", async () => {
            const instance = { ...JP_WITH_WIN_TRANSFERS };
            const jp = new JackpotWallet(params, instance, exchangeRate, wallet);
            const gameWithTransfer = await createTestJPGame(jp);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 73099.41,
                exchangeRate,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = gameWithTransfer.getContributions(10000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            const contributed = await jp.contribute(request.transactionId, request, contributions, playerContributions);
            const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": instance.id,
                "currency": "EUR",
                "pools": {
                    "main": {
                        "seed": 20,
                        "progressive": 40
                    },
                    "prize": {
                        "seed": 0,
                        "progressive": 0
                    },
                },
                "seqId": 1
            });

            // release win
            const win: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                amount: 80,
                pool: "main",
                progressive: 20,
                seed: 60,
                playerAmount: 584.8,
                exchangeRate: 0.1368,
            };
            const transfer: JackpotWinTransfer = {
                type: JackpotWinType.TRANSFER,
                amount: 60,
                pool: "main",
                progressive: 20,
                seed: 40,
                transferPool: "prize"
            };
            await jp.releaseWin(trxId, "123", "1", [win, transfer], 5);
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": instance.id,
                "currency": "EUR",
                "pools": {
                    "main": {
                        "seed": -80,
                        "progressive": 0
                    },
                    "prize": {
                        "seed": 40,
                        "progressive": 20
                    }
                },
                "seqId": 2,
            });

            await unloadLastTransaction();
            const logs = await getWins();
            expect(logs.length).to.equal(1);
            expect(logs[0]).to.deep.equal({
                "trxId": trxId,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "currency": "EUR",
                "jackpotId": instance.id,
                "pool": "main",
                "eventId": 0,
                "initialSeed": 100,
                "insertedAt": logs[0].insertedAt,
                "seed": 60,
                "progressive": 20,
                "totalSeed": 20,
                "totalProgressive": 40,
                "brandId": 1,
                "region": "eu",
                "gameId": "test",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "winAmount": 584.8,
                "currencyRate": 0.1368,
                "roundId": "1",
                "gameCode": "test",
                "progressiveSinceLastWin": 40,
                "seedSinceLastWin": 20,
                "betAmount": 5,
                "info": null,
            });
            const transfers = await getTransfers();
            expect(transfers.length).to.equal(1);
            expect(transfers[0]).to.deep.equal({
                "trxId": trxId,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "currency": "EUR",
                "jackpotId": instance.id,
                "fromPool": "main",
                "fromPoolProgressive": "40",
                "fromPoolSeed": "20",
                "seed": "40",
                "progressive": "20",
                "toPool": "prize",
                "toPoolProgressive": "0",
                "toPoolSeed": "0",
                "gameId": "test",
            });
        });

        it("fails to releases win on insufficient balance", async () => {
            const jp = new JackpotWallet(params, jpInstance, exchangeRate, wallet);
            const trxId = await wallet.generateTransactionId();
            const win: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                pool: "medium",
                amount: 140,
                seed: 100,
                progressive: 40,
                playerAmount: 1023.39,
                exchangeRate,
            };
            await expect(jp.releaseWin(trxId, "123", "1", [win])).to.be.rejectedWith(InsufficientJackpotBalance);
        });

        it("doesn't release money twice on duplicate transaction", async () => {
            const jp = new JackpotWallet(params, jpInstance, exchangeRate, wallet);

            const trxId = await wallet.generateTransactionId();
            const win: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                pool: "medium",
                amount: 100,
                seed: 100,
                progressive: 0,
                playerAmount: 877.19,
                exchangeRate,
            };

            // release win #1
            await jp.releaseWin(trxId, "123", "1", [win]);
            let ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 0,
                        "progressive": 0
                    },
                    "medium": {
                        "seed": -100,
                        "progressive": 0
                    },
                    "small": {
                        "seed": 0,
                        "progressive": 0
                    },
                },
                "seqId": 1
            });

            const lastTrx = await getLastTransaction();
            walletFindCommitted.returns(lastTrx);

            // release win #2
            await jp.releaseWin(trxId, "123", "1", [win]);
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 0,
                        "progressive": 0
                    },
                    "medium": {
                        "seed": -100,
                        "progressive": 0
                    },
                    "small": {
                        "seed": 0,
                        "progressive": 0
                    },
                },
                "seqId": 1
            });
        });

        it("contributes with conditions", async () => {
            const instanceWithConditions = {
                "internalId": 2,
                "id": "JP-TEST-CONDITIONS",
                "type": "test",
                "jpGameId": "test",
                "precision": DEFAULT_PRECISION,
                "definition": {
                    "currency": "EUR",
                    "list": [
                        {
                            "id": "bet10",
                            "seed": {
                                "amount": 10,
                            },
                            "contribution": [
                                {
                                    "seed": 0.1,
                                    "progressive": 0.3,
                                    "condition": {
                                        "request.bet": { "$lte": 10 },
                                    },
                                },
                            ],
                        }, {
                            "id": "bet20",
                            "seed": {
                                "amount": 20,
                            },
                            "contribution": [
                                {
                                    "seed": 0.1,
                                    "progressive": 0.3,
                                    "condition": {
                                        "request.bet": { "$gt": 10, "$lte": 20 },
                                    },
                                },
                            ],
                        }, {
                            "id": "bet30",
                            "seed": {
                                "amount": 30,
                            },
                            "contribution": [
                                {
                                    "seed": 0.1,
                                    "progressive": 0.3,
                                    "condition": {
                                        "request.bet": { "$gt": 20, "$lte": 30 },
                                    },
                                },
                            ],
                        },
                    ],
                },
            };
            const playerWithConditions: BaseWalletParams = {
                playerCode: "PL-CODE-CONDITIONS",
                brandId: 1,
                region: "eu",
                playerCurrency: "CNY",
                gameCode: "test",
                gameId: "test",
                roundId: "1"
            };
            const jp = new JackpotWallet(playerWithConditions, instanceWithConditions, exchangeRate, wallet);
            const gameWithConditions = await createTestJPGame(jp);

            // bet10
            let trxId = await wallet.generateTransactionId();
            let request = {
                externalId: "10",
                transactionId: trxId,
                amount: 73.1,
                bet: 10,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            let contributions = gameWithConditions.getContributions(10, request, ticker.pools);
            let playerContributions = createPlayerContributions(contributions);
            let contributed = await jp.contribute(request.transactionId, request, contributions, playerContributions);
            let expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": instanceWithConditions.id,
                "currency": "EUR",
                "pools": {
                    "bet10": {
                        "seed": 0.01,
                        "progressive": 0.03
                    },
                    "bet20": {
                        "seed": 0,
                        "progressive": 0
                    },
                    "bet30": {
                        "seed": 0,
                        "progressive": 0
                    },
                },
                "seqId": 1
            });
            await unloadLastTransaction();
            let logs = await getContributions();
            expect(logs.length).to.equal(1);
            expect(logs[0]).to.deep.equal(
                {
                    "trxId": trxId,
                    "externalId": "10",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "jackpotId": "JP-TEST-CONDITIONS",
                    "pool": "bet10",
                    "currency": "EUR",
                    "seed": 0.01,
                    "progressive": 0.03,
                    "playerProgressive": 0.03,
                    "playerSeed": 0.01,
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE-CONDITIONS",
                    "insertedAt": logs[0].insertedAt,
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "roundId": "1",
                    "contributionAmount": 73.1,
                    "currencyRate": 0.1368,
                    "gameData": {
                        "amount": 73.1,
                        "externalId": "10",
                        "transactionId": trxId,
                        "bet": 10,
                        "roundId": "1"
                    },
                    "totalProgressive": "0",
                    "totalSeed": "0",
                },
            );

            // bet20
            trxId = await wallet.generateTransactionId();
            request = {
                externalId: "20",
                transactionId: trxId,
                amount: 146.2,
                bet: 20,
                roundId: "1"
            };
            ticker = await jp.getTicker();
            contributions = gameWithConditions.getContributions(20, request, ticker.pools);
            expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            playerContributions = createPlayerContributions(contributions);
            contributed = await jp.contribute(request.transactionId, request, contributions, playerContributions);

            // contributed to "bet20"
            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": instanceWithConditions.id,
                "currency": "EUR",
                "pools": {
                    "bet10": {
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                    "bet20": {
                        "progressive": 0.06,
                        "seed": 0.02
                    },
                    "bet30": {
                        "progressive": 0,
                        "seed": 0
                    },
                },
                "seqId": 2
            });
            await unloadLastTransaction();
            logs = await getContributions();
            expect(logs.length).to.equal(2);
            expect(logs[1]).to.deep.equal(
                {
                    "trxId": trxId,
                    "externalId": "20",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "jackpotId": "JP-TEST-CONDITIONS",
                    "insertedAt": logs[1].insertedAt,
                    "pool": "bet20",
                    "currency": "EUR",
                    "seed": 0.02,
                    "progressive": 0.06,
                    "playerProgressive": 0.06,
                    "playerSeed": 0.02,
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE-CONDITIONS",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "roundId": "1",
                    "contributionAmount": 146.2,
                    "currencyRate": 0.1368,
                    "gameData": {
                        "amount": 146.2,
                        "externalId": "20",
                        "transactionId": trxId,
                        "bet": 20,
                        "roundId": "1"
                    },
                    "totalProgressive": "0",
                    "totalSeed": "0",
                },
            );

            // bet30
            trxId = await wallet.generateTransactionId();
            request = {
                externalId: "30",
                transactionId: trxId,
                amount: 219.3,
                bet: 30,
                roundId: "1"
            };
            ticker = await jp.getTicker();
            contributions = gameWithConditions.getContributions(30, request, ticker.pools);
            expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            playerContributions = createPlayerContributions(contributions);
            contributed = await jp.contribute(request.transactionId, request, contributions, playerContributions);

            // contributed to "bet30"
            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": instanceWithConditions.id,
                "currency": "EUR",
                "pools": {
                    "bet10": {
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                    "bet20": {
                        "progressive": 0.06,
                        "seed": 0.02
                    },
                    "bet30": {
                        "progressive": 0.09,
                        "seed": 0.03
                    },
                },
                "seqId": 3
            });
            await unloadLastTransaction();
            logs = await getContributions();
            expect(logs.length).to.equal(3);
            expect(logs[2]).to.deep.equal(
                {
                    "trxId": trxId,
                    "externalId": "30",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "jackpotId": "JP-TEST-CONDITIONS",
                    "pool": "bet30",
                    "currency": "EUR",
                    "insertedAt": logs[2].insertedAt,
                    "seed": 0.03,
                    "progressive": 0.09,
                    "playerProgressive": 0.09,
                    "playerSeed": 0.03,
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE-CONDITIONS",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "roundId": "1",
                    "contributionAmount": 219.3,
                    "currencyRate": 0.1368,
                    "gameData": {
                        "amount": 219.3,
                        "externalId": "30",
                        "transactionId": trxId,
                        "bet": 30,
                        "roundId": "1"
                    },
                    "totalProgressive": "0",
                    "totalSeed": "0",
                },
            );

            // bet 40
            trxId = await wallet.generateTransactionId();
            request = {
                externalId: "40",
                transactionId: trxId,
                amount: 40,
                bet: 40,
                roundId: "1"
            };
            ticker = await jp.getTicker();
            contributions = gameWithConditions.getContributions(40, request, ticker.pools);
            expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            playerContributions = createPlayerContributions(contributions);
            contributed = await jp.contribute(request.transactionId, request, contributions, playerContributions);
            // no contribution
            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": instanceWithConditions.id,
                "currency": "EUR",
                "pools": {
                    "bet10": {
                        "progressive": 0.03,
                        "seed": 0.01
                    },
                    "bet20": {
                        "progressive": 0.06,
                        "seed": 0.02
                    },
                    "bet30": {
                        "progressive": 0.09,
                        "seed": 0.03
                    },
                },
                "seqId": 3
            });
            await unloadLastTransaction();
            logs = await getContributions();
            expect(logs.length).to.equal(3);
        });

        it("releases win and transfers to void", async () => {
            const instance = { ...JP_WITH_WIN_TRANSFERS };
            const jp = new JackpotWallet(params, instance, exchangeRate, wallet);
            const gameWithTransfer = await createTestJPGame(jp);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 73099.41,
                exchangeRate,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = gameWithTransfer.getContributions(10000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            await jp.contribute(request.transactionId, request, contributions, playerContributions);

            // release win
            const win: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                amount: 80,
                pool: "main",
                progressive: 20,
                seed: 60,
                playerAmount: 584.8,
                exchangeRate: 0.1368,
            };
            const transfer: JackpotWinTransfer = {
                type: JackpotWinType.TRANSFER,
                amount: 60,
                pool: "main",
                progressive: 20,
                seed: 40,
                transferPool: null
            };
            await jp.releaseWin(trxId, "123", "1", [win, transfer], 5);
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": instance.id,
                "currency": "EUR",
                "pools": {
                    "main": {
                        "seed": -80,
                        "progressive": 0
                    },
                    "prize": {
                        "seed": 0,
                        "progressive": 0
                    }
                },
                "seqId": 2,
            });

            await unloadLastTransaction();
            const logs = await getWins();
            expect(logs.length).to.equal(1);
            expect(logs[0]).to.deep.equal({
                "trxId": trxId,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "currency": "EUR",
                "jackpotId": instance.id,
                "pool": "main",
                "eventId": 0,
                "initialSeed": 100,
                "insertedAt": logs[0].insertedAt,
                "seed": 60,
                "progressive": 20,
                "totalSeed": 20,
                "totalProgressive": 40,
                "brandId": 1,
                "region": "eu",
                "gameId": "test",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "winAmount": 584.8,
                "currencyRate": 0.1368,
                "roundId": "1",
                "gameCode": "test",
                "progressiveSinceLastWin": 40,
                "seedSinceLastWin": 20,
                "betAmount": 5,
                "info": null,
            });
            const transfers = await getTransfers();
            expect(transfers.length).to.equal(1);
            expect(transfers[0]).to.deep.equal({
                "trxId": trxId,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "currency": "EUR",
                "jackpotId": instance.id,
                "fromPool": "main",
                "fromPoolProgressive": "40",
                "fromPoolSeed": "20",
                "seed": "40",
                "progressive": "20",
                "toPool": null,
                "toPoolProgressive": "0",
                "toPoolSeed": "0",
                "gameId": "test",
            });
        });

        it("internal transfer - happy path", async () => {
            const instance = { ...JP_WITH_DYNAMIC_CONTRIBUTION };
            const jpWallet = new JackpotWallet(params, instance, exchangeRate, wallet);
            const gameWithTransfer = await createTestJPGame(jpWallet);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 1,
                exchangeRate,
                roundId: "1"
            };
            let ticker = await jpWallet.getTicker();
            const contributions = gameWithTransfer.getContributions(10000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            await jpWallet.contribute(request.transactionId, request, contributions, playerContributions);
            /*
            ticker:{"small":{"seed":10,"progressive":30},"medium":{"seed":20,"progressive":40},"large":{"seed":30,"progressive":70}},"seqId":1}
            */
            const transfers: InternalTransferPoolManipulation[] = [
                { pool: "small", type: PoolManipulationType.InternalTransferFromSeed, payload: { restValue: 11 } },
                { pool: "medium", type: PoolManipulationType.InternalTransferFromSeed, payload: { restValue: 0 } },
                { pool: "large", type: PoolManipulationType.InternalTransferFromSeed, payload: { restValue: 10 } },
            ];
            await jpWallet.processPoolManipulations(trxId, "123", "1", transfers);
            // transfer from "small" skipped
            ticker = await jpWallet.getTicker();
            expect(ticker).to.deep.equal({
                "id": instance.id,
                "currency": "EUR",
                "pools": {
                    "small": {
                        "seed": 10,
                        "progressive": 30
                    },
                    "medium": {
                        "seed": 0,
                        "progressive": 60
                    },
                    "large": {
                        seed: 10,
                        progressive: 90
                    }
                },
                "seqId": 2
            });

            await unloadLastTransaction();
            const transfersHistory = await getTransfers();
            expect(transfersHistory.length).to.equal(2);
            expect(transfersHistory[0]).deep.equal(
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "currency": "EUR",
                    "jackpotId": instance.id,
                    "fromPool": "medium",
                    "fromPoolProgressive": "40",
                    "fromPoolSeed": "20",
                    "seed": "20",
                    "progressive": "0",
                    "toPool": "medium",
                    "toPoolProgressive": "0",
                    "toPoolSeed": "0",
                    "gameId": "test"
                }
            );
        });

        it("transfers between pools and partially processed AboveInitial - happy path", async () => {
            const instance = { ...JP_INSTANT };
            const jpWallet = new JackpotWallet(params, instance, exchangeRate, wallet);
            const gameWithTransfer = await createTestJPGame(jpWallet);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 1,
                exchangeRate,
                roundId: "1"
            };
            let ticker = await jpWallet.getTicker();
            const contributions = gameWithTransfer.getContributions(10000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            await jpWallet.contribute(request.transactionId, request, contributions, playerContributions);
            /*
            ticker:{"D":{"seed":10,"progressive":30},"C":{"seed":20,"progressive":40},"B":{"seed":30,"progressive":70},"A":{"seed":40,"progressive":80}},"seqId":1}
            */
            const transfers: TransferBetweenPoolsManipulation[] = [
                { pool: "C", toPool: "D", type: PoolManipulationType.TransferBetweenPools, transferType: TransferType.AboveInitial, payload: { initialSeed: 100 } },
                { pool: "B", toPool: "D", type: PoolManipulationType.TransferBetweenPools, transferType: TransferType.Positive },
                { pool: "A", toPool: "D", type: PoolManipulationType.TransferBetweenPools, transferType: TransferType.Positive },
            ];
            await jpWallet.processPoolManipulations(trxId, "123", "1", transfers);

            ticker = await jpWallet.getTicker();
            expect(ticker).to.deep.equal({
                "id": instance.id,
                "currency": "EUR",
                "pools": {
                    "D": {
                        "seed": 10,
                        "progressive": 290
                    },
                    "C": {
                        "seed": 20,
                        "progressive": 0
                    },
                    "B": {
                        "seed": 0,
                        "progressive": 0
                    },
                    "A": {
                        "seed": 0,
                        "progressive": 0
                    }
                },
                "seqId": 2
            });

            await unloadLastTransaction();
            const transfersHistory = await getTransfers();
            expect(transfersHistory.length).to.equal(3);
            expect(transfersHistory).deep.equal([
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "currency": "EUR",
                    "jackpotId": instance.id,
                    "fromPool": "C",
                    "fromPoolProgressive": "40",
                    "fromPoolSeed": "20",
                    "seed": "0",
                    "progressive": "40",
                    "toPool": "D",
                    "toPoolProgressive": "30",
                    "toPoolSeed": "0",
                    "gameId": "test"
                },
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "currency": "EUR",
                    "jackpotId": instance.id,
                    "fromPool": "B",
                    "fromPoolProgressive": "70",
                    "fromPoolSeed": "30",
                    "seed": "30",
                    "progressive": "70",
                    "toPool": "D",
                    "toPoolProgressive": "30",
                    "toPoolSeed": "0",
                    "gameId": "test"
                },
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "currency": "EUR",
                    "jackpotId": instance.id,
                    "fromPool": "A",
                    "fromPoolProgressive": "80",
                    "fromPoolSeed": "40",
                    "seed": "40",
                    "progressive": "80",
                    "toPool": "D",
                    "toPoolProgressive": "30",
                    "toPoolSeed": "0",
                    "gameId": "test"
                }
            ]);
        });

        it("transfers between pools and processed AboveInitial - happy path", async () => {
            const instance = { ...JP_INSTANT };
            const jpWallet = new JackpotWallet(params, instance, exchangeRate, wallet);
            const gameWithTransfer = await createTestJPGame(jpWallet);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 1,
                exchangeRate,
                roundId: "1"
            };
            let ticker = await jpWallet.getTicker();
            const contributions = gameWithTransfer.getContributions(100000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            await jpWallet.contribute(request.transactionId, request, contributions, playerContributions);
            /*
            ticker:{"D":{"seed":10,"progressive":30},"C":{"seed":20,"progressive":40},"B":{"seed":30,"progressive":70},"A":{"seed":40,"progressive":80}},"seqId":1}
            */
            const transfers: TransferBetweenPoolsManipulation[] = [
                { pool: "C", toPool: "D", type: PoolManipulationType.TransferBetweenPools, transferType: TransferType.AboveInitial, payload: { initialSeed: 100 } },
                { pool: "B", toPool: "D", type: PoolManipulationType.TransferBetweenPools, transferType: TransferType.Positive },
                { pool: "A", toPool: "D", type: PoolManipulationType.TransferBetweenPools, transferType: TransferType.Positive },
            ];
            await jpWallet.processPoolManipulations(trxId, "123", "1", transfers);

            ticker = await jpWallet.getTicker();
            expect(ticker).to.deep.equal({
                "id": instance.id,
                "currency": "EUR",
                "pools": {
                    "D": {
                        "seed": 100,
                        "progressive": 3000
                    },
                    "C": {
                        "seed": 100,
                        "progressive": 0
                    },
                    "B": {
                        "seed": 0,
                        "progressive": 0
                    },
                    "A": {
                        "seed": 0,
                        "progressive": 0
                    }
                },
                "seqId": 2
            });

            await unloadLastTransaction();
            const transfersHistory = await getTransfers();
            expect(transfersHistory.length).to.equal(3);
            expect(transfersHistory).deep.equal([
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "currency": "EUR",
                    "jackpotId": instance.id,
                    "fromPool": "C",
                    "fromPoolProgressive": "400",
                    "fromPoolSeed": "200",
                    "seed": "100",
                    "progressive": "400",
                    "toPool": "D",
                    "toPoolProgressive": "300",
                    "toPoolSeed": "0",
                    "gameId": "test"
                },
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "currency": "EUR",
                    "jackpotId": instance.id,
                    "fromPool": "B",
                    "fromPoolProgressive": "700",
                    "fromPoolSeed": "300",
                    "seed": "300",
                    "progressive": "700",
                    "toPool": "D",
                    "toPoolProgressive": "300",
                    "toPoolSeed": "0",
                    "gameId": "test"
                },
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "currency": "EUR",
                    "jackpotId": instance.id,
                    "fromPool": "A",
                    "fromPoolProgressive": "800",
                    "fromPoolSeed": "400",
                    "seed": "400",
                    "progressive": "800",
                    "toPool": "D",
                    "toPoolProgressive": "300",
                    "toPoolSeed": "0",
                    "gameId": "test"
                }
            ]);
        });

        it("transfers between pools and internalTransfer - happy path", async () => {
            const instance = { ...JP_INSTANT };
            const jpWallet = new JackpotWallet(params, instance, exchangeRate, wallet);
            const gameWithTransfer = await createTestJPGame(jpWallet);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 1,
                exchangeRate,
                roundId: "1"
            };
            let ticker = await jpWallet.getTicker();
            const contributions = gameWithTransfer.getContributions(100000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            await jpWallet.contribute(request.transactionId, request, contributions, playerContributions);
            /*
            ticker:{"D":{"seed":10,"progressive":30},"C":{"seed":20,"progressive":40},"B":{"seed":30,"progressive":70},"A":{"seed":40,"progressive":80}},"seqId":1}
            */
            const transfers: PoolManipulation[] = [
                { pool: "D", toPool: "D", type: PoolManipulationType.InternalTransferFromSeed, payload: { restValue: 10 } } as InternalTransferPoolManipulation,
                { pool: "C", toPool: "D", type: PoolManipulationType.TransferBetweenPools, transferType: TransferType.Positive } as TransferBetweenPoolsManipulation,
                { pool: "B", toPool: "D", type: PoolManipulationType.TransferBetweenPools, transferType: TransferType.Positive } as TransferBetweenPoolsManipulation,
                { pool: "A", toPool: "D", type: PoolManipulationType.TransferBetweenPools, transferType: TransferType.Positive } as TransferBetweenPoolsManipulation,
            ];
            await jpWallet.processPoolManipulations(trxId, "123", "1", transfers);

            ticker = await jpWallet.getTicker();
            expect(ticker).to.deep.equal({
                "id": instance.id,
                "currency": "EUR",
                "pools": {
                    "D": {
                        "seed": 10,
                        "progressive": 3190
                    },
                    "C": {
                        "seed": 0,
                        "progressive": 0
                    },
                    "B": {
                        "seed": 0,
                        "progressive": 0
                    },
                    "A": {
                        "seed": 0,
                        "progressive": 0
                    }
                },
                "seqId": 2
            });

            await unloadLastTransaction();
            const transfersHistory = await getTransfers();
            expect(transfersHistory.length).to.equal(4);
            expect(transfersHistory).deep.equal([
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "currency": "EUR",
                    "jackpotId": instance.id,
                    "fromPool": "D",
                    "fromPoolProgressive": "300",
                    "fromPoolSeed": "100",
                    "seed": "90",
                    "progressive": "0",
                    "toPool": "D",
                    "toPoolProgressive": "0",
                    "toPoolSeed": "0",
                    "gameId": "test"
                },
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "currency": "EUR",
                    "jackpotId": instance.id,
                    "fromPool": "C",
                    "fromPoolProgressive": "400",
                    "fromPoolSeed": "200",
                    "seed": "200",
                    "progressive": "400",
                    "toPool": "D",
                    "toPoolProgressive": "300",
                    "toPoolSeed": "0",
                    "gameId": "test"
                },
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "currency": "EUR",
                    "jackpotId": instance.id,
                    "fromPool": "B",
                    "fromPoolProgressive": "700",
                    "fromPoolSeed": "300",
                    "seed": "300",
                    "progressive": "700",
                    "toPool": "D",
                    "toPoolProgressive": "300",
                    "toPoolSeed": "0",
                    "gameId": "test"
                },
                {
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "currency": "EUR",
                    "jackpotId": instance.id,
                    "fromPool": "A",
                    "fromPoolProgressive": "800",
                    "fromPoolSeed": "400",
                    "seed": "400",
                    "progressive": "800",
                    "toPool": "D",
                    "toPoolProgressive": "300",
                    "toPoolSeed": "0",
                    "gameId": "test"
                }
            ]);
        });

        it("internal transfer in case of it was performed by other player", async () => {
            const instance = { ...JP_WITH_DYNAMIC_CONTRIBUTION };
            const jpWallet = new JackpotWallet(params, instance, exchangeRate, wallet);
            const gameWithTransfer = await createTestJPGame(jpWallet);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 1,
                exchangeRate,
                roundId: "1"
            };
            let ticker = await jpWallet.getTicker();
            const contributions = gameWithTransfer.getContributions(10000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            await jpWallet.contribute(request.transactionId, request, contributions, playerContributions);
            /*
            ticker:{"small":{"seed":10,"progressive":30},"medium":{"seed":20,"progressive":40},"large":{"seed":30,"progressive":70}},"seqId":1}
            */
            const transfers: InternalTransferPoolManipulation[] = [
                { pool: "small", type: PoolManipulationType.InternalTransferFromSeed, payload: { restValue: 10 } },
            ];
            await jpWallet.processPoolManipulations(trxId, "123", "1", transfers);
            ticker = await jpWallet.getTicker();
            // ticker should not be changed
            expect(ticker["seqId"]).to.equal(1);

            await unloadLastTransaction();
            const transfersHistory = await getTransfers();
            // No wallet changes, no transaction
            expect(transfersHistory.length).to.equal(0);
        });

        // It's difficult to predict async behavior
        it.skip("internal transfer - concurrently", async () => {
            const instance = { ...JP_WITH_DYNAMIC_CONTRIBUTION };
            const jpWallet = new JackpotWallet(params, instance, exchangeRate, wallet);
            const gameWithTransfer = await createTestJPGame(jpWallet);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 1,
                exchangeRate,
                roundId: "1"
            };
            const ticker = await jpWallet.getTicker();
            const contributions = gameWithTransfer.getContributions(10000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            await jpWallet.contribute(request.transactionId, request, contributions, playerContributions);

            // ticker in cache: {"small":{"seed":10,"progressive":30}
            const transfers: InternalTransferPoolManipulation[] = [
                { pool: "small", type: PoolManipulationType.InternalTransferFromSeed, payload: { restValue: 5 } }
            ];
            const processPromise = jpWallet.processPoolManipulations(trxId, "123", "1", transfers);

            // decrease pool
            const decreaseTrxId = await wallet.generateTransactionId();
            const transaction: ITransaction = await wallet.startTransaction(decreaseTrxId);
            const walletKey = `jackpot:${instance.id}:${instance.definition.currency}`;
            const internalWallet: IWallet = await transaction.getWallet(walletKey);
            const account: IAccount = internalWallet.accounts.get("small");
            account.inc("seed", -7 * 10 ** 9, "release");
            transaction.commit();

            await processPromise.then(() => {
                    throw new Error();
                },
                (e) => expect(e.message).to.equal("Insufficient jackpot balance"));
        });

        it("releases win - pool's title should be added to transaction params", async () => {
            const jpInstanceCopy = _.cloneDeep(jpInstance);
            jpInstanceCopy.definition.list[2].title = "PrincessRO666-super-title";
            const jp = new JackpotWallet(params, jpInstanceCopy, exchangeRate, wallet);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 73099.41,
                exchangeRate,
                roundId: "1"
            };
            const ticker = await jp.getTicker();
            const contributions = game.getContributions(10000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            await jp.contribute(request.transactionId, request, contributions, playerContributions);

            // release win
            const payouts = await game.getWinPayouts([{ type: "win", pool: "medium" }, { type: "win", pool: "large" }],
                ticker.pools);
            const win: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...payouts[0],
                playerAmount: 1023.39,
                exchangeRate,
            };
            const win2: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...payouts[1],
                playerAmount: 1023.39,
                exchangeRate,
            };
            await jp.releaseWin(trxId, "123", "1", [win, win2], 5);

            const trx = await getLastTransaction();
            expect(trx.operation.params.wins).to.deep.equal([
                {
                    "currencyRate": 0.1368,
                    "initialSeed": 100,
                    "pool": "medium",
                    "progressive": 0,
                    "progressiveSinceLastWin": 40,
                    "seed": 100,
                    "seedSinceLastWin": 20,
                    "title": "medium",
                    "totalProgressive": 40,
                    "totalSeed": 20,
                    "winAmount": 1023.39,
                    "info": null,
                },
                {
                    "currencyRate": 0.1368,
                    "initialSeed": 1000,
                    "pool": "large",
                    "progressive": 0,
                    "progressiveSinceLastWin": 70,
                    "seed": 1000,
                    "seedSinceLastWin": 30,
                    "title": "PrincessRO666-super-title",
                    "totalProgressive": 70,
                    "totalSeed": 30,
                    "winAmount": 1023.39,
                    "info": null,
                }
            ]);
        });

        describe("jackpot without seed", () => {

            const jpWithoutSeed = {
                "internalId": 1,
                "id": "JP-TEST",
                "type": "test",
                "jpGameId": "test",
                "precision": DEFAULT_PRECISION,
                "definition": {
                    "currency": "EUR",
                    "list": [
                        {
                            "id": "small",
                            "contribution": [
                                {
                                    "progressive": 0.3,
                                }
                            ],
                        }, {
                            "id": "medium",
                            "contribution": [
                                {
                                    "progressive": 0.4,
                                }
                            ],
                        }, {
                            "id": "large",
                            "contribution": [
                                {
                                    "progressive": 0.7,
                                }
                            ],
                        },
                    ],
                },
            };

            it("contributes and wins", async () => {
                const jp = new JackpotWallet(params, jpWithoutSeed, exchangeRate, wallet);
                const gameWithoutSeed = await createTestJPGame(jp);

                // contribute
                const trxId = await wallet.generateTransactionId();
                const request = {
                    externalId: "123",
                    transactionId: trxId,
                    amount: 10000,
                    roundId: "1"
                };
                let ticker = await jp.getTicker();
                const contributions = gameWithoutSeed.getContributions(request.amount, request, ticker.pools);
                const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
                const playerContributions = createPlayerContributions(contributions);
                const contributed = await jp.contribute(request.transactionId, request,
                    contributions, playerContributions);
                expect(contributed).deep.equals(
                    { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
                );
                ticker = await jp.getTicker();
                expect(ticker).to.deep.equal({
                    "id": jpInstance.id,
                    "currency": "EUR",
                    "pools": {
                        "large": {
                            "progressive": 70,
                            "seed": 0,
                        },
                        "medium": {
                            "progressive": 40,
                            "seed": 0
                        },
                        "small": {
                            "progressive": 30,
                            "seed": 0
                        },
                    },
                    "seqId": 1
                });

                // release win
                const payouts = await gameWithoutSeed.getWinPayouts([{ type: "win", pool: "medium" }], ticker.pools);
                expect(payouts.length).to.equal(1);
                expect(payouts[0]).to.deep.equal({
                    "amount": 40,
                    "pool": "medium",
                    "progressive": 40,
                    "seed": 0
                });
                const win: JackpotPlayerWin = {
                    type: JackpotWinType.PLAYER,
                    ...payouts[0],
                    playerAmount: 1023.39,
                    exchangeRate: 0.1368,
                };
                await jp.releaseWin(trxId, "123", "1", [win], 1);
                ticker = await jp.getTicker();
                expect(ticker).to.deep.equal({
                    "id": jpInstance.id,
                    "currency": "EUR",
                    "pools": {
                        "large": {
                            "progressive": 70,
                            "seed": 0,
                        },
                        "medium": {
                            "progressive": 0,
                            "seed": 0
                        },
                        "small": {
                            "progressive": 30,
                            "seed": 0
                        },
                    },
                    "seqId": 2
                });

                await unloadLastTransaction();
                const logs = await getWins();
                expect(logs[0]).to.deep.equal({
                    "trxId": trxId,
                    "externalId": "123",
                    "remoteTrxId": null,
                    "remoteTrxRegion": null,
                    "currency": "EUR",
                    "jackpotId": "JP-TEST",
                    "pool": "medium",
                    "eventId": 0,
                    "initialSeed": 0,
                    "insertedAt": logs[0].insertedAt,
                    "seed": 0,
                    "progressive": 40,
                    "totalSeed": 0,
                    "totalProgressive": 40,
                    "brandId": 1,
                    "region": "eu",
                    "gameId": "test",
                    "playerCode": "PL-CODE",
                    "playerCurrency": "CNY",
                    "gameCode": "test",
                    "winAmount": 1023.39,
                    "currencyRate": 0.1368,
                    "roundId": "1",
                    "seedSinceLastWin": 0,
                    "progressiveSinceLastWin": 40,
                    "betAmount": 1,
                    "info": null,
                });
            });

            it("insufficient balance", async () => {
                const jp = new JackpotWallet(params, jpWithoutSeed, exchangeRate, wallet);
                const gameWithoutSeed = await createTestJPGame(jp);

                // contribute
                const trxId = await wallet.generateTransactionId();
                const request = {
                    externalId: "123",
                    transactionId: trxId,
                    amount: 10000,
                    roundId: "1"
                };
                let ticker = await jp.getTicker();
                const contributions = gameWithoutSeed.getContributions(request.amount, request, ticker.pools);
                const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
                const playerContributions = createPlayerContributions(contributions);
                const contributed = await jp.contribute(request.transactionId, request,
                    contributions, playerContributions);
                expect(contributed).deep.equals(
                    { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
                );
                ticker = await jp.getTicker();
                expect(ticker).to.deep.equal({
                    "id": jpInstance.id,
                    "currency": "EUR",
                    "pools": {
                        "large": {
                            "progressive": 70,
                            "seed": 0,
                        },
                        "medium": {
                            "progressive": 40,
                            "seed": 0
                        },
                        "small": {
                            "progressive": 30,
                            "seed": 0
                        },
                    },
                    "seqId": 1
                });

                // release win
                await expect(jp.releaseWin(trxId, "123", "1", [
                    {
                        type: JackpotWinType.PLAYER,
                        pool: "medium",
                        amount: 140,
                        seed: 100,
                        progressive: 40,
                        playerAmount: 140,
                        exchangeRate: 1
                    } as JackpotPlayerWin
                ])).to.be.rejectedWith(InsufficientJackpotBalance);
            });
        });
    });

    describe("deferredWins flow", () => {
        it("releases deferred win and confirms contribution and jp win", async () => {
            const jp = new JackpotWallet(params, jpInstance, exchangeRate, wallet);
            const game = await createTestJPGame(jp);
            const gameResult = {
                type: "win",
                pool: "large"
            };

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 73099.41,
                exchangeRate,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = game.getContributions(10000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            const contributionParams = {
                "brandId": 1,
                "contributionAmount": 73099.41,
                "contributions": [
                    {
                        "pool": "small",
                        "progressive": 30,
                        "seed": 10,
                        "totalProgressive": 0,
                        "totalSeed": 0
                    },
                    {
                        "pool": "medium",
                        "progressive": 40,
                        "seed": 20,
                        "totalProgressive": 0,
                        "totalSeed": 0
                    },
                    {
                        "pool": "large",
                        "progressive": 70,
                        "seed": 30,
                        "totalProgressive": 0,
                        "totalSeed": 0
                    }
                ],
                "currencyRate": 0.1368,
                "gameCode": "test",
                "gameData": {
                    "amount": 73099.41,
                    "exchangeRate": 0.1368,
                    "externalId": "123",
                    "roundId": "1",
                    "transactionId": trxId
                },
                "gameId": "test",
                "gameResult": {
                    "pool": "large",
                    "type": "win"
                },
                "initialSeed": {
                    "large": 1000,
                    "medium": 100,
                    "small": 10
                },
                "playerCode": "PL-CODE",
                "playerContributions": [
                    {
                        "pool": "small",
                        "progressive": 30,
                        "seed": 10
                    },
                    {
                        "pool": "medium",
                        "progressive": 40,
                        "seed": 20
                    },
                    {
                        "pool": "large",
                        "progressive": 70,
                        "seed": 30
                    }
                ],
                "playerCurrency": "CNY",
                "precision": 1000000000,
                "region": "eu",
                "roundId": "1"
            };
            const contributed = await jp.contribute(
                request.transactionId,
                request,
                contributions,
                playerContributions,
                gameResult,
                true
            );
            let expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            expectedContributionsPayment = expectedContributionsPayment.map(item => { return { ...item, status: JackpotOperationStatus.PENDING };});

            const expectedContributionParams = contributionParams;
            expectedContributionParams.contributions = contributionParams.contributions.map(item => { return { ...item, status: JackpotOperationStatus.PENDING };});

            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult, playerContributions, contributionParams: expectedContributionParams }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 30,
                        "progressive": 70
                    },
                    "medium": {
                        "seed": 20,
                        "progressive": 40
                    },
                    "small": {
                        "seed": 10,
                        "progressive": 30
                    },
                },
                "seqId": 1
            });

            // release win
            const payouts = game.getWinPayouts([{ type: "win", pool: "medium" }], ticker.pools);
            expect(payouts.length).to.equal(1);
            expect(payouts[0]).to.deep.equal({
                "amount": 140,
                "pool": "medium",
                "progressive": 40,
                "seed": 100
            });
            const win: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...payouts[0],
                playerAmount: 1023.39,
                exchangeRate,
            };
            await jp.releaseWin(trxId, "123", "1", [win], 5, true);
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 30,
                        "progressive": 70
                    },
                    "medium": {
                        "seed": -80,
                        "progressive": 0
                    },
                    "small": {
                        "seed": 10,
                        "progressive": 30
                    },
                },
                "seqId": 2
            });

            await unloadLastTransaction();
            let logs = await getWins();
            expect(logs[0]).to.deep.equal({
                "trxId": trxId,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "currency": "EUR",
                "jackpotId": "JP-TEST",
                "pool": "medium",
                "eventId": 0,
                "initialSeed": 100,
                "insertedAt": logs[0].insertedAt,
                "seed": 100,
                "progressive": 40,
                "totalSeed": 20,
                "totalProgressive": 40,
                "seedSinceLastWin": 20,
                "progressiveSinceLastWin": 40,
                "brandId": 1,
                "region": "eu",
                "gameId": "test",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "winAmount": 1023.39,
                "currencyRate": 0.1368,
                "roundId": "1",
                "gameCode": "test",
                "betAmount": 5,
                "status": "pending",
                "info": null,
            });

            const winParams = {
                "wins": [
                    {
                        "pool": "medium",
                        "seed": 0,
                        "title": "medium",
                        "totalSeed": 20,
                        "winAmount": 1023.39,
                        "initialSeed": 100,
                        "progressive": 40,
                        "currencyRate": 0.1368,
                        "seedSinceLastWin": 0,
                        "totalProgressive": 40,
                        "progressiveSinceLastWin": 0
                    }
                ],
                "gameId": "test",
                "region": "eu",
                "brandId": 1,
                "betAmount": 5,
                "roundId": "1",
                "gameCode": "test",
                "precision": 1000000000,
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY"
            };

            // confirms win
            const resolveWinResponse = await jp.resolveWin(
                trxId,
                "123",
                winParams,
                JackpotOperationStatus.RESOLVED
            );
            expect(resolveWinResponse).to.deep.equal({
                wins: [
                    {
                        pool: "medium",
                        seed: 0,
                        title: "medium",
                        totalSeed: 20,
                        winAmount: 1023.39,
                        initialSeed: 100,
                        progressive: 40,
                        currencyRate: 0.1368,
                        seedSinceLastWin: 0,
                        totalProgressive: 40,
                        progressiveSinceLastWin: 0,
                        status: "resolved"
                    }
                ],
                winPayouts: [
                    {
                        pool: "medium",
                        seed: 0,
                        title: "medium",
                        totalSeed: 20,
                        winAmount: 1023.39,
                        initialSeed: 100,
                        progressive: 40,
                        currencyRate: 0.1368,
                        seedSinceLastWin: 0,
                        totalProgressive: 40,
                        progressiveSinceLastWin: 0,
                        status: "resolved"
                    }
                ],
                winParams: {
                    "betAmount": 5,
                    "brandId": 1,
                    "gameCode": "test",
                    "gameId": "test",
                    "playerCode": "PL-CODE",
                    "playerCurrency": "CNY",
                    "precision": 1000000000,
                    "region": "eu",
                    "roundId": "1",
                    "wins": [
                        {
                            "currencyRate": 0.1368,
                            "initialSeed": 100,
                            "pool": "medium",
                            "progressive": 40,
                            "progressiveSinceLastWin": 0,
                            "seed": 0,
                            "seedSinceLastWin": 0,
                            "status": "resolved",
                            "title": "medium",
                            "totalProgressive": 40,
                            "totalSeed": 20,
                            "winAmount": 1023.39
                        }
                    ]
                }
            });
            const transactionList = config.trxStoragePrefix + ":wallet:transaction-list";
            const value = await client.lpop(transactionList);
            const trx = JSON.parse(value);
            trx.data = [{ walletKey: "jackpot:JP-TEST:EUR" }];
            await new WinConsumer().process(undefined, [trx]);
            logs = await getWins();
            expect(logs[0]).to.deep.equal({
                trxId: logs[0].trxId,
                externalId: "123",
                remoteTrxId: null,
                remoteTrxRegion: null,
                jackpotId: "JP-TEST",
                pool: "medium",
                eventId: 0,
                currency: "EUR",
                initialSeed: 100,
                insertedAt: logs[0].insertedAt,
                seed: 0,
                progressive: 40,
                totalSeed: 20,
                totalProgressive: 40,
                seedSinceLastWin: 0,
                progressiveSinceLastWin: 0,
                brandId: 1,
                region: "eu",
                gameId: "test",
                playerCode: "PL-CODE",
                playerCurrency: "CNY",
                winAmount: 1023.39,
                currencyRate: 0.1368,
                roundId: "1",
                gameCode: "test",
                betAmount: 5,
                status: "resolved",
                info: null,
            });

            // confirms contribution
            const resolveContributionResponse = await jp.resolveContribution(
                trxId,
                "123",
                contributionParams,
                JackpotOperationStatus.RESOLVED
            );
            expect(resolveContributionResponse).to.deep.equal({
                gameResult,
                "contributions": [
                    {
                        "pool": "small",
                        "progressive": 30,
                        "seed": 10,
                        "status": "resolved",
                        "totalProgressive": 0,
                        "totalSeed": 0
                    },
                    {
                        "pool": "medium",
                        "progressive": 40,
                        "seed": 20,
                        "status": "resolved",
                        "totalProgressive": 0,
                        "totalSeed": 0
                    },
                    {
                        "pool": "large",
                        "progressive": 70,
                        "seed": 30,
                        "status": "resolved",
                        "totalProgressive": 0,
                        "totalSeed": 0
                    }
                ],
                "playerContributions": [
                    {
                        "pool": "small",
                        "progressive": 30,
                        "seed": 10
                    },
                    {
                        "pool": "medium",
                        "progressive": 40,
                        "seed": 20
                    },
                    {
                        "pool": "large",
                        "progressive": 70,
                        "seed": 30
                    }
                ],
                "contributionParams": {
                    "brandId": 1,
                    "contributionAmount": 73099.41,
                    "contributions": [
                        {
                            "pool": "small",
                            "progressive": 30,
                            "seed": 10,
                            "status": "resolved",
                            "totalProgressive": 0,
                            "totalSeed": 0
                        },
                        {
                            "pool": "medium",
                            "progressive": 40,
                            "seed": 20,
                            "status": "resolved",
                            "totalProgressive": 0,
                            "totalSeed": 0,
                        },
                        {
                            "pool": "large",
                            "progressive": 70,
                            "seed": 30,
                            "status": "resolved",
                            "totalProgressive": 0,
                            "totalSeed": 0
                        }
                    ],
                    "currencyRate": 0.1368,
                    "gameCode": "test",
                    "gameData": {
                        "amount": 73099.41,
                        "exchangeRate": 0.1368,
                        "externalId": "123",
                        "roundId": "1",
                        "transactionId": trxId
                    },
                    "gameId": "test",
                    "gameResult": {
                        "pool": "large",
                        "type": "win",
                    },
                    "initialSeed": {
                        "large": 1000,
                        "medium": 100,
                        "small": 10
                    },
                    "playerCode": "PL-CODE",
                    "playerContributions": [
                        {
                            "pool": "small",
                            "progressive": 30,
                            "seed": 10
                        },
                        {
                            "pool": "medium",
                            "progressive": 40,
                            "seed": 20
                        },
                        {
                            "pool": "large",
                            "progressive": 70,
                            "seed": 30
                        }
                    ],
                    "playerCurrency": "CNY",
                    "precision": 1000000000,
                    "region": "eu",
                    "roundId": "1"
                }
            });

            await unloadLastTransaction();
            logs = await getContributions();
            expect(logs[0]).to.deep.equal({
                trxId: logs[0].trxId,
                externalId: "123",
                remoteTrxId: null,
                remoteTrxRegion: null,
                jackpotId: "JP-TEST",
                pool: "small",
                currency: "EUR",
                seed: 10,
                progressive: 30,
                playerSeed: 10,
                playerProgressive: 30,
                brandId: 1,
                region: "eu",
                gameId: "test",
                playerCode: "PL-CODE",
                playerCurrency: "CNY",
                contributionAmount: 73099.41,
                currencyRate: 0.1368,
                gameData: {
                    amount: 73099.41,
                    roundId: "1",
                    externalId: "123",
                    exchangeRate: 0.1368,
                    transactionId: logs[0].trxId
                },
                gameCode: "test",
                roundId: "1",
                insertedAt: logs[0].insertedAt,
                totalSeed: "0",
                totalProgressive: "0",
                status: "resolved"
            });
        });

        it("releases deferred win and rollbacks contribution and jp win", async () => {
            const jp = new JackpotWallet(params, jpInstance, exchangeRate, wallet);
            const game = await createTestJPGame(jp);
            const gameResult = {
                type: "win",
                pool: "medium"
            };

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 73099.41,
                exchangeRate,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = game.getContributions(10000, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            const contributed = await jp.contribute(
                request.transactionId,
                request,
                contributions,
                playerContributions,
                gameResult,
                true
            );

            let expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            expectedContributionsPayment = expectedContributionsPayment.map(item => { return { ...item, status: JackpotOperationStatus.PENDING };});

            const contributionParams = {
                "brandId": 1,
                "contributionAmount": 73099.41,
                "contributions": [
                    {
                        "pool": "small",
                        "progressive": 30,
                        "seed": 10,
                        "totalProgressive": 0,
                        "totalSeed": 0
                    },
                    {
                        "pool": "medium",
                        "progressive": 40,
                        "seed": 20,
                        "totalProgressive": 0,
                        "totalSeed": 0
                    },
                    {
                        "pool": "large",
                        "progressive": 70,
                        "seed": 30,
                        "totalProgressive": 0,
                        "totalSeed": 0
                    }
                ],
                "currencyRate": 0.1368,
                "gameCode": "test",
                "gameData": {
                    "amount": 73099.41,
                    "exchangeRate": 0.1368,
                    "externalId": "123",
                    "roundId": "1",
                    "transactionId": trxId
                },
                "gameId": "test",
                "gameResult": {
                    "pool": "medium",
                    "type": "win"
                },
                "initialSeed": {
                    "large": 1000,
                    "medium": 100,
                    "small": 10
                },
                "playerCode": "PL-CODE",
                "playerContributions": [
                    {
                        "pool": "small",
                        "progressive": 30,
                        "seed": 10
                    },
                    {
                        "pool": "medium",
                        "progressive": 40,
                        "seed": 20
                    },
                    {
                        "pool": "large",
                        "progressive": 70,
                        "seed": 30
                    }
                ],
                "playerCurrency": "CNY",
                "precision": 1000000000,
                "region": "eu",
                "roundId": "1",
            };

            const expectedContributionParams = contributionParams;
            expectedContributionParams.contributions = contributionParams.contributions.map(item => {
                return { ...item, status: JackpotOperationStatus.PENDING };
            });

            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult, playerContributions, contributionParams: expectedContributionParams }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 30,
                        "progressive": 70
                    },
                    "medium": {
                        "seed": 20,
                        "progressive": 40
                    },
                    "small": {
                        "seed": 10,
                        "progressive": 30
                    },
                },
                "seqId": 1
            });

            // release win
            const payouts = game.getWinPayouts([{ type: "win", pool: "medium" }], ticker.pools);
            expect(payouts.length).to.equal(1);
            expect(payouts[0]).to.deep.equal({
                "amount": 140,
                "pool": "medium",
                "progressive": 40,
                "seed": 100
            });
            const win: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...payouts[0],
                playerAmount: 1023.39,
                exchangeRate,
            };
            await jp.releaseWin(trxId, "123", "1", [win], 5, true);
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 30,
                        "progressive": 70
                    },
                    "medium": {
                        "seed": -80,
                        "progressive": 0
                    },
                    "small": {
                        "seed": 10,
                        "progressive": 30
                    },
                },
                "seqId": 2
            });

            await unloadLastTransaction();
            let logs = await getWins();
            expect(logs[0]).to.deep.equal({
                "trxId": trxId,
                "externalId": "123",
                "remoteTrxId": null,
                "remoteTrxRegion": null,
                "currency": "EUR",
                "jackpotId": "JP-TEST",
                "pool": "medium",
                "eventId": 0,
                "initialSeed": 100,
                "insertedAt": logs[0].insertedAt,
                "seed": 100,
                "progressive": 40,
                "totalSeed": 20,
                "totalProgressive": 40,
                "seedSinceLastWin": 20,
                "progressiveSinceLastWin": 40,
                "brandId": 1,
                "region": "eu",
                "gameId": "test",
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY",
                "winAmount": 1023.39,
                "currencyRate": 0.1368,
                "roundId": "1",
                "gameCode": "test",
                "betAmount": 5,
                "status": "pending",
                "info": null,
            });

            // rollback contribution
            const resolveContributionResponse = await jp.resolveContribution(
                trxId,
                "123",
                contributionParams,
                JackpotOperationStatus.REJECTED
            );
            expect(resolveContributionResponse).to.deep.equal({
                gameResult,
                "contributions": [
                    {
                        "pool": "small",
                        "progressive": 30,
                        "seed": 10,
                        "status": "rejected",
                        "totalProgressive": 30,
                        "totalSeed": 10
                    },
                    {
                        "pool": "medium",
                        "progressive": 40,
                        "seed": 20,
                        "status": "rejected",
                        "totalProgressive": 0,
                        "totalSeed": -80
                    },
                    {
                        "pool": "large",
                        "progressive": 70,
                        "seed": 30,
                        "status": "rejected",
                        "totalProgressive": 70,
                        "totalSeed": 30
                    }
                ],
                "playerContributions": [
                    {
                        "pool": "small",
                        "progressive": 30,
                        "seed": 10
                    },
                    {
                        "pool": "medium",
                        "progressive": 40,
                        "seed": 20
                    },
                    {
                        "pool": "large",
                        "progressive": 70,
                        "seed": 30
                    }
                ],
                "contributionParams": {
                    "brandId": 1,
                    "contributionAmount": 73099.41,
                    "contributions": [
                        {
                            "pool": "small",
                            "progressive": 30,
                            "seed": 10,
                            "status": "rejected",
                            "totalProgressive": 30,
                            "totalSeed": 10,
                        },
                        {
                            "pool": "medium",
                            "progressive": 40,
                            "seed": 20,
                            "status": "rejected",
                            "totalProgressive": 0,
                            "totalSeed": -80,
                        },
                        {
                            "pool": "large",
                            "progressive": 70,
                            "seed": 30,
                            "status": "rejected",
                            "totalProgressive": 70,
                            "totalSeed": 30
                        }
                    ],
                    "currencyRate": 0.1368,
                    "gameCode": "test",
                    "gameData": {
                        "amount": 73099.41,
                        "exchangeRate": 0.1368,
                        "externalId": "123",
                        "roundId": "1",
                        "transactionId": trxId
                    },
                    "gameId": "test",
                    "gameResult": {
                        "pool": "medium",
                        "type": "win",
                    },
                    "initialSeed": {
                        "large": 1000,
                        "medium": 100,
                        "small": 10
                    },
                    "playerCode": "PL-CODE",
                    "playerContributions": [
                        {
                            "pool": "small",
                            "progressive": 30,
                            "seed": 10
                        },
                        {
                            "pool": "medium",
                            "progressive": 40,
                            "seed": 20
                        },
                        {
                            "pool": "large",
                            "progressive": 70,
                            "seed": 30
                        }
                    ],
                    "playerCurrency": "CNY",
                    "precision": 1000000000,
                    "region": "eu",
                    "roundId": "1"
                }
            });

            await unloadLastTransaction();
            logs = await getContributions();
            expect(logs[0]).to.deep.equal({
                trxId: logs[0].trxId,
                externalId: "123",
                remoteTrxId: null,
                remoteTrxRegion: null,
                jackpotId: "JP-TEST",
                pool: "small",
                currency: "EUR",
                seed: -10,
                progressive: -30,
                playerSeed: 10,
                playerProgressive: 30,
                brandId: 1,
                region: "eu",
                gameId: "test",
                playerCode: "PL-CODE",
                playerCurrency: "CNY",
                contributionAmount: 73099.41,
                currencyRate: 0.1368,
                gameData: {
                    amount: 73099.41,
                    roundId: "1",
                    externalId: "123",
                    exchangeRate: 0.1368,
                    transactionId: logs[0].trxId
                },
                gameCode: "test",
                roundId: "1",
                insertedAt: logs[0].insertedAt,
                totalSeed: "10",
                totalProgressive: "30",
                status: "rejected"
            });

            const winParams = {
                "wins": [
                    {
                        "pool": "medium",
                        "seed": 0,
                        "title": "medium",
                        "totalSeed": 20,
                        "winAmount": 1023.39,
                        "initialSeed": 100,
                        "progressive": 40,
                        "currencyRate": 0.1368,
                        "seedSinceLastWin": 0,
                        "totalProgressive": 40,
                        "progressiveSinceLastWin": 0
                    }
                ],
                "gameId": "test",
                "region": "eu",
                "brandId": 1,
                "roundId": "1",
                "gameCode": "test",
                "precision": 1000000000,
                "playerCode": "PL-CODE",
                "playerCurrency": "CNY"
            };

            // rollback win
            const resolveWinResponse = await jp.resolveWin(
                trxId,
                "123",
                winParams,
                JackpotOperationStatus.REJECTED
            );
            expect(resolveWinResponse).to.deep.equal({
                wins: [
                    {
                        pool: "medium",
                        seed: 0,
                        title: "medium",
                        totalSeed: 20,
                        winAmount: 1023.39,
                        initialSeed: 100,
                        progressive: 40,
                        currencyRate: 0.1368,
                        seedSinceLastWin: 0,
                        totalProgressive: 40,
                        progressiveSinceLastWin: 0,
                        status: "rejected"
                    }
                ],
                winPayouts: [
                    {
                        pool: "medium",
                        seed: 0,
                        title: "medium",
                        totalSeed: 20,
                        winAmount: 1023.39,
                        initialSeed: 100,
                        progressive: 40,
                        currencyRate: 0.1368,
                        seedSinceLastWin: 0,
                        totalProgressive: 40,
                        progressiveSinceLastWin: 0,
                        status: "rejected"
                    }
                ],
                "winParams": {
                    "brandId": 1,
                    "gameCode": "test",
                    "gameId": "test",
                    "playerCode": "PL-CODE",
                    "playerCurrency": "CNY",
                    "precision": 1000000000,
                    "region": "eu",
                    "roundId": "1",
                    "wins": [
                        {
                            "currencyRate": 0.1368,
                            "initialSeed": 100,
                            "pool": "medium",
                            "progressive": 40,
                            "progressiveSinceLastWin": 0,
                            "seed": 0,
                            "seedSinceLastWin": 0,
                            "status": "rejected",
                            "title": "medium",
                            "totalProgressive": 40,
                            "totalSeed": 20,
                            "winAmount": 1023.39
                        }
                    ]
                }
            });

            const transactionList = config.trxStoragePrefix + ":wallet:transaction-list";
            const value = await client.lpop(transactionList);
            const trx = JSON.parse(value);
            trx.data = [{ walletKey: "jackpot:JP-TEST:EUR" }];
            await new WinConsumer().process(undefined, [trx]);

            logs = await getWins();
            expect(logs[0]).to.deep.equal({
                trxId: logs[0].trxId,
                externalId: "123",
                remoteTrxId: null,
                remoteTrxRegion: null,
                jackpotId: "JP-TEST",
                pool: "medium",
                eventId: 0,
                currency: "EUR",
                initialSeed: 100,
                insertedAt: logs[0].insertedAt,
                seed: 0,
                progressive: 40,
                totalSeed: 20,
                totalProgressive: 40,
                seedSinceLastWin: 0,
                progressiveSinceLastWin: 0,
                brandId: 1,
                region: "eu",
                gameId: "test",
                playerCode: "PL-CODE",
                playerCurrency: "CNY",
                winAmount: 1023.39,
                currencyRate: 0.1368,
                roundId: "1",
                gameCode: "test",
                betAmount: 5,
                status: "rejected",
                info: null,
            });
        });
    });

    describe("check seeds", function() {

        it("check seeds", async () => {
            const module: JackpotWallet = new JackpotWallet(params, jpInstance, 1, inMemoryWallet);
            const game = await createTestJPGame(module);
            let result;
            for (let pos = 0; pos < 11000; pos++) {
                const request = {
                    externalId: "123",
                    transactionId: `${pos + 1}`,
                    amount: 1,
                    roundId: "1"
                };
                let ticker = await module.getTicker();
                const contributions = game.getContributions(request.amount, request, ticker.pools);
                const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
                const playerContributions = createPlayerContributions(contributions);
                result = await module.contribute(request.transactionId, request, contributions, playerContributions);

                ticker = await module.getTicker();
                expectedContributionsPayment.forEach(item => {
                    item.totalProgressive = ticker.pools[item.pool].progressive || 0;
                    item.totalSeed = ticker.pools[item.pool].seed || 0;
                });
                expect(result).deep.equals(
                    { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
                );
            }

            const jpWallet: IWallet = await inMemoryWallet.get(
                `jackpot:${jpInstance.id}:${jpInstance.definition.currency}`);

            const keys = Object.keys(jpWallet.data.accounts);
            keys.forEach(key => {
                delete jpWallet.data.accounts[key].version;
            });
            // get wallet
            expect(jpWallet.data.accounts).deep.eq({
                "large": {
                    "progressive": ***********,
                    "seed": ***********,
                    "progressiveSinceLastWin": ***********,
                    "seedSinceLastWin": ***********,
                },
                "medium": {
                    "progressive": ***********,
                    "seed": ***********,
                    "progressiveSinceLastWin": ***********,
                    "seedSinceLastWin": ***********,
                },
                "small": {
                    "progressive": ***********,
                    "seed": ***********,
                    "progressiveSinceLastWin": ***********,
                    "seedSinceLastWin": ***********,
                },
                [META_INF_ACCOUNT]: {
                    "seqId": 11000,
                    "disabled": 0
                }
            });
        });
    });

    describe("Pool state", () => {

        it("gets pool state", async () => {
            const jp = new JackpotWallet(params, jpInstance, 1, wallet);
            const result = await jp.getPoolState("large");
            expect(result).to.deep.equal({
                "poolId": "large",
                "initialSeed": 1000,
                "progressive": 0,
                "seed": 0,
            });
        });

        it("get all pools", async () => {
            const jp = new JackpotWallet(params, jpInstance, 1, wallet);
            const result = await jp.getAllPoolsState();
            expect(result).to.deep.equal([
                {
                    "initialSeed": 10,
                    "poolId": "small",
                    "progressive": 0,
                    "seed": 0,
                },
                {
                    "initialSeed": 100,
                    "poolId": "medium",
                    "progressive": 0,
                    "seed": 0,
                },
                {
                    "initialSeed": 1000,
                    "poolId": "large",
                    "progressive": 0,
                    "seed": 0,
                }
            ]);
        });

        it("updates pool for not test jackpot instance - negative", async () => {
            const jp = new JackpotWallet(params, jpInstance, 1, wallet);
            await expect(jp.updatePool("N", "N", { seed: 0, progressive: 0 }))
                .to.be.rejectedWith(ValidationError);
        });

        it("updates pool for production environment - negative", async () => {

            config.environment = "production";

            const jp = new JackpotWallet(params, JP_TEST, 1, wallet);
            await expect(jp.updatePool("N", "N", { seed: 0, progressive: 0 }))
                .to.be.rejectedWith(ValidationError);

            config.environment = "development";
        });

    });

    describe("Limited jp contribution/release", async () => {

        it("Contribute larger than maxContributionAmount", async () => {
            const jp = new JackpotWallet(params, JP_WITH_LIMITED_CONTRIBUTION, 1, wallet);
            const game = await createTestJPGame(jp);

            const trxId = await wallet.generateTransactionId();

            const contribution = {
                transactionId: trxId,
                amount: 1000000,
                exchangeRate: 1,
                roundId: "1"
            };

            const ticker = await jp.getTicker();
            const contributions = game.getContributions(contribution.amount, contribution, ticker.pools);
            const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            const playerContributions = createPlayerContributions(contributions);
            const success = await jp.contribute(contribution.transactionId, contribution,
                contributions, playerContributions);
            expect(success).deep.equals(
                { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );

            const result = await jp.getTicker();

            // contribution Limit is 1000 ,  so seed: 1000000 ~> 1000 * 0.3% = 3
            expect(result).to.deep.equal({
                "currency": "EUR",
                "id": "JP-TEST-2",
                "pools": {
                    "large": {
                        "progressive": 7,
                        "seed": 3,
                    },
                    "medium": {
                        "progressive": 0,
                        "seed": 0,
                    },
                    "small": {
                        "progressive": 0,
                        "seed": 0,
                    }
                },
                "seqId": 1
            });
        });

        it("Contribute larger than maxContributionAmount", async () => {
            const jp = new JackpotWallet(params, JP_WITH_LIMITED_CONTRIBUTION, 1, wallet);

            const jpWinrequest: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                pool: "small",
                amount: 10,
                playerAmount: 10,
                exchangeRate: 1,
                seed: 10,
                progressive: 0
            };

            const trxId1 = await wallet.generateTransactionId();
            await jp.releaseWin(trxId1, "123", "1", [jpWinrequest]);

            const result1 = await jp.getTicker();

            expect(result1).to.deep.equal({
                "currency": "EUR",
                "id": "JP-TEST-2",
                "pools": {
                    "large": {
                        "progressive": 0,
                        "seed": 0,
                    },
                    "medium": {
                        "progressive": 0,
                        "seed": 0,
                    },
                    "small": {
                        "progressive": 0,
                        "seed": -10,
                    }
                },
                "seqId": 1
            });

            const trxId2 = await wallet.generateTransactionId();
            await jp.releaseWin(trxId2, "123", "1", [jpWinrequest]);

            const result2 = await jp.getTicker();

            expect(result2).to.deep.equal({
                "currency": "EUR",
                "id": "JP-TEST-2",
                "pools": {
                    "large": {
                        "progressive": 0,
                        "seed": 0,
                    },
                    "medium": {
                        "progressive": 0,
                        "seed": 0,
                    },
                    "small": {
                        "progressive": 0,
                        "seed": -20,
                    }
                },
                "seqId": 2
            });

            const trxId3 = await wallet.generateTransactionId();
            await expect(jp.releaseWin(trxId3, "123", "1", [jpWinrequest])).to.be
                .rejectedWith(InsufficientJackpotBalance);

        });

    });

    describe("Contributions since last win", () => {
        async function getAccount(): Promise<IAccount> {
            const trxId = await wallet.generateTransactionId();
            const transaction: ITransaction = await wallet.startTransaction(trxId);
            const walletKey = `jackpot:${jpInstance.id}:${jpInstance.definition.currency}`;
            const jpWallet: IWallet = await transaction.getWallet(walletKey);
            const result: IAccount = jpWallet.accounts.get("small");
            await transaction.commit();
            return result;
        }

        it("seed and progressive since last win should be zero after win", async () => {
            const jp = new JackpotWallet(params, jpInstance, 1, wallet);
            const game = await createTestJPGame(jp);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 5000,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = game.getContributions(request.amount, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            await jp.contribute(request.transactionId, request, contributions, playerContributions);
            let account = await getAccount();
            expect(account.get("seedSinceLastWin")).to.equal(**********);
            ticker = await jp.getTicker();
            const payouts: WinPayout[] = await game.getWinPayouts([{ pool: "small", type: "win" }], ticker.pools);
            const win: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...payouts[0],
                playerAmount: 1023.39,
                exchangeRate: 0.1368,
            };
            await jp.releaseWin(trxId, "123", "1", [win]);
            account = await getAccount();
            expect(account.get("seedSinceLastWin")).to.equal(undefined);
            expect(account.get("progressiveSinceLastWin")).to.equal(undefined);
        });

        it("should be off for special games", async () => {
            const jp = new JackpotWallet(params, JP_WITH_DYNAMIC_CONTRIBUTION, 1, wallet);
            const game = await createTestJPGame(jp);

            // contribute
            let trxId = await wallet.generateTransactionId();
            let request = {
                externalId: "123",
                transactionId: trxId,
                amount: 5000,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            let contributions = game.getContributions(request.amount, request, ticker.pools);
            let playerContributions = createPlayerContributions(contributions);
            await jp.contribute(request.transactionId, request, contributions, playerContributions);

            trxId = await wallet.generateTransactionId();
            request = {
                externalId: "123",
                transactionId: trxId,
                amount: 15000,
                roundId: "1"
            };
            ticker = await jp.getTicker();
            contributions = game.getContributions(request.amount, request, ticker.pools);
            playerContributions = createPlayerContributions(contributions);
            await jp.contribute(request.transactionId, request, contributions, playerContributions);
            const account = await getAccount();
            expect(account.get("seedSinceLastWin")).to.equal(undefined);
            expect(account.get("progressiveSinceLastWin")).to.equal(undefined);
        });

        it("two jackpot wins one by one different transactions", async () => {
            const jp = new JackpotWallet(params, jpInstance, 1, wallet);
            const game = await createTestJPGame(jp);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 15000,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = game.getContributions(request.amount, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            await jp.contribute(request.transactionId, request, contributions, playerContributions);
            let account = await getAccount();
            expect(account.get("seedSinceLastWin")).to.equal(1**********);
            expect(account.get("progressiveSinceLastWin")).to.equal(4**********);
            ticker = await jp.getTicker();
            const firstPayout: WinPayout[] = await game.getWinPayouts([{ pool: "small", type: "win" }], ticker.pools);
            const firstWin: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...firstPayout[0],
                playerAmount: 1023.39,
                exchangeRate: 0.1368,
            };
            await jp.releaseWin(trxId, "123", "1", [firstWin]);
            account = await getAccount();
            expect(account.get("seedSinceLastWin")).to.equal(undefined);
            expect(account.get("progressiveSinceLastWin")).to.equal(undefined);
            ticker = await jp.getTicker();
            const secondPayout: WinPayout[] = await game.getWinPayouts([{ pool: "small", type: "win" }], ticker.pools);
            const secondWin: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...secondPayout[0],
                playerAmount: 1023.39,
                exchangeRate: 0.1368,
            };
            const secondTrxId = await wallet.generateTransactionId();
            await jp.releaseWin(secondTrxId, "123", "1", [secondWin]);
            account = await getAccount();
            expect(account.get("seedSinceLastWin")).to.equal(undefined);
            expect(account.get("progressiveSinceLastWin")).to.equal(undefined);
        });

        it("two jackpot wins one by one single transaction", async () => {
            const jp = new JackpotWallet(params, jpInstance, 1, wallet);
            const game = await createTestJPGame(jp);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 15000,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = game.getContributions(request.amount, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            await jp.contribute(request.transactionId, request, contributions, playerContributions);
            let account = await getAccount();
            expect(account.get("seedSinceLastWin")).to.equal(1**********);
            ticker = await jp.getTicker();
            const firstPayout: WinPayout[] = await game.getWinPayouts([{ pool: "small", type: "win" }], ticker.pools);
            const firstWin: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...firstPayout[0],
                playerAmount: 1023.39,
                exchangeRate: 0.1368,
            };
            const secondWin: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                pool: "small",
                amount: 10,
                seed: 10,
                progressive: 0,
                playerAmount: 1023.39,
                exchangeRate: 0.1368
            };
            await jp.releaseWin(trxId, "123", "1", [firstWin, secondWin]);
            await unloadLastTransaction();
            const logs = await getWins();
            expect(logs[0].seedSinceLastWin).to.equal(15);
            expect(logs[1].seedSinceLastWin).to.equal(0);
            account = await getAccount();
            expect(account.get("seedSinceLastWin")).to.equal(undefined);
        });

        it("contribute, win, contribute", async () => {
            const jp = new JackpotWallet(params, jpInstance, 1, wallet);
            const game = await createTestJPGame(jp);

            // contribute
            const trxId = await wallet.generateTransactionId();
            let request = {
                externalId: "123",
                transactionId: trxId,
                amount: 15000,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            let contributions = game.getContributions(request.amount, request, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);
            await jp.contribute(request.transactionId, request, contributions, playerContributions);
            let account = await getAccount();
            expect(account.get("seedSinceLastWin")).to.equal(1**********);
            ticker = await jp.getTicker();
            const firstPayout: WinPayout[] = await game.getWinPayouts([{ pool: "small", type: "win" }], ticker.pools);
            const firstWin: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                ...firstPayout[0],
                playerAmount: 1023.39,
                exchangeRate: 0.1368,
            };
            await jp.releaseWin(trxId, "123", "1", [firstWin]);
            account = await getAccount();
            expect(account.get("seedSinceLastWin")).to.equal(undefined);
            const secondTrxId = await wallet.generateTransactionId();
            request = {
                externalId: "123",
                transactionId: secondTrxId,
                amount: 10000,
                roundId: "1"
            };
            ticker = await jp.getTicker();
            contributions = game.getContributions(request.amount, request, ticker.pools);
            await jp.contribute(request.transactionId, request,
                contributions, createPlayerContributions(contributions));
            account = await getAccount();
            expect(account.get("seedSinceLastWin")).to.equal(***********);
        });

    });

    describe("Enable/disable", async () => {

        it("Disable jackpot", async () => {
            const jp = new JackpotWallet(params, JP_WITH_DYNAMIC_CONTRIBUTION, 1, wallet);

            await jp.disable();

            const isDisabled = await jp.isDisabled();
            expect(isDisabled).to.be.true;

            const ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "currency": "EUR",
                "id": "JP-TEST",
                "pools": {
                    "large": {
                        "progressive": 0,
                        "seed": 0,
                    },
                    "medium": {
                        "progressive": 0,
                        "seed": 0,
                    },
                    "small": {
                        "progressive": 0,
                        "seed": 0,
                    }
                },
                "seqId": 0,
                "isDisabled": true
            });
        });

        it("Don't contribute after disable", async () => {
            const instance = { ...JP_WITH_DYNAMIC_CONTRIBUTION };
            const jp = new JackpotWallet(params, instance, 1, wallet);

            await jp.disable();

            const game = await createTestJPGame(jp);

            const trxId = await wallet.generateTransactionId();

            const contribution = {
                transactionId: trxId,
                amount: 1000000,
                exchangeRate: 1,
                roundId: "1"
            };

            const ticker = await jp.getTicker();
            const contributions = game.getContributions(contribution.amount, contribution, ticker.pools);
            const playerContributions = createPlayerContributions(contributions);

            instance.disableMode = 0;
            instance.isDisabled = true;

            const result = await jp.contribute(contribution.transactionId, contribution,
                contributions, playerContributions);
            expect(result).to.deep.equal({
                gameResult: undefined,
                contributions: undefined,
                playerContributions: undefined
            });
        });

        it("don't win after disable", async () => {
            const instance = { ...JP_WITH_DYNAMIC_CONTRIBUTION };
            const jp = new JackpotWallet(params, instance, exchangeRate, wallet);
            const gameWithoutSeed = await createTestJPGame(jp);

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 10000,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = gameWithoutSeed.getContributions(request.amount, request, ticker.pools);
            const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            const playerContributions = createPlayerContributions(contributions);
            const contributed = await jp.contribute(request.transactionId, request,
                contributions, playerContributions);
            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 30,
                        "progressive": 70
                    },
                    "medium": {
                        "seed": 20,
                        "progressive": 40
                    },
                    "small": {
                        "seed": 10,
                        "progressive": 30
                    },
                },
                "seqId": 1
            });

            // disable jackpot
            instance.disableMode = 0;
            instance.isDisabled = true;
            await jp.disable();

            // release win
            const win: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                amount: 40,
                pool: "medium",
                progressive: 40,
                seed: 0,
                playerAmount: 1023.39,
                exchangeRate: 0.1368,
            };
            const result = await jp.releaseWin(trxId, "123", "1", [win]);
            expect(result).to.deep.equal({
                wins: undefined,
                winPayouts: undefined
            });
        });

        it("disable on next win", async () => {
            const instance = { ...JP_WITH_DYNAMIC_CONTRIBUTION };
            const jp = new JackpotWallet(params, instance, exchangeRate, wallet);
            const gameWithoutSeed = await createTestJPGame(jp);

            // disable jackpot
            instance.disableMode = 1;
            instance.isDisabled = true;

            // contribute
            const trxId = await wallet.generateTransactionId();
            const request = {
                externalId: "123",
                transactionId: trxId,
                amount: 10000,
                roundId: "1"
            };
            let ticker = await jp.getTicker();
            const contributions = gameWithoutSeed.getContributions(request.amount, request, ticker.pools);
            const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
            const playerContributions = createPlayerContributions(contributions);
            const contributed = await jp.contribute(request.transactionId, request,
                contributions, playerContributions);
            expect(contributed).deep.equals(
                { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 30,
                        "progressive": 70
                    },
                    "medium": {
                        "seed": 20,
                        "progressive": 40
                    },
                    "small": {
                        "seed": 10,
                        "progressive": 30
                    },
                },
                "seqId": 1
            });

            // release win
            const win: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                amount: 40,
                pool: "medium",
                progressive: 40,
                seed: 0,
                playerAmount: 1023.39,
                exchangeRate: 0.1368,
            };
            const result = await jp.releaseWin(trxId, "123", "1", [win]);
            expect(result.wins).to.deep.equal([win]);
            ticker = await jp.getTicker();
            expect(ticker).to.deep.equal({
                "id": jpInstance.id,
                "currency": "EUR",
                "pools": {
                    "large": {
                        "seed": 30,
                        "progressive": 70
                    },
                    "medium": {
                        "seed": 20,
                        "progressive": 0
                    },
                    "small": {
                        "seed": 10,
                        "progressive": 30
                    },
                },
                "seqId": 2,
                "isDisabled": true
            });
        });

        it("win after disable on next win for Instant JP", async () => {
            const instance = { ...JP_INSTANT };

            const params1 = { ...params, playerCode: "PL-CODE-1" };
            const jp1 = new JackpotWallet(params1, instance, exchangeRate, wallet);
            const game1 = await createTestJPGame(jp1);

            const params2 = { ...params, playerCode: "PL-CODE-2" };
            const jp2 = new JackpotWallet(params2, instance, exchangeRate, wallet);
            const game2 = await createTestJPGame(jp2);

            // disable jackpot
            instance.disableMode = 1;
            instance.isDisabled = true;

            // contribute for the 1st player
            const trxId1 = await wallet.generateTransactionId();
            const request1 = {
                externalId: "1231",
                transactionId: trxId1,
                amount: 10000,
                roundId: "1"
            };
            let ticker1 = await jp1.getTicker();
            const contributions1 = game1.getContributions(request1.amount, request1, ticker1.pools);
            const expectedContributionsPayment1 = convertContributionsToContributionsPayout(contributions1);
            const playerContributions1 = createPlayerContributions(contributions1);
            const contributed1 = await jp1.contribute(request1.transactionId,
                request1,
                contributions1,
                playerContributions1);
            expect(contributed1).deep.equals({
                    contributions: expectedContributionsPayment1,
                    gameResult: undefined,
                    playerContributions: playerContributions1
                }
            );
            ticker1 = await jp1.getTicker();
            expect(ticker1).to.deep.equal({
                "id": instance.id,
                "currency": "EUR",
                "pools": {
                    "A": {
                        "progressive": 80,
                        "seed": 40,
                    },
                    "B": {
                        "progressive": 70,
                        "seed": 30,
                    },
                    "C": {
                        "progressive": 40,
                        "seed": 20,
                    },
                    "D": {
                        "progressive": 30,
                        "seed": 10,
                    },
                },
                "seqId": 1
            });

            // contribute for the 2nd player
            const trxId2 = await wallet.generateTransactionId();
            const request2 = {
                externalId: "1232",
                transactionId: trxId2,
                amount: 10000,
                roundId: "1"
            };
            let ticker2 = await jp2.getTicker();
            const contributions2 = game2.getContributions(request2.amount, request2, ticker2.pools);
            const totals2 =
                {
                    "A": { totalSeed: 40, totalProgressive: 80 },
                    "B": { totalSeed: 30, totalProgressive: 70 },
                    "C": { totalSeed: 20, totalProgressive: 40 },
                    "D": { totalSeed: 10, totalProgressive: 30 },
                };
            const expectedContributionsPayment2 = convertContributionsToContributionsPayoutWithTotals(contributions2, totals2);
            const playerContributions2 = createPlayerContributions(contributions2);
            const contributed2 = await jp2.contribute(request2.transactionId,
                request2,
                contributions2,
                playerContributions2);
            expect(contributed2).deep.equals({
                    contributions: expectedContributionsPayment2,
                    gameResult: undefined,
                    playerContributions: playerContributions2
                }
            );
            ticker2 = await jp2.getTicker();
            expect(ticker2).to.deep.equal({
                "id": instance.id,
                "currency": "EUR",
                "pools": {
                    "A": {
                        "progressive": 160,
                        "seed": 80
                    },
                    "B": {
                        "progressive": 140,
                        "seed": 60
                    },
                    "C": {
                        "progressive": 80,
                        "seed": 40
                    },
                    "D": {
                        "progressive": 70,
                        "seed": 10
                    },
                },
                "seqId": 2
            });

            // release win for the 1st player
            const win1: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                amount: 30,
                pool: "D",
                progressive: 30,
                seed: 10,
                playerAmount: 1023.39,
                exchangeRate: 0.1368,
            };
            const result1 = await jp1.releaseWin(trxId1, "1231", "1", [win1]);
            expect(result1.wins).to.deep.equal([win1]);
            ticker1 = await jp1.getTicker();
            expect(ticker1).to.deep.equal({
                "id": instance.id,
                "currency": "EUR",
                "pools": {
                    "A": {
                        "progressive": 160,
                        "seed": 80,
                    },
                    "B": {
                        "progressive": 140,
                        "seed": 60,
                    },
                    "C": {
                        "progressive": 80,
                        "seed": 40,
                    },
                    "D": {
                        "progressive": 40,
                        "seed": 0,
                    },
                },
                "seqId": 3,
                "isDisabled": true
            });

            // release win for the 2nd player
            const win2: JackpotPlayerWin = {
                type: JackpotWinType.PLAYER,
                amount: 30,
                pool: "D",
                progressive: 30,
                seed: 10,
                playerAmount: 1023.39,
                exchangeRate: 0.1368,
            };
            const result2 = await jp2.releaseWin(trxId2, "1232", "1", [win2]);
            expect(result2.wins).to.deep.equal([win2]);
            ticker2 = await jp2.getTicker();
            expect(ticker2).to.deep.equal({
                "id": instance.id,
                "currency": "EUR",
                "pools": {
                    "A": {
                        "progressive": 160,
                        "seed": 80,
                    },
                    "B": {
                        "progressive": 140,
                        "seed": 60,
                    },
                    "C": {
                        "progressive": 80,
                        "seed": 40,
                    },
                    "D": {
                        "progressive": 10,
                        "seed": -10,
                    },
                },
                "seqId": 4,
                "isDisabled": true
            });
        });

        it("Enable jackpot", async () => {
            const jp = new JackpotWallet(params, JP_WITH_DYNAMIC_CONTRIBUTION, 1, wallet);

            // disable
            await jp.disable();

            let isDisabled = await jp.isDisabled();
            expect(isDisabled).to.be.true;

            let ticker = await jp.getTicker();
            expect(ticker.isDisabled).to.be.true;

            // enable #1
            await jp.enable();

            isDisabled = await jp.isDisabled();
            expect(isDisabled).to.be.false;

            ticker = await jp.getTicker();
            expect(ticker.isDisabled).to.be.undefined;

            // enable #2
            await jp.enable();

            isDisabled = await jp.isDisabled();
            expect(isDisabled).to.be.false;

            ticker = await jp.getTicker();
            expect(ticker.isDisabled).to.be.undefined;

            // disable on next win
            await jp.disable();

            isDisabled = await jp.isDisabled();
            expect(isDisabled).to.be.true;

            ticker = await jp.getTicker();
            expect(ticker.isDisabled).to.be.true;

            // enable
            await jp.enable();

            isDisabled = await jp.isDisabled();
            expect(isDisabled).to.be.false;

            ticker = await jp.getTicker();
            expect(ticker.isDisabled).to.be.undefined;
        });
    });

    describe("Game action", async () => {

        it("Validation", async () => {
            const jp = new JackpotWallet(params, JP_WITH_DYNAMIC_CONTRIBUTION, 1, wallet);
            let wrongChanges: PropertyChange[] = [{} as any];
            await expect(jp.applyPropertyChanges(wrongChanges)).to.be.rejectedWith(ValidationError);

            wrongChanges = [
                {
                    property: "aaa",
                    operation: { operation: Operation.DEC, max: 3 },
                    pool: "pool",
                    amount: 3
                } as any
            ];
            await expect(jp.applyPropertyChanges(wrongChanges)).to.be.rejectedWith(ValidationError);

            wrongChanges = [
                {
                    property: "aaa",
                    operation: { operation: Operation.DEC, min: 3 },
                    pool: "pool",
                    amount: 3.4
                }
            ];
            await expect(jp.applyPropertyChanges(wrongChanges)).to.be.rejectedWith(ValidationError);

            wrongChanges = [
                {
                    property: "seed",
                    operation: { operation: Operation.INC, max: 2 },
                    pool: "small",
                    amount: 2
                }
            ];
            await expect(jp.applyPropertyChanges(wrongChanges)).to.be.rejectedWith(ValidationError);
        });

        it("Inc operation - happy path", async () => {
            const instance = { ...JP_WITH_DYNAMIC_CONTRIBUTION };
            const jp = new JackpotWallet(params, instance, 1, wallet);

            const changes: PropertyChange[] = [
                {
                    property: "state",
                    operation: { operation: Operation.INC, max: 2 },
                    pool: "small",
                    amount: 2
                },
                {
                    property: "state",
                    operation: { operation: Operation.INC, max: 2 },
                    pool: "medium",
                    amount: 1
                }
            ];
            const result = await jp.applyPropertyChanges(changes);
            expect(result).deep.equal(["small.state", "medium.state"]);
            const ticker = await jp.getTicker();
            expect(ticker).deep.equal({
                "currency": "EUR",
                "id": "JP-TEST",
                "pools": {
                    "large": {
                        "progressive": 0,
                        "seed": 0,
                    },
                    "medium": {
                        "progressive": 0,
                        "seed": 0,
                        "state": 1,
                    },
                    "small": {
                        "progressive": 0,
                        "seed": 0,
                        "state": 2
                    },
                },
                "seqId": 1
            });
            const trxId = await wallet.generateTransactionId();
            const trx = await wallet.startTransaction(trxId);
            const instanceWallet = await trx.getWallet(getWalletKey(instance));
            const account: IAccount = instanceWallet.accounts.get("small");
            const value = await account.get("state");
            expect(value).to.equal(2);
        });

        it("Inc operation - skip on max", async () => {
            const instance = { ...JP_WITH_DYNAMIC_CONTRIBUTION };
            const jp = new JackpotWallet(params, instance, 1, wallet);

            const changes: PropertyChange[] = [
                {
                    property: "state",
                    operation: { operation: Operation.INC, max: 1 },
                    pool: "small",
                    amount: 2
                },
                {
                    property: "state",
                    operation: { operation: Operation.INC, max: 2 },
                    pool: "medium",
                    amount: 1
                }

            ];
            const result = await jp.applyPropertyChanges(changes);
            expect(result).deep.equal(["medium.state"]);
        });

        it("Inc operation - concurrently", async () => {
            const instance = { ...JP_WITH_DYNAMIC_CONTRIBUTION };
            const jp = new JackpotWallet(params, instance, 1, wallet);

            const changes: PropertyChange[] = [
                {
                    property: "state",
                    operation: { operation: Operation.INC, max: 2 },
                    pool: "small",
                    amount: 2
                },
                {
                    property: "state",
                    operation: { operation: Operation.INC, max: 2 },
                    pool: "small",
                    amount: 1
                }
            ];
            await expect(jp.applyPropertyChanges(changes)).to.be.rejectedWith(MAX_CAPACITY_REACHED);
        });

        it("Dec operation", async () => {
            const instance = { ...JP_WITH_DYNAMIC_CONTRIBUTION };
            const jp = new JackpotWallet(params, instance, 1, wallet);
            const trxId = await wallet.generateTransactionId();
            const trx = await wallet.startTransaction(trxId);
            const instanceWallet = await trx.getWallet(getWalletKey(instance));
            const account: IAccount = instanceWallet.accounts.get("small");
            account.set("state", 3, "aaa", true);
            await trx.commit();

            const changes: PropertyChange[] = [
                {
                    property: "state",
                    operation: { operation: Operation.DEC, min: 0 },
                    pool: "small",
                    amount: 3
                },
                {
                    property: "state",
                    operation: { operation: Operation.DEC, min: 0 },
                    pool: "medium",
                    amount: 1
                }
            ];
            const result = await jp.applyPropertyChanges(changes);
            expect(result).deep.equal(["small.state"]);
        });
    });
});
