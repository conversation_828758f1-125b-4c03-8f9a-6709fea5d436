import { expect, use } from "chai";
import { Sinon<PERSON>py, SinonStub, spy, stub } from "sinon";
import { flush, flushDb, getLastTransaction, JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT, } from "../helpers";
import * as request from "request";
import { AuthRequest, Contribution, JackpotGameWinResult } from "@skywind-group/sw-jpn-core";
import wallet from "../../skywind/services/wallet.service";
import JackpotInstanceService from "../../skywind/services/jackpot.instance";
import JackpotRegionService from "../../skywind/services/regionService";
import { JackpotInternalInstance, JackpotRegion } from "../../skywind/api/model";
import { createRemoteServices, RemoteJackpotModule } from "../../skywind/modules/remoteJackpotModule";
import { JackpotGameFlow } from "../../skywind/services/jackpotGameFlow";
import { JackpotPlayerWin, JackpotWin, JackpotWinType } from "../../definition";
import {
    ContributionPayout,
    convertContributionsToContributionsPayout,
    WalletWinPayout
} from "../../skywind/modules/walletParams";
import * as _ from "lodash";
import { LocalJackpotModule } from "../../skywind/modules/localJackpotModule";
import { JackpotWallet } from "../../skywind/modules/jackpotWallet";

use(require("chai-as-promised"));

describe("Remote Jackpot Module", () => {

    const region: JackpotRegion = {
        code: "eu",
        url: "http://jpn.eu",
        secureOptions: { ca: "rootCA" }
    };
    const jpInstance: JackpotInternalInstance = {
        ...JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT,
        regionCode: region.code,
        region
    };
    const player: AuthRequest = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        jackpotIds: [jpInstance.id],
        currency: "CNY",
        gameCode: "test"
    };
    const exchangeRate = 0.1368;

    let walletFindCommitted: SinonStub;
    let walletSaveExternal: SinonSpy;
    let lookup: SinonStub;
    let remoteGet: SinonStub;
    let remotePost: SinonStub;

    const contributions: Contribution[] = [{
        pool: "small",
        seed: 0.01,
        progressive: 0.02
    }, {
        pool: "medium",
        seed: 0.1,
        progressive: 0.2
    }];
    const contributionsPayout: ContributionPayout[] = convertContributionsToContributionsPayout(contributions);
    const gameResult: JackpotGameWinResult = { type: "win", pool: "small" };
    const wins: JackpotWin[] = [{
        type: JackpotWinType.PLAYER,
        pool: "small",
        amount: 10.02,
        seed: 10,
        progressive: 0.02,
        playerAmount: 73.1,
        exchangeRate
    } as JackpotPlayerWin];
    const winPayouts: WalletWinPayout[] = [{
        pool: "small",
        winAmount: 73.1,
        currencyRate: exchangeRate,
        initialSeed: 10,
        seed: 10,
        progressive: 0.02,
        totalSeed: 0.01,
        totalProgressive: 0.02,
        seedSinceLastWin: 0.01,
        progressiveSinceLastWin: 0.02
    }];
    const ticker = {
        id: "JP-TEST",
        currency: "EUR",
        pools:
            {
                small: { seed: 0, progressive: 0 },
                medium: { seed: 0, progressive: 0 },
                large: { seed: 0, progressive: 0 }
            },
        seqId: 2
    };
    const remoteServices = createRemoteServices(JackpotInstanceService);

    before(async () => {
        lookup = stub(JackpotInstanceService, "findInternal");
        walletFindCommitted = stub(wallet, "findCommittedTransaction");
        walletSaveExternal = spy(wallet, "saveExternalTransaction");
        remoteGet = stub(request, "get");
        remotePost = stub(request, "post");

        await flushDb();
        await JackpotRegionService.create(region);
    });

    after(() => {
        walletFindCommitted.restore();
        walletSaveExternal.restore();
        lookup.restore();
        remoteGet.restore();
        remotePost.restore();
    });

    beforeEach(async () => {
        await flush();
        walletFindCommitted.resetHistory();
        walletSaveExternal.resetHistory();
        lookup.resetBehavior();
        lookup.returns(jpInstance);
        remoteGet.resetHistory();
        remotePost.resetHistory();
    });

    it("gets ticker", async () => {
        remoteGet.yields(null, { statusCode: 200 }, ticker);

        const module = new RemoteJackpotModule(player, jpInstance, exchangeRate, wallet, remoteServices);

        const response = await module.getTicker();
        expect(response).to.deep.equal(ticker);
    });

    it("process game flow - no contribution no win", async () => {
        const gameFlow: JackpotGameFlow = {
            hasContributions: () => false,
            hasWonJackpot: () => false
        } as JackpotGameFlow;

        const module = new RemoteJackpotModule(player, jpInstance, exchangeRate, wallet, remoteServices);

        const result = await module.processGameFlow(gameFlow);

        expect(result).to.deep.equal({});

        expect(walletSaveExternal.notCalled).to.be.true;
    });

    it("process game flow - no win", async () => {
        const trxId = await wallet.generateTransactionId();
        const remoteTrxId = await wallet.generateTransactionId();

        const gameFlow: JackpotGameFlow = {
            transactionId: trxId,
            request: {
                transactionId: trxId,
                externalId: "123",
                roundId: "1",
                amount: 10
            },
            hasContributions: () => true,
            hasWonJackpot: () => false,
            getContributions: () => contributions,
            getPlayerContributions: () => contributions,
            getGameResult: () => undefined,
            calculateWins: () => undefined,
            refreshTicker: () => undefined,
            hasWonJackpotFromMiniGame: () => undefined,
            getTicker: () => ticker,
            getPlayerBetAmount: () => gameFlow.request.amount
        } as any;

        remoteGet.onFirstCall().yields(null, { statusCode: 200 }, { transactionIds: [remoteTrxId] });

        const module = new RemoteJackpotModule(player, jpInstance, exchangeRate, wallet, remoteServices);

        const result = await module.processGameFlow(gameFlow);

        expect(result).to.deep.equal({
            contributions: contributionsPayout,
            playerContributions: contributions,
            gameResult: undefined,
            wins: undefined
        });

        expect(walletSaveExternal.calledOnce).to.be.true;
        expect(walletSaveExternal.args[0][0]).to.deep.equal({
            "trxId": trxId,
            "operation": {
                "externalTrxId": "123",
                "gameId": "1",
                "operationId": 1,
                "operationName": "remote-contribute",
                "params": {
                    "remoteTrxId": remoteTrxId,
                    "remoteTrxRegion": "eu",
                    "brandId": player.brandId,
                    "contributionAmount": 10,
                    "contributions": contributionsPayout,
                    "playerContributions": contributions,
                    "currencyRate": 0.1368,
                    "gameCode": "test",
                    "gameData": {
                        "amount": 10,
                        "roundId": "1",
                        "transactionId": trxId,
                        "externalId": "123"
                    },
                    "initialSeed": {
                        "large": 1000,
                        "medium": 100,
                        "small": 10
                    },
                    "gameId": "test",
                    "gameResult": undefined,
                    "playerCode": player.playerCode,
                    "playerCurrency": player.currency,
                    "region": "eu",
                    "roundId": "1",
                    "winResults": undefined,
                }
            },
            "changes": [
                {
                    "account": "$$metaInf",
                    "amount": 1,
                    "property": "remoteCall",
                    "trxType": "remote",
                    "walletKey": "jackpot:JP-TEST:EUR"
                }
            ]
        });
    });

    it("process game flow - contribute and win", async () => {
        const trxId = await wallet.generateTransactionId();
        const remoteTrxId = await wallet.generateTransactionId();

        const gameFlow: JackpotGameFlow = {
            transactionId: trxId,
            request: {
                transactionId: trxId,
                externalId: "123",
                roundId: "1",
                amount: 10
            },
            hasContributions: () => true,
            hasWonJackpot: () => true,
            getContributions: () => contributions,
            getPlayerContributions: () => contributions,
            getGameResult: () => gameResult,
            calculateWins: () => wins,
            refreshTicker: () => undefined,
            getTicker: () => ticker,
            hasWonJackpotFromMiniGame: () => undefined,
            getPlayerBetAmount: () => gameFlow.request.amount
        } as any;

        remoteGet.onFirstCall().yields(null, { statusCode: 200 }, { transactionIds: [remoteTrxId] });

        remotePost.onFirstCall().yields(null, { statusCode: 200 },
            { results: [{ contributions, playerContributions: contributions, gameResult, wins, winPayouts, ticker }] });

        const module = new RemoteJackpotModule(player, jpInstance, exchangeRate, wallet, remoteServices);

        const result = await module.processGameFlow(gameFlow);

        expect(result).to.deep.equal({
            contributions,
            playerContributions: contributions,
            gameResult,
            wins,
            winPayouts
        });

        expect(remotePost.calledOnce).to.be.true;
        expect(_.omit(remotePost.args[0][0], "headers", "agent")).to.deep.equal({
            "baseUrl": "http://jpn.eu",
            "ca": "rootCA",
            "json": {
                "playerInfo": player,
                "requestRegion": "default",
                "request": {
                    "amount": 10,
                    "roundId": "1",
                    "transactionId": trxId,
                    "externalId": "123"
                },
                "results": [
                    {
                        "contributions": contributionsPayout,
                        "playerContributions": contributions,
                        "gameResult": gameResult,
                        "jackpotId": "JP-TEST",
                        "wins": wins
                    }
                ],
                "transactionId": remoteTrxId
            },
            "qs": {},
            "url": "/api/v2/jpn/remote/gameFlow"
        });

        expect(walletSaveExternal.calledTwice).to.be.true;
        expect(walletSaveExternal.args[0][0]).to.deep.equal({
            "trxId": trxId,
            "operation": {
                "externalTrxId": "123",
                "gameId": "1",
                "operationId": 1,
                "operationName": "remote-contribute",
                "params": {
                    "remoteTrxId": remoteTrxId,
                    "remoteTrxRegion": "eu",
                    "brandId": player.brandId,
                    "contributionAmount": 10,
                    "contributions": contributionsPayout,
                    "playerContributions": contributions,
                    "currencyRate": 0.1368,
                    "gameCode": "test",
                    "gameData": {
                        "amount": 10,
                        "roundId": "1",
                        "transactionId": trxId,
                        "externalId": "123"
                    },
                    "initialSeed": {
                        "large": 1000,
                        "medium": 100,
                        "small": 10
                    },
                    "gameId": "test",
                    "gameResult": gameResult,
                    "playerCode": player.playerCode,
                    "playerCurrency": player.currency,
                    "region": "eu",
                    "roundId": "1",
                    "winResults": wins,
                }
            },
            "changes": [
                {
                    "account": "$$metaInf",
                    "amount": 1,
                    "property": "remoteCall",
                    "trxType": "remote",
                    "walletKey": "jackpot:JP-TEST:EUR"
                }
            ]
        });
        expect(walletSaveExternal.args[1][0]).to.deep.equal({
            "trxId": trxId,
            "operation": {
                "externalTrxId": "123",
                "gameId": "1",
                "operationId": 1001,
                "operationName": "remote-release",
                "params": {
                    "remoteTrxId": remoteTrxId,
                    "remoteTrxRegion": "eu",
                    "brandId": player.brandId,
                    "gameCode": "test",
                    "gameId": "test",
                    "playerCode": player.playerCode,
                    "playerCurrency": player.currency,
                    "region": "eu",
                    "roundId": "1",
                    "winResults": wins,
                    "wins": winPayouts,
                    "betAmount": 10
                }
            },
            "changes": [
                {
                    "account": "$$metaInf",
                    "amount": 1,
                    "property": "remoteCall",
                    "trxType": "remote",
                    "walletKey": "jackpot:JP-TEST:EUR"
                }
            ]
        });
    });

    it("process game flow - win", async () => {
        const trxId = await wallet.generateTransactionId();
        const remoteTrxId = await wallet.generateTransactionId();

        const gameFlow: JackpotGameFlow = {
            transactionId: trxId,
            request: {
                transactionId: trxId,
                externalId: "123",
                roundId: "1",
                amount: 10
            },
            hasContributions: () => false,
            hasWonJackpot: () => true,
            getContributions: () => undefined,
            getPlayerContributions: () => undefined,
            getGameResult: () => gameResult,
            calculateWins: () => wins,
            refreshTicker: () => undefined,
            getTicker: () => ticker,
            hasWonJackpotFromMiniGame: () => undefined,
            getPlayerBetAmount: () => gameFlow.request.amount
        } as any;

        remoteGet.onFirstCall().yields(null, { statusCode: 200 }, { transactionIds: [remoteTrxId] });

        remotePost.onFirstCall().yields(null, { statusCode: 200 },
            { results: [{ contributions: undefined, gameResult, wins, winPayouts, ticker }] });

        const module = new RemoteJackpotModule(player, jpInstance, exchangeRate, wallet, remoteServices);

        const result = await module.processGameFlow(gameFlow);

        expect(result).to.deep.equal({
            contributions: undefined,
            playerContributions: undefined,
            gameResult,
            wins,
            winPayouts
        });

        expect(remotePost.calledOnce).to.be.true;
        expect(_.omit(remotePost.args[0][0], "headers", "agent")).to.deep.equal({
            "baseUrl": "http://jpn.eu",
            "ca": "rootCA",
            "json": {
                "playerInfo": player,
                "requestRegion": "default",
                "request": {
                    "amount": 10,
                    "roundId": "1",
                    "transactionId": trxId,
                    "externalId": "123"
                },
                "results": [
                    {
                        "contributions": undefined,
                        "playerContributions": undefined,
                        "gameResult": gameResult,
                        "jackpotId": "JP-TEST",
                        "wins": wins
                    }
                ],
                "transactionId": remoteTrxId
            },
            "qs": {},
            "url": "/api/v2/jpn/remote/gameFlow"
        });

        expect(walletSaveExternal.calledTwice).to.be.true;
        expect(walletSaveExternal.args[0][0]).to.deep.equal({
            "trxId": trxId,
            "operation": {
                "externalTrxId": "123",
                "gameId": "1",
                "operationId": 1,
                "operationName": "remote-contribute",
                "params": {
                    "remoteTrxId": remoteTrxId,
                    "remoteTrxRegion": "eu",
                    "brandId": player.brandId,
                    "contributionAmount": 10,
                    "contributions": undefined,
                    "playerContributions": undefined,
                    "currencyRate": 0.1368,
                    "gameCode": "test",
                    "gameData": {
                        "amount": 10,
                        "roundId": "1",
                        "transactionId": trxId,
                        "externalId": "123"
                    },
                    "initialSeed": {
                        "large": 1000,
                        "medium": 100,
                        "small": 10
                    },
                    "gameId": "test",
                    "gameResult": gameResult,
                    "playerCode": player.playerCode,
                    "playerCurrency": player.currency,
                    "region": "eu",
                    "roundId": "1",
                    "winResults": wins,
                }
            },
            "changes": [
                {
                    "account": "$$metaInf",
                    "amount": 1,
                    "property": "remoteCall",
                    "trxType": "remote",
                    "walletKey": "jackpot:JP-TEST:EUR"
                }
            ]
        });
        expect(walletSaveExternal.args[1][0]).to.deep.equal({
            "trxId": trxId,
            "operation": {
                "externalTrxId": "123",
                "gameId": "1",
                "operationId": 1001,
                "operationName": "remote-release",
                "params": {
                    "remoteTrxId": remoteTrxId,
                    "remoteTrxRegion": "eu",
                    "brandId": player.brandId,
                    "gameCode": "test",
                    "gameId": "test",
                    "playerCode": player.playerCode,
                    "playerCurrency": player.currency,
                    "region": "eu",
                    "roundId": "1",
                    "winResults": wins,
                    "wins": winPayouts,
                    "betAmount": 10
                }
            },
            "changes": [
                {
                    "account": "$$metaInf",
                    "amount": 1,
                    "property": "remoteCall",
                    "trxType": "remote",
                    "walletKey": "jackpot:JP-TEST:EUR"
                }
            ]
        });
    });

    it("process game flow - duplicate contribute and win", async () => {
        const trxId = await wallet.generateTransactionId();
        const remoteTrxId = await wallet.generateTransactionId();
        const nextRemoteTrxId = await wallet.generateTransactionId();

        const gameFlow: JackpotGameFlow = {
            transactionId: trxId,
            request: {
                transactionId: trxId,
                roundId: "1",
                amount: 10
            },
            hasContributions: () => true,
            hasWonJackpot: () => true,
            getContributions: () => contributions,
            getPlayerContributions: () => contributions,
            getGameResult: () => gameResult,
            calculateWins: () => wins,
            refreshTicker: () => undefined,
            getTicker: () => ticker,
            hasWonJackpotFromMiniGame: () => undefined,
            getPlayerBetAmount: () => gameFlow.request.amount
        } as any;

        remoteGet.onFirstCall().yields(null, { statusCode: 200 }, { transactionIds: [remoteTrxId, nextRemoteTrxId] });

        remotePost.onFirstCall().yields(null, { statusCode: 200 },
            { results: [{ contributions, playerContributions: contributions, gameResult, wins, winPayouts, ticker }] });

        const module = new RemoteJackpotModule(player, jpInstance, exchangeRate, wallet, remoteServices);

        // original request

        const result1 = await module.processGameFlow(gameFlow);
        expect(result1).to.deep.equal({
            contributions,
            playerContributions: contributions,
            gameResult,
            wins,
            winPayouts
        });

        // duplicate request

        remotePost.onSecondCall().yields(null, { statusCode: 200 },
            { results: [{ contributions, playerContributions: contributions, gameResult, wins, winPayouts, ticker }] });

        const releaseTrx = await getLastTransaction();
        const contributeTrx = await getLastTransaction();
        walletFindCommitted.onFirstCall().returns(contributeTrx);
        walletFindCommitted.onSecondCall().returns(releaseTrx);

        const duplicateContributions = [{
            pool: "small",
            seed: 0.02,
            progressive: 0.04
        }, {
            pool: "medium",
            seed: 0.2,
            progressive: 0.4
        }];
        const duplicateGameResult = { type: "win", pool: "large" };
        const duplicateWins = [{
            type: JackpotWinType.PLAYER,
            pool: "large",
            amount: 20,
            seed: 10,
            progressive: 10,
            playerAmount: 73.1,
            exchangeRate
        } as JackpotPlayerWin];
        gameFlow.getContributions = () => duplicateContributions;
        gameFlow.getPlayerContributions = () => duplicateContributions;
        gameFlow.getGameResult = () => duplicateGameResult;
        gameFlow.calculateWins = async () => duplicateWins;

        const result2 = await module.processGameFlow(gameFlow);
        expect(result2).to.deep.equal({
            contributions,
            playerContributions: contributions,
            gameResult,
            wins,
            winPayouts
        });

        // expectations

        const expectedPostData = {
            "baseUrl": "http://jpn.eu",
            "ca": "rootCA",
            "json": {
                "playerInfo": player,
                "requestRegion": "default",
                "request": {
                    "amount": 10,
                    "roundId": "1",
                    "transactionId": trxId,
                },
                "results": [
                    {
                        "contributions": contributionsPayout,
                        "playerContributions": contributions,
                        "gameResult": gameResult,
                        "jackpotId": "JP-TEST",
                        "wins": wins
                    }
                ],
                "transactionId": remoteTrxId
            },
            "qs": {},
            "url": "/api/v2/jpn/remote/gameFlow"
        };
        expect(remotePost.calledTwice).to.be.true;
        expect(_.omit(remotePost.args[0][0], "headers", "agent")).to.deep.equal(expectedPostData);
        expect(_.omit(remotePost.args[1][0], "headers", "agent")).to.deep.equal(expectedPostData);

        expect(walletSaveExternal.callCount).to.equal(4);
        expect(walletSaveExternal.args[0][0]).to.deep.equal({
            "trxId": trxId,
            "operation": {
                "externalTrxId": undefined,
                "gameId": "1",
                "operationId": 1,
                "operationName": "remote-contribute",
                "params": {
                    "remoteTrxId": remoteTrxId,
                    "remoteTrxRegion": "eu",
                    "brandId": player.brandId,
                    "contributionAmount": 10,
                    "contributions": contributionsPayout,
                    "playerContributions": contributions,
                    "currencyRate": 0.1368,
                    "gameCode": "test",
                    "gameData": {
                        "amount": 10,
                        "roundId": "1",
                        "transactionId": trxId
                    },
                    "initialSeed": {
                        "large": 1000,
                        "medium": 100,
                        "small": 10
                    },
                    "gameId": "test",
                    "gameResult": gameResult,
                    "playerCode": player.playerCode,
                    "playerCurrency": player.currency,
                    "region": "eu",
                    "roundId": "1",
                    "winResults": wins,
                }
            },
            "changes": [
                {
                    "account": "$$metaInf",
                    "amount": 1,
                    "property": "remoteCall",
                    "trxType": "remote",
                    "walletKey": "jackpot:JP-TEST:EUR"
                }
            ]
        });

        const expectedDuplicateContributions = convertContributionsToContributionsPayout(duplicateContributions);
        expect(walletSaveExternal.args[2][0]).to.deep.equal({
            "trxId": trxId,
            "operation": {
                "externalTrxId": undefined,
                "gameId": "1",
                "operationId": 1,
                "operationName": "remote-contribute",
                "params": {
                    "remoteTrxId": nextRemoteTrxId,
                    "remoteTrxRegion": "eu",
                    "brandId": player.brandId,
                    "contributionAmount": 10,
                    "contributions": expectedDuplicateContributions,
                    "playerContributions": duplicateContributions,
                    "currencyRate": 0.1368,
                    "gameCode": "test",
                    "gameData": {
                        "amount": 10,
                        "roundId": "1",
                        "transactionId": trxId
                    },
                    "initialSeed": {
                        "large": 1000,
                        "medium": 100,
                        "small": 10
                    },
                    "gameId": "test",
                    "gameResult": duplicateGameResult,
                    "playerCode": player.playerCode,
                    "playerCurrency": player.currency,
                    "region": "eu",
                    "roundId": "1",
                    "winResults": duplicateWins,
                }
            },
            "changes": [
                {
                    "account": "$$metaInf",
                    "amount": 1,
                    "property": "remoteCall",
                    "trxType": "remote",
                    "walletKey": "jackpot:JP-TEST:EUR"
                }
            ]
        });
        const expectedWalletWinRelease = {
            "trxId": trxId,
            "operation": {
                "externalTrxId": undefined,
                "gameId": "1",
                "operationId": 1001,
                "operationName": "remote-release",
                "params": {
                    "remoteTrxId": remoteTrxId,
                    "remoteTrxRegion": "eu",
                    "brandId": player.brandId,
                    "gameCode": "test",
                    "gameId": "test",
                    "playerCode": player.playerCode,
                    "playerCurrency": player.currency,
                    "region": "eu",
                    "roundId": "1",
                    "winResults": wins,
                    "wins": winPayouts,
                    "betAmount": 10
                }
            },
            "changes": [
                {
                    "account": "$$metaInf",
                    "amount": 1,
                    "property": "remoteCall",
                    "trxType": "remote",
                    "walletKey": "jackpot:JP-TEST:EUR"
                }
            ]
        };
        expect(walletSaveExternal.args[1][0]).to.deep.equal(expectedWalletWinRelease);
        expect(walletSaveExternal.args[3][0]).to.deep.equal(expectedWalletWinRelease);
    });

    it("process game flow - migrated transaction", async () => {
        const remoteTrxId = await wallet.generateTransactionId();
        remoteGet.onFirstCall().yields(null, { statusCode: 200 }, { transactionIds: [remoteTrxId] });

        const trxId = await wallet.generateTransactionId();

        const gameFlow: JackpotGameFlow = {
            transactionId: trxId,
            request: {
                transactionId: trxId,
                roundId: "1",
                amount: 10
            },
            hasContributions: () => true,
            hasWonJackpot: () => true,
            getContributions: () => contributions,
            getPlayerContributions: () => contributions,
            getGameResult: () => gameResult,
            calculateWins: () => wins,
            getWins: () => undefined,
            refreshTicker: () => undefined,
            validateCheckWin: () => undefined,
            getTicker: () => ticker,
            hasWonJackpotFromMiniGame: () => undefined,
            getPoolManipulations: () => undefined,
            getPlayerBetAmount: () => gameFlow.request.amount
        } as any;

        // original

        const localWallet = new JackpotWallet({
            ...player,
            region: "eu",
            playerCurrency: "CNY",
            gameId: "test",
            roundId: "1"
        }, jpInstance, exchangeRate, wallet);
        const localModule = new LocalJackpotModule(localWallet);
        await localModule.processGameFlow(gameFlow);

        // migrated

        const releaseTrx = await getLastTransaction();
        const contributeTrx = await getLastTransaction();
        walletFindCommitted.onFirstCall().returns(contributeTrx);
        walletFindCommitted.onSecondCall().returns(releaseTrx);

        const migratedInstance: JackpotInternalInstance = {
            ...jpInstance,
            migratedAt: new Date()
        };
        const remoteModule = new RemoteJackpotModule(player, migratedInstance, exchangeRate, wallet, remoteServices);

        const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
        const result = await remoteModule.processGameFlow(gameFlow);
        expect(result).to.deep.equal({
            contributions: expectedContributionsPayment,
            playerContributions: contributions,
            gameResult,
            wins,
            winPayouts: [
                {
                    ...winPayouts[0],
                    title: "small",
                    info: null,
                }
            ]
        });

        expect(remotePost.notCalled).to.be.true;
    });

    it("process game flow - partially committed migrated transaction", async () => {
        const remoteTrxId = await wallet.generateTransactionId();
        const nextRemoteTrxId = await wallet.generateTransactionId();
        remoteGet.onFirstCall().yields(null, { statusCode: 200 }, { transactionIds: [remoteTrxId, nextRemoteTrxId] });

        const trxId = await wallet.generateTransactionId();

        const gameFlow: JackpotGameFlow = {
            transactionId: trxId,
            request: {
                transactionId: trxId,
                roundId: "1",
                amount: 10
            },
            hasContributions: () => true,
            hasWonJackpot: () => true,
            getContributions: () => contributions,
            getPlayerContributions: () => contributions,
            getGameResult: () => gameResult,
            calculateWins: () => wins,
            getWins: () => undefined,
            refreshTicker: () => undefined,
            getTicker: () => ticker,
            hasWonJackpotFromMiniGame: () => undefined,
            getPlayerBetAmount: () => gameFlow.request.amount
        } as any;

        // original

        const localWallet = new JackpotWallet({
            ...player,
            region: "eu",
            playerCurrency: "CNY",
            gameId: "test",
            roundId: "1"
        }, jpInstance, exchangeRate, wallet);
        await localWallet.contribute(trxId, gameFlow.request as any,
            gameFlow.getContributions(), gameFlow.getPlayerContributions(), gameFlow.getGameResult());

        // migrated

        remotePost.onFirstCall().yields(null, { statusCode: 200 },
            { results: [{ gameResult, wins, winPayouts, ticker }] });

        const contributeTrx = await getLastTransaction();
        walletFindCommitted.onFirstCall().returns(contributeTrx);
        walletFindCommitted.onSecondCall().returns(undefined);

        const migratedInstance: JackpotInternalInstance = {
            ...jpInstance,
            migratedAt: new Date()
        };
        const remoteModule = new RemoteJackpotModule(player, migratedInstance, exchangeRate, wallet, remoteServices);

        const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
        const result = await remoteModule.processGameFlow(gameFlow);
        expect(result).to.deep.equal({
            contributions: expectedContributionsPayment,
            playerContributions: contributions,
            gameResult,
            wins,
            winPayouts
        });

        const expectedPostData = {
            "baseUrl": "http://jpn.eu",
            "ca": "rootCA",
            "json": {
                "playerInfo": player,
                "requestRegion": "default",
                "request": {
                    "amount": 10,
                    "roundId": "1",
                    "transactionId": trxId,
                },
                "results": [
                    {
                        "gameResult": gameResult,
                        "jackpotId": "JP-TEST",
                        "wins": wins
                    }
                ],
                "transactionId": nextRemoteTrxId
            },
            "qs": {},
            "url": "/api/v2/jpn/remote/gameFlow"
        };
        expect(remotePost.calledOnce).to.be.true;
        expect(_.omit(remotePost.args[0][0], "headers", "agent")).to.deep.equal(expectedPostData);
    });

    it("process game flow - not finished migrated win", async () => {
        const remoteTrxId = await wallet.generateTransactionId();
        remoteGet.onFirstCall().yields(null, { statusCode: 200 }, { transactionIds: [remoteTrxId] });

        const oldTrxId = await wallet.generateTransactionId();

        const gameFlow: JackpotGameFlow = {
            transactionId: oldTrxId,
            request: {
                transactionId: oldTrxId,
                roundId: "1",
                amount: 10
            },
            hasContributions: () => false,
            hasWonJackpot: () => true,
            getContributions: () => undefined,
            getPlayerContributions: () => undefined,
            getGameResult: () => gameResult,
            calculateWins: () => wins,
            getWins: () => undefined,
            refreshTicker: () => undefined,
            getTicker: () => ticker,
            hasWonJackpotFromMiniGame: () => undefined,
            getPlayerBetAmount: () => gameFlow.request.amount
        } as any;

        walletFindCommitted.onFirstCall().returns(undefined);

        remotePost.onFirstCall().yields(null, { statusCode: 200 },
            { results: [{ gameResult, wins, winPayouts, ticker }] });

        const migratedInstance: JackpotInternalInstance = {
            ...jpInstance,
            migratedAt: new Date()
        };
        const remoteModule = new RemoteJackpotModule(player, migratedInstance, exchangeRate, wallet, remoteServices);

        const result = await remoteModule.processGameFlow(gameFlow);
        expect(result).to.deep.equal({
            contributions: undefined,
            playerContributions: undefined,
            gameResult,
            wins,
            winPayouts
        });

        expect(remotePost.calledOnce).to.be.true;
    });
});
