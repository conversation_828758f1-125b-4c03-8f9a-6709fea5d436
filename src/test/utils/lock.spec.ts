import { expect, use } from "chai";
import { flush } from "../helpers";
import { Lock } from "../../skywind/utils/lock";

use(require("chai-as-promised"));

function doSomething(ms) {
    return new Promise((resolve, reject) => {
       setTimeout(resolve, ms);
    });
}

describe("Lock", () => {

    beforeEach(async() => {
       await flush();
    });

    it("locks key", async() => {
        const key = "test-lock-key";
        const lock1 = new Lock(key);
        const lock2 = new Lock(key);

        await lock1.lock(1000);
        let lockedSecondTime = false;
        const locked2 = lock2.lock(1000).then(() => {
            lockedSecondTime = true;
        });
        await doSomething(200);
        expect(lockedSecondTime).to.be.false;
        await lock1.unlock();
        await locked2;
        expect(lockedSecondTime).to.be.true;
        await lock2.unlock();
    });

    it("avoids dead lock", async() => {
        const key = "test-lock-key";
        const lock1 = new Lock(key);
        const lock2 = new Lock(key);

        await lock1.lock(1);
        await lock2.lock(1);
        await expect(lock1.unlock()).to.be.rejectedWith(Error);
        await lock2.unlock();
    });

    it("fails to unlock not acquired lock", async() => {
        const lock = new Lock("test-lock-key");
        await expect(lock.unlock()).to.be.rejectedWith(Error);
    });

    it("fails to unlock stolen lock", async() => {
        const key = "test-lock-key";
        const lock1 = new Lock(key);
        const lock2 = new Lock(key);

        await lock1.lock(1000);
        await expect(lock2.unlock()).to.be.rejectedWith(Error);
    });
});
