import { expect } from "chai";
import { JackpotId } from "../../skywind/utils/jackpotId";

describe("JackpotId", () => {
    it("Filters and groups external jackpots", async() => {
        const jackpotIds = ["PP;jp1;sc1_wg1_EUR", "PP;jp2;sc2_wg2_EUR"];
        const jackpotMap = JackpotId.filterAndGroupExternal(jackpotIds);
        const result = new Map<string, string[]>([["PP", jackpotIds]]);
        expect(Array.from(jackpotMap.entries())).to.deep.equal(Array.from(result.entries()));
    });

    it("Filters internal jackpots", async() => {
        const jackpots = JackpotId.filterInternal(["1234", "1235", "PP;jp1;sc1_wg1_EUR"]);
        expect(jackpots).to.deep.equal(["1234", "1235"]);
    });
});
