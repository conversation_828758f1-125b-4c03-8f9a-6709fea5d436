import { expect, use } from "chai";
import { SinonSpy, spy, stub } from "sinon";
import { TrxIdGenerator, TrxIdPool } from "../../skywind/utils/trxIdPool";
import config from "../../skywind/config";
import { delay } from "../helpers";

use(require("chai-as-promised"));

describe("Trx Id Pool", () => {

    const originalConfig = config.trxIdPool;

    before(() => {
        config.trxIdPool = {
            minLength: 3,
            maxLength: 20,
            loadFactor: 2,
            refreshTimeout: 100
        };
    });

    after(() => {
        config.trxIdPool = originalConfig;
    });

    let generatorId = 0;
    const generator: TrxIdGenerator = (count: number) => {
        return new Promise<string[]>((resolve) => {
            setTimeout(() => {
                resolve([...new Array(count)].map(() => (++ generatorId).toString()));
            }, 1);
        });
    };

    beforeEach(() => {
        generatorId = 0;
    });

    it("generates ids", async () => {
        const generatorSpy: SinonSpy = spy(generator);

        const pool = new TrxIdPool(generatorSpy);

        // pool=0, minPoolLength=3
        expect(await pool.getTransactionId()).to.equal("1");
        expect(generatorSpy.calledOnce).to.be.true;
        expect(generatorSpy.args[0][0]).to.eq(6);
        generatorSpy.resetHistory();

        // pool=5, minPoolLength=3
        expect(await pool.getTransactionId()).to.equal("2");
        expect(generatorSpy.called).to.be.false;
        generatorSpy.resetHistory();

        // pool=4, minPoolLength=3
        expect(await pool.getTransactionId()).to.equal("3");
        expect(generatorSpy.calledOnce).to.be.true;
        expect(generatorSpy.args[0][0]).to.eq(6);
        await Promise.all(generatorSpy.returnValues);
        generatorSpy.resetHistory();

        // pool=9, minPoolLength=3
        let results = await Promise.all([...new Array(6)].map(() => pool.getTransactionId()));
        expect(results.filter((v) => !v).length).to.eq(0);
        expect(generatorSpy.calledOnce).to.be.true;
        expect(generatorSpy.args[0][0]).to.eq(6);
        await Promise.all(generatorSpy.returnValues);
        generatorSpy.resetHistory();

        // pool=9, minPoolLength=3
        results = await Promise.all([...new Array(9)].map(() => pool.getTransactionId()));
        expect(results.filter((v) => !v).length).to.eq(0);
        expect(generatorSpy.calledOnce).to.be.true;
        expect(generatorSpy.args[0][0]).to.eq(6);
        await Promise.all(generatorSpy.returnValues);
        generatorSpy.resetHistory();

        // pool=6, minPoolLength=6
        results = await Promise.all([...new Array(12)].map(() => pool.getTransactionId()));
        expect(results.filter((v) => !v).length).to.eq(0);
        expect(generatorSpy.calledTwice).to.be.true;
        expect(generatorSpy.args[0][0]).to.eq(12);
        expect(generatorSpy.args[1][0]).to.eq(20);
        await Promise.all(generatorSpy.returnValues);
        generatorSpy.resetHistory();

        // pool=26, minPoolLength=10
        results = await Promise.all([...new Array(18)].map(() => pool.getTransactionId()));
        expect(results.filter((v) => !v).length).to.eq(0);
        expect(generatorSpy.calledOnce).to.be.true;
        expect(generatorSpy.args[0][0]).to.eq(20);
        await Promise.all(generatorSpy.returnValues);
        generatorSpy.resetHistory();

        // request too many
        results = await Promise.all([...new Array(100)].map(() => pool.getTransactionId()));
        expect(results.filter((v) => !v).length).to.eq(0);
        await Promise.all(generatorSpy.returnValues);
        generatorSpy.resetHistory();
    });

    it("throws generator error", async () => {
        let values = await generator(6);

        const generatorStub = stub();
        generatorStub.onFirstCall().returns(values);
        generatorStub.returns(Promise.reject(new Error("test")));

        const pool = new TrxIdPool(generatorStub);

        for (const v of values) {
            expect(await pool.getTransactionId()).to.equal(v);
        }

        await expect(pool.getTransactionId()).to.be.rejectedWith(Error, "test");
        await expect(pool.getTransactionId()).to.be.rejectedWith(Error, "test");
        await expect(pool.getTransactionId()).to.be.rejectedWith(Error, "test");

        values = await generator(6);
        generatorStub.returns(values);

        for (const v of values) {
            expect(await pool.getTransactionId()).to.equal(v);
        }
    });

    it("refresh stale transaction ids", async () => {
        const generatorStub = stub();
        generatorStub.onFirstCall().returns(await generator(6));
        generatorStub.onSecondCall().returns(await generator(6));

        const pool = new TrxIdPool(generatorStub);

        expect(await pool.getTransactionId()).to.equal("1");

        await delay(100);

        expect(await pool.getTransactionId()).to.equal("7");
    });
});
