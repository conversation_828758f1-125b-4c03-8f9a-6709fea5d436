import { expect } from "chai";
import { round } from "../../skywind/utils/rounding";

describe("Rounding", () => {

    it("Rounding with defined behaviour", () => {
        const val1 = round(123.456, 2, () => 0.1);
        expect(val1).eq(123.46);

        const val2 = round(123.456, 2, () => 0.9);
        expect(val2).eq(123.45);
    });

    it("Round to integer", async () => {
        const a = round(0.5, 0, () => 0);
        const b = round(0.5, 0, () => 0.9999999999);

        expect(a).eq(1);
        expect(b).eq(0);
    });

    it("Compare sum with defined behaviour", () => {
        let sumA = 0;
        let sumB = 0;

        for (let i = 0; i < 10; i++) {
            const a = .5;
            const rnd = (i % 2); // Generates [0,1,0,1,...]
            const b = round(a, 0, () => rnd);

            sumA += a;
            sumB += b;
        }

        expect(sumA).eq(5);
        expect(sumA).eq(sumB);

    });

    it.skip("Compare sum on default rng", () => {

        const iterations = 10000000;

        let sumA = 0;
        let sumB = 0;

        for (let i = 0; i < iterations; i++) {
            const a = Math.random() * 1000; // [0, 999]
            const b = round(a, 1);

            sumA += a;
            sumB += b;
        }

        const diff = Math.abs(sumA - sumB);
        const deviation = 1;
        expect(diff < deviation).eq(true, `Original sum and rounded sum more then ${deviation}`);

    });

    it.skip("Stochastic rounding at least should be better than Number.toFixed", () => {

        const iterations = 10000000;

        let sumA = 0; // Original
        let sumB = 0; // Stochastic rounding
        let sumC = 0; // Number.toFixed() rounding

        for (let i = 0; i < iterations; i++) {
            const a = Math.random() * 1000; // [0, 999]
            const b = round(a, 1);
            const c = +a.toFixed(1);

            sumA += a;
            sumB += b;
            sumC += c;
        }

        const diffAB = Math.abs(sumA - sumB);
        const diffAC = Math.abs(sumA - sumC);

        expect(diffAB < diffAC).eq(true);
    });

});
