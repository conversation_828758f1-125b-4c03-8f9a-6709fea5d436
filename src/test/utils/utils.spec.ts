import { expect } from "chai";
import { getFilesList, safeExchange } from "../../skywind/utils/utils";
import * as path from "path";

describe("Utils", () => {

    it("get file list from dir async", async() => {
        const dirPath = path.resolve(__dirname, "");
        const list = await getFilesList(dirPath);

        expect(list).not.empty;

        const found = list.find((value) => {
            return value.indexOf("utils.spec.") !== -1;
        });

        expect(found).not.undefined;
    });

    it("safe exchange small values", async() => {
        expect(safeExchange(1, 1 / 0.00004)).to.equal(25000);
        expect(safeExchange(9, 0.001)).to.equal(0.009);
        expect(safeExchange(0.03, 184.76843)).to.equal(5.5430529);
        expect(safeExchange(100, 1 / 0.00004)).to.equal(2500000);
        expect(safeExchange(300, 1 / 0.00004)).to.equal(7500000);
        expect(safeExchange(400, 1 / 0.00004)).to.equal(10000000);
        expect(safeExchange(500, 1 / 0.00004)).to.equal(12500000);
        expect(safeExchange(1000, 1 / 0.00004)).to.equal(25000000);
        expect(safeExchange(111991.001, 9)).to.equal(1007919.009);
    });

    it("safe exchange too high values - no rounding", async() => {
        expect(safeExchange(90071925474099.1, 13.3331)).to.equal(90071925474099.1 * 13.3331);
        expect(safeExchange(9005571925474097.1, 13.01)).to.equal(9005571925474097.1 * 13.01);
        expect(safeExchange(9007191125474099.1, 113.3331)).to.equal(9007191125474099.1 * 113.3331);
    });

    it("rounded result are similar to maxSafeInt - no rounding", async() => {
        expect(safeExchange(900719.9254740991111111, 100)).to.equal(Number.MAX_SAFE_INTEGER / 10 ** 8);
    });

    it("rounding with lower precision for big numbers", async() => {
        expect(safeExchange(10000, 1 / 0.00004)).to.equal(250000000);
        expect(safeExchange(1000000, 1 / 0.00004)).to.equal(25000000000);

        // !!! Method limitations - we cannot create safe multiplication for all arguments!
        expect(safeExchange(80071991.01, 6)).to.equal(480431946.0600001);
        expect(safeExchange(800701991.001, 9)).to.equal(7206317919.009001);
    });
});
