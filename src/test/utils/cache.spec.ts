import { expect } from "chai";
import { stub, SinonStub } from "sinon";
import { delay } from "../helpers";
import { Cache } from "../../skywind/utils/cache";
import { randomUUID } from "node:crypto";

describe("Cache", () => {

    let search: SinonStub;
    let cacheName;
    let cache;

    beforeEach(async () => {
        search = stub();
        cacheName = randomUUID();
        cache = new Cache<string, any>(cacheName, search);
        // await until pub/sub connections are ready
        await delay(100);
    });

    it("caches values", async() => {
        const testId = "test";
        const testValue = { test: cacheName };
        search.returns(testValue);

        const found = await cache.find(testId);
        expect(found).to.deep.equal(testValue);

        const foundCached = await cache.find(testId);
        expect(foundCached).to.deep.equal(testValue);

        expect(search.calledOnce).to.be.true;
    });

    it("returns undefined when not found", async() => {
        search.returns(undefined);

        const notFound = await cache.find("test");
        expect(notFound).to.be.undefined;

        expect(search.calledOnce).to.be.true;
    });

    it("invalidates cache values", async() => {
        const testId = "test";
        const testValue = { test: cacheName };
        search.returns(testValue);

        const found = await cache.find(testId);
        expect(found).to.deep.equal(testValue);

        cache.invalidate(testId);
        await delay(100);

        const foundInvalidated = await cache.find(testId);
        expect(foundInvalidated).to.deep.equal(testValue);

        expect(search.calledTwice).to.be.true;
    });
});
