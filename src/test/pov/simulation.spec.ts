import { expect } from "chai";
import {
    flush, JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT, createTestJPGame
} from "../helpers";
import { JackpotWallet } from "../../skywind/modules/jackpotWallet";
import inMemoryWallet from "../../pov/wallet/wallet.inmemory";
import redisWallet from "../../pov/wallet/wallet.redis";
import { history } from "../../pov/wallet/wallet.history";
import { IWallet } from "@skywind-group/sw-wallet";
import { createPlayerContributions } from "../../skywind/services/jackpotGameFlow";
import { BaseWalletParams, convertContributionsToContributionsPayout } from "../../skywind/modules/walletParams";
import { createJPNServer } from "../../";
import { JackpotPlayerWin, JackpotWinType } from "../../definition";

async function simulate(wallet, checkFullLog?: boolean, ignoreZeroValuesInWallet?: boolean) {

    const jpInstance = Object.assign({}, JP_WITH_DYNAMIC_CONTRIBUTION_WITHOUT_DEPOSIT);
    jpInstance.id = Math.round(Math.random() * 1000).toString();
    const player: BaseWalletParams = {
        playerCode: "PL-CODE",
        brandId: 1,
        region: "eu",
        playerCurrency: "CNY",
        gameCode: "test",
        gameId: "test",
        roundId: "1"
    };

    const module: JackpotWallet = new JackpotWallet(player, jpInstance, 1, wallet);

    const game = await createTestJPGame(module);

    // contribute
    let result;
    for (let pos = 0; pos < 11000; pos++) {
        const data = {
            externalId: `${pos + 1}`,
            transactionId: `${pos + 1}`,
            amount: 1,
            exchangeRate: 1,
            roundId: "1"
        };

        const ticker = await module.getTicker(true);
        const contributions = game.getContributions(data.amount, data, ticker.pools);
        for (const c of contributions) {
            c.properties = { customProp: 1.15 };
        }
        const playerContributions = createPlayerContributions(contributions);
        result = await module.contribute(data.transactionId, data, contributions, playerContributions);
        const expectedTicker = await module.getTicker();
        const expectedContributionsPayment = convertContributionsToContributionsPayout(contributions);
        expectedContributionsPayment.forEach(item => {
            item.totalProgressive = expectedTicker.pools[item.pool].progressive || 0;
            item.totalSeed = expectedTicker.pools[item.pool].seed || 0;
        });
        expect(result).deep.equals(
            { contributions: expectedContributionsPayment, gameResult: undefined, playerContributions }
            );
    }

    // verify wallet
    let jpWallet: IWallet = await wallet.get(`jackpot:${jpInstance.id}:${jpInstance.definition.currency}`);
    const expectedAccounts = {
        "large": {
            "progressive": ***********,
            "seed": ***********,
            "progressiveSinceLastWin": ***********,
            "seedSinceLastWin": ***********,
            "customProp": 1.15
        },
        "medium": {
            "progressive": ***********,
            "seed": ***********,
            "progressiveSinceLastWin": ***********,
            "seedSinceLastWin": ***********,
            "customProp": 1.15
        },
        "small": {
            "progressive": ***********,
            "seed": ***********,
            "progressiveSinceLastWin": ***********,
            "seedSinceLastWin": ***********,
            "customProp": 1.15
        },
        "$$metaInf": {
            "seqId": 11000,
            "disabled": 0
        }
    };
    if (ignoreZeroValuesInWallet) {
        delete expectedAccounts["$$metaInf"].disabled;
    }
    expect(jpWallet.data.accounts).deep.eq(expectedAccounts);

    // win
    for (let i = 0; i < 3; i += 1) {
        const ticker = await module.getTicker(true);
        for (const pool of Object.keys(ticker.pools)) {
            const poolDef = jpInstance.definition.list.find((item) => item.id === pool);
            await module.releaseWin("win:" + pool, undefined, undefined, [{
                type: JackpotWinType.PLAYER,
                pool: pool,
                amount: poolDef.seed.amount + ticker.pools[pool].progressive,
                seed: poolDef.seed.amount,
                progressive: ticker.pools[pool].progressive,
                properties: {
                    customProp: 2.23
                }
            }]);
        }
    }

    // verify wallet
    jpWallet = await wallet.get(`jackpot:${jpInstance.id}:${jpInstance.definition.currency}`);
    const expectedAccounts2 = {
        "large": {
            "progressive": 0,
            "seed": -*************,
            "progressiveSinceLastWin": 0,
            "seedSinceLastWin": 0,
            "customProp": 2.23
        },
        "medium": {
            "progressive": 0,
            "seed": -************,
            "progressiveSinceLastWin": 0,
            "seedSinceLastWin": 0,
            "customProp": 2.23
        },
        "small": {
            "progressive": 0,
            "seed": -***********,
            "progressiveSinceLastWin": 0,
            "seedSinceLastWin": 0,
            "customProp": 2.23
        },
        "$$metaInf": {
            "seqId": 11009,
            "disabled": 0
        }
    };
    if (ignoreZeroValuesInWallet) {
        delete expectedAccounts2["large"].progressive;
        delete expectedAccounts2["large"].progressiveSinceLastWin;
        delete expectedAccounts2["large"].seedSinceLastWin;
        delete expectedAccounts2["medium"].progressive;
        delete expectedAccounts2["medium"].progressiveSinceLastWin;
        delete expectedAccounts2["medium"].seedSinceLastWin;
        delete expectedAccounts2["small"].progressive;
        delete expectedAccounts2["small"].progressiveSinceLastWin;
        delete expectedAccounts2["small"].seedSinceLastWin;
        delete expectedAccounts2["$$metaInf"].disabled;
    }
    expect(jpWallet.data.accounts).deep.eq(expectedAccounts2);

    // verify history
    const jpHistory = await history.getHistory(jpInstance.id);
    expect(jpHistory.contributions).deep.eq({
        "large": {
            "count": 11000,
            "progressiveSum": 77.**************,
            "seedSum": 33.**************
        },
        "medium": {
            "count": 11000,
            "progressiveSum": 43.**************,
            "seedSum": 21.***************
        },
        "small": {
            "count": 11000,
            "progressiveSum": 34.**************,
            "seedSum": 9.***************
        }
    });
    expect(jpHistory.wins).deep.equal({
        "large": {
            "count": 3,
            "initialSeed": 1000,
            "progressiveMax": 77,
            "progressiveMin": 0,
            "progressiveSum": 77,
            "seedMax": 33,
            "seedMin": -1967,
            "seedSum": 3000
        },
        "medium": {
            "count": 3,
            "initialSeed": 100,
            "progressiveMax": 44,
            "progressiveMin": 0,
            "progressiveSum": 44,
            "seedMax": 22,
            "seedMin": -178,
            "seedSum": 300
        },
        "small": {
            "count": 3,
            "initialSeed": 10,
            "progressiveMax": 34,
            "progressiveMin": 0,
            "progressiveSum": 34,
            "seedMax": 10,
            "seedMin": -10,
            "seedSum": 30
        }
    });
    if (checkFullLog) {
        expect(jpHistory.fullLog.length).to.equal(9);
    }
}

async function simulateJpnServer(jackpotId: string, globalJp?: boolean) {
    const server = await createJPNServer({
        api: "direct",
        auth: {
            playerCode: "pl1",
            brandId: 1,
            currency: "EUR",
            gameCode: "test",
            region: "pov"
        },
        jackpots: [{ id: jackpotId, type: "sw-reel-mega" }],
        globalJp
    });
    for (let pos = 0; pos < 1000; pos++) {
        const data = {
            externalId: `${pos + 1}`,
            transactionId: `${pos + 1}`,
            amount: 1,
            exchangeRate: 1,
            roundId: "1"
        };
        await server.contribute(data);
    }

    // verify wallet
    const jpWallet: IWallet = await inMemoryWallet.get(`jackpot:${jackpotId}:EUR`);
    expect(jpWallet.data.accounts).deep.eq({
        "mega": {
            "progressive": **********,
            "progressiveSinceLastWin": **********,
            "seed": **********,
            "seedSinceLastWin": **********
        },
        "$$metaInf": {
            "seqId": 1000,
            "disabled": 0
        }
    });
}

describe("POV Simulation", function() {

    this.timeout(20000);

    before(flush);

    it("in-memory wallet", async() => {
        await simulate(inMemoryWallet);
    });

    it("redis wallet", async() => {
        redisWallet.collectJpFullLog = true;
        await simulate(redisWallet, true, true);
    });

    it("direct jpn server", async() => {
        await simulateJpnServer("test");
    });

    it("direct jpn server - global jp", async() => {
        await simulateJpnServer("test-global", true);
    });
});
