import { flush } from "../helpers";
import redisWallet from "../../pov/wallet/wallet.redis";
import { IOperation, ITransaction, IWallet } from "@skywind-group/sw-wallet";
import { DEFAULT_PRECISION } from "../../skywind/modules/jackpotWallet";
import { LOCAL_WALLET_OPERATION_NAME, WIN_TRX_OPERATION_ID_MARKER } from "../../skywind/modules/walletParams";
import { expect, use } from "chai";
use(require("chai-as-promised"));

describe("POV Shared wallet", () => {

    before(flush);

    it("dec property successfully ", async () => {
        const contribTrxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: 1,
            operationName: LOCAL_WALLET_OPERATION_NAME.contribute,
            params: {}
        };
        const contribTrx: ITransaction = await redisWallet.startTransaction("trx", contribTrxOperation);

        let jpWallet: IWallet = await contribTrx.getWallet("testjackpot1");
        let pool = jpWallet.accounts.get("pool");
        pool.inc("progressive", 100 * DEFAULT_PRECISION, "contribute");
        await contribTrx.commit();

        const winTrxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: WIN_TRX_OPERATION_ID_MARKER,
            operationName: LOCAL_WALLET_OPERATION_NAME.release,
            params: { wins: [] }
        };
        const winTrx: ITransaction = await redisWallet.startTransaction("trx", winTrxOperation);
        jpWallet = await winTrx.getWallet("testjackpot1");
        pool = jpWallet.accounts.get("pool");
        pool.inc("progressive", -99 * DEFAULT_PRECISION, "release");
        await winTrx.commit();

        jpWallet = await redisWallet.get("testjackpot1");
        const account = jpWallet.accounts.get("pool");
        expect(account.get("progressive")).to.equal(10 ** 9);
    });

    it("dec property successfully to zero", async () => {
        const contribTrxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: 1,
            operationName: LOCAL_WALLET_OPERATION_NAME.contribute,
            params: {}
        };
        const contribTrx: ITransaction = await redisWallet.startTransaction("trx", contribTrxOperation);

        let jpWallet: IWallet = await contribTrx.getWallet("testjackpot2");
        let pool = jpWallet.accounts.get("pool");
        pool.inc("progressive", 100 * DEFAULT_PRECISION, "release");
        await contribTrx.commit();

        const winTrxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: WIN_TRX_OPERATION_ID_MARKER,
            operationName: LOCAL_WALLET_OPERATION_NAME.release,
            params: { wins: [] }
        };
        const winTrx: ITransaction = await redisWallet.startTransaction("trx", winTrxOperation);
        jpWallet = await winTrx.getWallet("testjackpot2");
        pool = jpWallet.accounts.get("pool");
        pool.inc("progressive", -100 * DEFAULT_PRECISION, "release");
        await winTrx.commit();

        jpWallet = await redisWallet.get("testjackpot2");
        const account = jpWallet.accounts.get("pool");
        expect(account.get("progressive")).to.equal( undefined);
    });

    it("dec property insufficient balance", async () => {
        const contribTrxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: 1,
            operationName: LOCAL_WALLET_OPERATION_NAME.contribute,
            params: {}
        };
        const contribTrx: ITransaction = await redisWallet.startTransaction("trx", contribTrxOperation);

        let jpWallet: IWallet = await contribTrx.getWallet("testjackpot3");
        let pool = jpWallet.accounts.get("pool");
        pool.inc("progressive", 100 * DEFAULT_PRECISION, "contribute");
        await contribTrx.commit();

        const winTrxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: WIN_TRX_OPERATION_ID_MARKER,
            operationName: LOCAL_WALLET_OPERATION_NAME.release,
            params: { wins: [] }
        };
        const winTrx: ITransaction = await redisWallet.startTransaction("trx", winTrxOperation);
        jpWallet = await winTrx.getWallet("testjackpot3");
        pool = jpWallet.accounts.get("pool");
        pool.inc("progressive", -101 * DEFAULT_PRECISION, "contribute");
        await expect(winTrx.commit()).be.rejectedWith("INSUFFICIENT_BALANCE");
    });

    it("two decs property successfully ", async () => {
        const contribTrxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: 1,
            operationName: LOCAL_WALLET_OPERATION_NAME.contribute,
            params: {}
        };
        const contribTrx: ITransaction = await redisWallet.startTransaction("trx", contribTrxOperation);

        let jpWallet: IWallet = await contribTrx.getWallet("testjackpot4");
        let pool = jpWallet.accounts.get("pool");
        pool.inc("progressive", 100 * DEFAULT_PRECISION, "contribute");
        await contribTrx.commit();

        const winTrxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: WIN_TRX_OPERATION_ID_MARKER,
            operationName: LOCAL_WALLET_OPERATION_NAME.release,
            params: { wins: [] }
        };
        const winTrx: ITransaction = await redisWallet.startTransaction("trx", winTrxOperation);
        jpWallet = await winTrx.getWallet("testjackpot4");
        pool = jpWallet.accounts.get("pool");
        pool.inc("progressive", -45 * DEFAULT_PRECISION, "release");
        pool.inc("progressive", -54 * DEFAULT_PRECISION, "release");
        await winTrx.commit();

        jpWallet = await redisWallet.get("testjackpot4");
        const account = jpWallet.accounts.get("pool");
        expect(account.get("progressive")).to.equal(10 ** 9);
    });

    it("two decs property successfully to zero", async () => {
        const contribTrxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: 1,
            operationName: LOCAL_WALLET_OPERATION_NAME.contribute,
            params: {}
        };
        const contribTrx: ITransaction = await redisWallet.startTransaction("trx", contribTrxOperation);

        let jpWallet: IWallet = await contribTrx.getWallet("testjackpot5");
        let pool = jpWallet.accounts.get("pool");
        pool.inc("progressive", 100 * DEFAULT_PRECISION, "contribute");
        await contribTrx.commit();

        const winTrxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: WIN_TRX_OPERATION_ID_MARKER,
            operationName: LOCAL_WALLET_OPERATION_NAME.release,
            params: { wins: [] }
        };
        const winTrx: ITransaction = await redisWallet.startTransaction("trx", winTrxOperation);
        jpWallet = await winTrx.getWallet("testjackpot5");
        pool = jpWallet.accounts.get("pool");
        pool.inc("progressive", -50 * DEFAULT_PRECISION, "contribute");
        pool.inc("progressive", -50 * DEFAULT_PRECISION, "contribute");
        await winTrx.commit();

        jpWallet = await redisWallet.get("testjackpot5");
        const account = jpWallet.accounts.get("pool");
        expect(account.get("progressive")).to.equal( undefined);
    });

    it("two decs property insufficient balance", async () => {
        const contribTrxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: 1,
            operationName: LOCAL_WALLET_OPERATION_NAME.contribute,
            params: {}
        };
        const contribTrx: ITransaction = await redisWallet.startTransaction("trx", contribTrxOperation);

        let jpWallet: IWallet = await contribTrx.getWallet("testjackpot6");
        let pool = jpWallet.accounts.get("pool");
        pool.inc("progressive", 100 * DEFAULT_PRECISION, "contribute");
        await contribTrx.commit();

        const winTrxOperation: IOperation = {
            // increment operation id to make a difference with contribution
            operationId: WIN_TRX_OPERATION_ID_MARKER,
            operationName: LOCAL_WALLET_OPERATION_NAME.release,
            params: { wins: [] }
        };
        const winTrx: ITransaction = await redisWallet.startTransaction("trx", winTrxOperation);
        jpWallet = await winTrx.getWallet("testjackpot6");
        pool = jpWallet.accounts.get("pool");
        pool.inc("progressive", -50 * DEFAULT_PRECISION, "release");
        pool.inc("progressive", -51 * DEFAULT_PRECISION, "release");
        await expect(winTrx.commit()).be.rejectedWith("INSUFFICIENT_BALANCE");
    });
});
