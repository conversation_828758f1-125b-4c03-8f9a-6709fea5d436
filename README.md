# Skywind Jackpot Network Game API
Jackpot Network is a standalone application which will manage Jackpots defined for specific game, merchant or both.

## Local Development
This project is using TypeScript 2.0. Typings are not required as standalone dependency. Everything is managed through NPM.
```sh
$ git clone sw-jpn-server-api
$ cd sw-jpn-server-api
$ npm install
$ npm test
$ npm start
```

## Environment variables

| Variable                                | Description                                                       |
|-----------------------------------------|-------------------------------------------------------------------|
|`NODE_ENV`                               | Env mode (default: development)                                   |

### SERVER

| Variable                         | Description                                                                                   |
|----------------------------------|-----------------------------------------------------------------------------------------------|
|`SERVER_NAME`                     | Server name (default: JPN)                                                                    |
|`SERVER_TIMEOUT`                  | Timeout to close connection (default: 120000)                                                 |
|`HTTP_SERVER_KEEP_ALIVE_TIMEOUT`  | TTL (default: 0)                                                                              |
|`REGION_CODE`                     | Region code (default: default)                                                                |
|`SECRET_KEY`                      | Secret key (default: \_secret\_)                                                              |
|`ACCESS_TOKEN_SECRET`             | Access token                                                                                  |
|`ALLOW_CHEATING`                  | Allow cheating (default: false)                                                               |
|`MAX_WIN_RETRY`                   | Max win retry (default: 3)                                                                    |

### Internal SERVER

| Variable                                   | Description                                            |
|--------------------------------------------|--------------------------------------------------------|
|`INTERNAL_SERVER_PORT`                      | Internal server port (default: 4004)                   |

### Redis

| Variable                                | Description                                                                                  |
|-----------------------------------------|----------------------------------------------------------------------------------------------|
|`REDIS_HOST`                             | Redis hostname (default: redis)                                                              |
|`REDIS_PORT`                             | Redis port (default: 6379)                                                                   |
|`REDIS_CLUSTER_NAME`                     | Redis cluster name (optional only if specified sentinel)                                     |
|`REDIS_SENTINELS`                        | Redis sentinels configurations                                                               |
|`REDIS_CONNECTION_TIMEOUT`               | Redis connection timeout(default is 5000)                                                    |
|`REDIS_REPLICATION_FACTOR`               | Redis replication factor(only if we have cluster configuration (default is 0) timeout        |
|`REDIS_REPLICATION_TIMEOUT`              | Redis replication max lag ( default is 100 ms)                                               |
|`REDIS_PASSWORD`                         | Redis password                                                                               |
|`REDIS_MIN_CONNECTIONS`                  | Min number of connection in the pool (default: 2)                                            |
|`REDIS_MAX_CONNECTIONS`                  | Max number of connection in the pool (default: 10)                                           |
|`REDIS_MAX_IDLE_TIME_MS`                 | Timeout to close idle connections (default: 30000)                                           |
|`REDIS_MAX_RETRIERS_PER_REQUEST`         | How many time to repeat request in case of failed connection (default  is 0)                 |
|`REDIS_RETRY_MAX_DELAY_MS`               | (default is 1000)                                                                            |

### Wallet config

| Variable                                | Description                                                                                           |
|-----------------------------------------|-------------------------------------------------------------------------------------------------------|
|`WALLET_REDIS_HOST`                      | Redis hostname. If not set we will get REDIS_HOST.(default: redis)                                    |
|`WALLET_REDIS_PORT`                      | Redis port. If not set we will get REDIS_PORT. (default: 6379)                                        |
|`WALLET_REDIS_PASSWORD`                  | Redis password. If not set we will get REDIS_PASSWORD.                                                |
|`WALLET_REDIS_CLUSTER_NAME`              | Redis cluster name (optional only if specified sentinel)                                              |
|`WALLET_REDIS_SENTINELS`                 | Redis sentinels configurations                                                                        |
|`WALLET_REDIS_CONNECTION_TIMEOUT`        | Redis connection timeout (default is 5000)                                                            |
|`WALLET_REDIS_REPLICATION_FACTOR`        | Redis replication factor(only if we have cluster configuration (default is 0) timeout                 |
|`WALLET_REDIS_REPLICATION_TIMEOUT`       | Redis replication max lag ( default is 100 ms)                                                        |
|`WALLET_REDIS_MIN_CONNECTIONS`           | Min number of connection in the pool. If not set we will get REDIS_MIN_CONNECTIONS. (default: 2)      |
|`WALLET_REDIS_MAX_CONNECTIONS`           | Max number of connection in the pool. If not set we will get REDIS_MAX_CONNECTIONS. (default: 10)     |
|`WALLET_REDIS_MAX_IDLE_TIME_MS`          | Timeout to close idle connections. If not set we will get MAX_IDLE_TIME_MS. (default: 30000)          |
|`WALLET_REDIS_MAX_RETRIERS_PER_REQUEST`  | How many time to repeat request in case of failed connection (default  is 0)                          |
|`WALLET_REDIS_RETRY_MAX_DELAY_MS`        | (default is 1000)                                                                                     |

### Minimal logging level

| Variable                        | Description                                                    |
|---------------------------------|----------------------------------------------------------------|
|`LOG_LEVEL`                      | error, warn, info, debug (default: "info")                     |
|`POSTGRES_QUERY_LOGGING`         | logging all POSTGRES queries to console (default: false)       |

### Graylog

| Variable                        | Description                                                    |
|---------------------------------|----------------------------------------------------------------|
|`GRAYLOG_HOST`                   | Hostname of Graylog server (optional)                          |
|`GRAYLOG_PORT`                   | Graylog port               (optional)                          |

### Server configuration

| Variable                                | Description                                                                                           |
|-----------------------------------------|-------------------------------------------------------------------------------------------------------|
|`KEEP_ALIVE_TIMEOUT`                     | Keep alive for socket connection (default is 0 ,turned off)                                           |
|`CREATE_JP_TYPES_ON_START`               | Enable/Disable jp type creation on server start (default: false)                                      |

### Postgres

| Variable              | Description                                        |
|-----------------------|----------------------------------------------------|
|`PGHOST`               | Postgres hostname (default: localhost)             |
|`PGPORT`               | Postgres port (default: 5432)                      |
|`PGUSER`               | Postgres user                                      |
|`PGPASSWORD`           | Password                                           |
|`PGDATABASE`           | Database to connect to (default: jackpot)       |
|`PG_CA_CERT`           | Postgre CA certificate path                        |
|`PG_SECURE_CONNECTION` | Postgre secure connection switch true/false        |
|`PG_MAX_CONNECTIONS`   | Max number of connection in the pool (default: 10) |
|`PG_MAX_IDLE_TIME_MS`  | Timeout to close idle connections (default: 30000) |

### Kafka consumer configuration

| Variable                               | Description                                                        |
|----------------------------------------|--------------------------------------------------------------------|
|`KAFKA_UNLOADER_ENABLED`                | enable kafka unloader                                              |
|`KAFKA_BROKER_HOSTNAMES`                | Kafka broker hostname (default: localhost:9092)                    |
|`KAFKA_REQUIRE_ACK`                     | Acknowledgement type. Default is -1  (from all nodes)              |
|`KAFKA_ACK_TIMEOUT`                     | Acknowledge timeout. Default is 1000 ms                            |
|`KAFKA_CONNECT_TIMEOUT`                 | Kafka connection timeout. Default is 6000 ms                       |
|`KAFKA_REQUEST_TIMEOUT`                 | Kafka request timeout. Default is 5000 ms                          |
|`KAFKA_PARTITIONER_TYPE`                | Kafka partitioner type. Default is 2 (cyclic)                      |
|`KAFKA_MAX_SEND_ATTEMPT_TIMEOUT`        | Max timeout before send attempt  (exponential back off)            |
|`TOPIC_NAME`                            | Topic name (default: jpn-trx-log)                                  |

### Postgres archive db  with wallet archive table (optional)

| Variable                           | Description                                       |
|------------------------------------|---------------------------------------------------|
|`JPN_ARCHIVE_PGHOST`               | Postgres hostname                                  |
|`JPN_ARCHIVE_PGPORT`               | Postgres port                                      |
|`JPN_ARCHIVE_PGUSER`               | Postgres user                                      |
|`JPN_ARCHIVE_PGPASSWORD`           | Password                                           |
|`JPN_ARCHIVE_PGDATABASE`           | Database to connect to                             |
|`JPN_ARCHIVE_PG_SECURE_CONNECTION` | Postgres secure connection switch true/false       |
|`JPN_ARCHIVE_PG_MAX_CONNECTIONS`   | Max number of connection in the pool (default: 10) |
|`JPN_ARCHIVE_PG_MAX_IDLE_TIME_MS`  | Timeout to close idle connections (default: 30000) |
|`JPN_ARCHIVE_PGSCHEMA`             |                                                    |

### Trx storage

| Variable                                | Description                                                   |
|-----------------------------------------|---------------------------------------------------------------|
|`RECENT_TRX_TTL`                         | TTL  (default is 3)                                           |
|`RECENT_TRX_CRON`                        | Trx crone (default: "0 * * * *")                              |
|`RECENT_TRX_CLEANUP_ITERATION_PERIOD`    | Cleanup iteration (default: 60 * 1000)                        |
|`RECENT_TRX_CLEANUP_ITERATION_PAUSE`     | Pause between iteration (default: 50)                         |

### Unloader

| Variable                    | Description                                                                    |
|-----------------------------|--------------------------------------------------------------------------------|
|`MAX_ORPHAN_TTL`             | TTL  (default is 15)                                                           |
|`REPAIR_WORKER_CRON`         | Cron to instantiate repairment of orphan worker lists (default: "*/20 * * * *")|
|`UNLOADER_MAX_BATCH_SIZE`    | Max batch size for unloader (default: 100)                                     |
|`MAX_SPIN_POP_DURATION`      | Max interval to await batch (default: 500)                                     |
|`SPIN_POP_SLEEP_DURATION`    | Sleep interval (default: 100)                                                  |

### History unloader

| Variable                    | Description                                                                    |
|-----------------------------|--------------------------------------------------------------------------------|
|`HISTORY_WORKER_ALIVE`       | How often to update worker alive timestamp (default is 30)                     |
|`HISTORY_WORKER_ALIVE_TTL`   | TTL when worker considered to be stale (default: 120)                          |
|`HISTORY_WORKER_REPAIR_CRON` | Schedule to repair stale workers (default: "*/20 * * * *")                     |
|`UNLOADER_MAX_BATCH_SIZE`    | Max batch size for unloader (default: 1000)                                    |

### New Relic

| Variable          | Description                                       |
|-------------------|---------------------------------------------------|
|`NEWRELIC_ENABLED` | Enable/Disable New Relic (default: true)          |
|`NEWRELIC_KEY`     | New Relic key                                     |
|`NEWRELIC_ENV_NAME`| New Relic environment name (default: development) |
|`NEW_RELIC_APP_NAME`|Application name (default: JPN-SERVER-API)        |

### API request size limits
| Variable                 | Description                                                            |
|--------------------------|------------------------------------------------------------------------|
|`BODY_PARSER_JSON_LIMIT`  | Max size of request json body (default: 5242880)                       |
|`BODY_PARSER_URL_LIMIT`   | Max size of request url (default: 5242880)                             |
|`COMPRESSION_THRESHOLD`   | Compression threshold (default: 1024)                                  |

### Ticker server
| Variable                 | Description                                                            |
|--------------------------|------------------------------------------------------------------------|
|`TICKER_SERVER_PORT`      | Ticker server port (default: 5001)                                     |

### CORS and Site Whitelisting

| Variable                           | Description                                                                                                              |
|------------------------------------|--------------------------------------------------------------------------------------------------------------------------|
|`GAME_CORS_WHITELIST`               | Comma-separated list of allowed origins for /game endpoint requests                                                      |                                              |

### Internal server token

| Variable                              | Description                                                   |
|---------------------------------------|---------------------------------------------------------------|
|`INTERNAL_SERVER_TOKEN_ALGORITHM`      | Internal token algorithm  (default: HS512)                    |
|`INTERNAL_SERVER_TOKEN_ISSUER`         | Internal token issuer (default: skywindgroup)                 |
|`INTERNAL_SERVER_SECRET`               | Internal token secret                                         |
|`INTERNAL_SERVER_TOKEN_EXPIRES_IN`     | Internal token ttl(default is 300 seconds)                    |

### Remote API server

| Variable                                 | Description                                                   |
|------------------------------------------|---------------------------------------------------------------|
|`REMOTE_API_SERVER_PORT`                  | Remote server port  (default: 5002)                           |
|`REMOTE_API_KEEP_ALIVE_FREE_SOCKET_COUNT` | Keep alive pool for Remote server (default: 100)              |
|`REMOTE_API_KEEP_ALIVE_TIMEOUT`           | Keep alive of free socket (default: 30000)                    |
|`REMOTE_API_SOCKET_ACTIVE_TTL`            | Time to live of active socket (default: 60000)                |

### Remote Ticker

#### Redis

| Variable                                | Description                                                                                  |
|-----------------------------------------|----------------------------------------------------------------------------------------------|
|`REMOTE_TICKER_REDIS_HOST`                             | Redis hostname (default: redis)                                                              |
|`REMOTE_TICKER_REDIS_PORT`                             | Redis port (default: 6379)                                                                   |
|`REMOTE_TICKER_REDIS_CLUSTER_NAME`                     | Redis cluster name (optional only if specified sentinel)                                     |
|`REMOTE_TICKER_REDIS_SENTINELS`                        | Redis sentinels configurations                                                               |
|`REMOTE_TICKER_REDIS_CONNECTION_TIMEOUT`               | Redis connection timeout(default is 5000)                                                    |
|`REMOTE_TICKER_REDIS_REPLICATION_FACTOR`               | Redis replication factor(only if we have cluster configuration (default is 0) timeout        |
|`REMOTE_TICKER_REDIS_REPLICATION_TIMEOUT`              | Redis replication max lag ( default is 100 ms)                                               |
|`REMOTE_TICKER_REDIS_PASSWORD`                         | Redis password                                                                               |
|`REMOTE_TICKER_REDIS_MIN_CONNECTIONS`                  | Min number of connection in the pool (default: 2)                                            |
|`REMOTE_TICKER_REDIS_MAX_CONNECTIONS`                  | Max number of connection in the pool (default: 10)                                           |
|`REMOTE_TICKER_REDIS_MAX_IDLE_TIME_MS`                 | Timeout to close idle connections (default: 30000)                                           |
|`REMOTE_TICKER_REDIS_MAX_RETRIERS_PER_REQUEST`         | How many time to repeat request in case of failed connection (default  is 0)                 |
|`REMOTE_TICKER_REDIS_RETRY_MAX_DELAY_MS`               | (default is 1000)                                                                            |

#### Other Ticker options

| Variable                                 | Description                                                   |
|------------------------------------------|---------------------------------------------------------------|
|`REMOTE_TICKER_REFRESH_TIMEOUT`           | Refresh timeout  (default: 5000)                              |
|`REMOTE_TICKER_PREFIX`                    | Ticker prefix (default: remote-ticker)                        |
|`REMOTE_TICKER_RETRIES_SLEEP_TIMEOUT`     | Sleep timeout (default: 50)                                   |
|`REMOTE_TICKER_RETRIES_MAX_TIMEOUT`       | Retry until total spent time has reached this value (default: 5000)|


### TRX Id Pool

| Variable                                 | Description                                                   |
|------------------------------------------|---------------------------------------------------------------|
|`TRX_ID_POOL_MIN_LENGTH`                  | Min pool  (default: 10)                                       |
|`TRX_ID_POOL_MAX_LENGTH`                  | Max pool  (default: 1000)                                     |
|`TRX_ID_POOL_LOAD_FACTOR`                 | Pool load factor (default: 1.5)                               |
|`TRX_ID_POOL_REFRESH_TIMEOUT_SEC`         | Refresh timeout (default: 0)                                  |

### Remote request queue

| Variable                                         | Description                                                   |
|--------------------------------------------------|---------------------------------------------------------------|
|`REMOTE_REQUEST_QUEUE_PREFIX`                     | Queue prefix (default: 1remote-request)                                       |
|`REMOTE_REQUEST_QUEUE_RETRANSMIT_TIMEOUT_SEC`     | Queue retransmit timeout (default: 10 * 1000)                                     |
|`REMOTE_REQUEST_QUEUE_RETRANSMIT_MAX_TIMEOUT_SEC` | Queue retransmit max timeout (default: 3600)                               |
