package perf

import io.gatling.core.Predef._
import io.gatling.http.Predef._
import scala.util.Random
import scala.concurrent.duration._

class ContributeSimulation extends Simulation {

  // -----------------------    Settings --------------------------------------

  val concurrentUsers = 100
  val duration = 10 // minutes

  val mapiServer = "http://localhost:3000"
  val mapiSecretKey = "aaa11200-19f1-48c1-a78c-3a3d56095f38"
  val mapiUser = "SUPERADMIN"
  val mapiPwd = "SUPERadmin777"

  val jpnServer = "http://localhost:5000"
  val jackpotId = Random.alphanumeric.take(20).mkString

  // -----------------------    Sc<PERSON>rio --------------------------------------

  val feeder = Iterator.continually(Map(
    "mapiServer" -> mapiServer,
    "mapiSecretKey" -> mapiSecretKey,
    "mapiUser" -> mapiUser,
    "mapiPwd" -> mapiPwd,
    "jackpotId" -> jackpotId,
    "jpnServer" -> jpnServer
   ))

  val prepare = scenario("Prepare")
    .feed(feeder)
    .exec(
       http("Get master token")
        .post("${mapiServer}/v1/login")
        .body(StringBody("""{"secretKey": "${mapiSecretKey}", "username": "${mapiUser}", "password": "${mapiPwd}"}""")).asJSON
        .check(jsonPath("$..accessToken").saveAs("masterToken"))
    )
    .exec(
      http("Create jackpot")
        .post("/api/v2/jpn/jackpots")
        .header("X-Access-Token", "${masterToken}")
        .body(StringBody("""{"id": "${jackpotId}", "type": "sw_omqjp"}""")).asJSON
    )

  val scn = scenario("Contribute")
    .pause(5)    // wait for preparation script to finish (??)
    .feed(feeder)
    .exec(
      http("Auth jackpot")
        .post("/api/v2/jpn/auth")
        .body(StringBody("""{"jackpotIds": ["${jackpotId}"], "playerCode": "test", "brandId": 42, "currency": "USD", "gameCode": "test"}""")).asJSON
        .check(jsonPath("$..token").saveAs("jpToken"))
    )
    .during(duration minutes) {
    exec(
      http("Get transaction id")
        .get("/api/v2/jpn/contribute/transactionId")
        .header("X-Access-Token", "${jpToken}")
        .asJSON
        .check(status.is(200))
        .check(jsonPath("$..transactionId").saveAs("transactionId"))
    )
    .exec(
      http("Contribute")
        .post("/api/v2/jpn/contribute")
        .header("X-Access-Token", "${jpToken}")
        .body(StringBody("""{"transactionId": "${transactionId}", "amount": 0.01, "roundId": 1}""")).asJSON
        .check(status.is(200))
    )
    .exec(
      http("Ticker")
        .get("/api/v2/jpn/ticker")
        .header("X-Access-Token", "${jpToken}")
        .check(status.is(200))
    )
  }

  val httpConf = http.baseURL(jpnServer)
    .header("Cache-Control", "no-cache, no-store, must-revalidate")

  setUp(
    prepare.inject(atOnceUsers(1)),
    scn.inject(atOnceUsers(concurrentUsers))
  ).protocols(httpConf)
}
