{"swagger": "2.0", "info": {"description": "Skywind - Galaxy Pro - JPN Remote API", "version": "4.103", "title": "Skywind - Galaxy Pro - JPN Remote API"}, "basePath": "/api/v2/jpn", "schemes": ["http", "https"], "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Basic JWT authorization", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-Access-Token", "in": "header"}}, "tags": [{"name": "Remote JPN", "description": "Internal server token is required"}], "paths": {"/remote/transactionId": {"get": {"security": [{"apiKey": []}], "tags": ["Remote JPN"], "summary": "Generate remote transaction ids batch", "parameters": [{"$ref": "#/parameters/count"}], "responses": {"200": {"description": "Return generated transaction ids", "schema": {"$ref": "#/definitions/TransactionIdBatch"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 41: Internal server token expired\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 40: Internal server token error\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/remote/ticker": {"get": {"security": [{"apiKey": []}], "tags": ["Remote JPN"], "summary": "Return remote ticker", "parameters": [{"$ref": "#/parameters/jackpotId"}], "responses": {"200": {"description": "Return generated transaction ids", "schema": {"$ref": "#/definitions/TickerInformationResponse"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 41: Internal server token expired\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 40: Internal server token error\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/remote/gameFlow": {"post": {"security": [{"apiKey": []}], "tags": ["Remote JPN"], "summary": "Process game flow executed on remote server", "parameters": [{"in": "body", "name": "gameFlow", "description": "Remote game flow state", "schema": {"$ref": "#/definitions/RemoteGameFlowRequest"}}], "responses": {"200": {"description": "Game flow processing result", "schema": {"$ref": "#/definitions/RemoteGameFlowResponse"}}, "400": {"description": "- 3: Validation error\n- 11: <PERSON><PERSON> is missing\n- 41: Internal server token expired\n- 32: Required field is missing\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 40: Internal server token error\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 18: Jackpot game not found\n- 24: Jackpot instance not found\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 17: Jackpot not initialized\n- 25: Invalid jackpot result\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n- 5: Too many jackpot requests\n- 20: Concurrent jackpot win\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/remote/findOrCreateJackpot": {"post": {"security": [{"apiKey": []}], "tags": ["Remote JPN"], "summary": "Find or create jackpot", "parameters": [{"in": "body", "name": "info", "description": "Jackpot instance", "schema": {"$ref": "#/definitions/JackpotInstance"}}, {"name": "autoCreate", "in": "query", "type": "boolean", "description": "Indicates if jackpot should be created if not exists", "required": false}], "responses": {"200": {"description": "Found/created jackpot", "schema": {"$ref": "#/definitions/JackpotInstance"}}, "400": {"description": "- 3: Validation error\n- 11: <PERSON><PERSON> is missing\n- 41: Internal server token expired\n- 32: Required field is missing\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 40: Internal server token error\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 24: <PERSON><PERSON> instance not found\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 17: Jackpot not initialized\n- 25: Invalid jackpot result\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n- 5: Too many jackpot requests\n- 20: Concurrent jackpot win\n", "schema": {"$ref": "#/definitions/Error"}}}}}}, "parameters": {"count": {"name": "count", "in": "query", "type": "number", "description": "Count of requested element", "required": true}, "jackpotId": {"name": "jackpotId", "in": "query", "type": "string", "description": "Jackpot ID", "required": true}}, "definitions": {"PlayerInformation": {"type": "object", "properties": {"playerCode": {"type": "string", "description": "Player Code (Id)", "example": "Player_01"}, "brandId": {"type": "integer", "description": "Brand Entity Id", "example": 42}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "gameCode": {"type": "string", "description": "Game code", "example": "game1"}}}, "TransactionIdBatch": {"type": "object", "required": ["transactionIds"], "properties": {"transactionIds": {"type": "array", "items": {"type": "string", "description": "Transaction Id", "example": "cmQzgyMiwiaXNzIjoic2t5d2luZGdyb3VwIn0..."}}}}, "BaseRequest": {"type": "object", "required": ["transactionId", "roundId"], "properties": {"transactionId": {"type": "string", "description": "Transaction Id generated by GET /contribute/transactionId", "example": "cmQzgyMiwiaXNzIjoic2t5d2luZGdyb3VwIn"}, "externalId": {"type": "string", "description": "External Id", "example": "1234..."}, "exchangeRates": {"type": "object", "description": "Custom exchange rates from player currency.", "example": {"USD": 1.25, "CNY": 0.13}}, "roundId": {"type": "string", "description": "Round id", "example": 120089}}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "number", "description": "Error code", "example": 42}, "message": {"type": "string", "description": "Error message", "example": "Something went wrong"}}}, "Contribution": {"type": "object", "required": ["pool", "seed", "progressive"], "properties": {"pool": {"type": "string", "description": "Jackpot pool name", "example": "small"}, "seed": {"type": "number", "description": "Contributed seed amount", "example": 12345}, "progressive": {"type": "number", "description": "Contributed progressive amount", "example": 12345}}}, "JackpotWin": {"type": "object", "required": ["pool", "seed", "progressive", "amount", "<PERSON><PERSON><PERSON>", "exchangeRate"], "properties": {"pool": {"type": "string", "description": "Jackpot pool name", "example": "small"}, "seed": {"type": "number", "description": "Amount of seed to withdraw", "example": 12345}, "progressive": {"type": "number", "description": "Amout of progressive to withdraw", "example": 12345}, "amount": {"type": "number", "description": "Jackpot win amount in jackpot currency", "example": 12345}, "playerAmount": {"type": "number", "description": "Jackpot win amount in player currency", "example": 12345}, "exchangeRate": {"type": "number", "description": "Win exchange rate", "example": 1.3}}}, "RemoteGameFlowResult": {"type": "object", "required": ["jackpotId"], "properties": {"jackpotId": {"type": "string", "description": "Jackpot Id", "example": "jackpot_1"}, "contributions": {"type": "array", "description": "Jackpot contributions", "items": {"$ref": "#/definitions/Contribution"}}, "gameResult": {"type": "object", "description": "Result of checking jackpot win"}, "wins": {"type": "array", "description": "Jackpot win payouts", "items": {"$ref": "#/definitions/JackpotWin"}}}}, "RemoteGameFlowRequest": {"type": "object", "required": ["playerInfo", "request", "results"], "properties": {"playerInfo": {"type": "object", "description": "Jackpot player information", "$ref": "#/definitions/PlayerInformation"}, "request": {"type": "object", "description": "Jackpot game request", "$ref": "#/definitions/BaseRequest"}, "results": {"type": "array", "description": "Jackpot game flow results from remote server request", "items": {"$ref": "#/definitions/RemoteGameFlowResult"}}}}, "RemoteGameFlowResponse": {"type": "object", "required": ["results"], "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/RemoteGameFlowResult"}}}}, "TickerInformationResponse": {"type": "object", "required": ["jackpotId", "pools"], "properties": {"jackpotId": {"type": "string", "description": "Jackpot Instance Id", "example": "123"}, "seqId": {"type": "number", "description": "Sequential state id to determine version of the ticker", "example": 1555951115945}, "pools": {"$ref": "#/definitions/JackpotPoolsInformation"}}}, "JackpotPoolInformation": {"type": "object", "required": ["amount"], "properties": {"amount": {"type": "number", "description": "Current pool amount", "example": 10.52}, "info": {"type": "object", "description": "Additional information", "example": {"bet": {"$gt": 10}}}}}, "JackpotPoolsInformation": {"type": "object", "additionalProperties": {"$ref": "#/definitions/JackpotPoolInformation"}}, "PoolStateResponse": {"type": "object", "required": ["seed", "progressive", "initialSeed"], "properties": {"seed": {"type": "number", "description": "Jackpot seed", "example": 12345}, "progressive": {"type": "number", "description": "Jackpot progressive", "example": 12345}, "initialSeed": {"type": "number", "description": "Jackpot initial seed", "example": 12345}}}, "JackpotInstance": {"type": "object", "required": ["id", "type"], "properties": {"id": {"type": "string", "description": "Jackpot Id", "example": "jackpot_1"}, "type": {"type": "string", "description": "Jackpot type", "example": "small"}, "isTest": {"type": "boolean", "description": "Flag that determines whether the jackpot is test", "example": true}, "isGlobal": {"type": "boolean", "description": "Flag that indicates whether the jackpot is global", "example": true}}}}}