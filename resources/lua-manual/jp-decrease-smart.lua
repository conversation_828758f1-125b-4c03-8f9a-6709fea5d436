local precision = 1000000000
local function round(x)
    return x>=0 and math.floor(x+0.5) or math.ceil(x-0.5)
end

local function main()
    local argsCount = #KEYS
    if (argsCount ~= 3) then
        return "Wrong args count should be: source_jackpot_id:currency source_pool amount"
    end
    local jpWalletKey = "jackpot:"..KEYS[1]
    local pool = KEYS[2]
    local amount = tonumber(KEYS[3])
    if (amount == nil or amount < 0) then
        return "Amount should be positive"
    end
    local walletAmount = round(amount * precision)
    local currentProgressive = tonumber(redis.call("hget", jpWalletKey, pool..":progressive"))
    local jackpotId = string.sub(KEYS[1], 1, string.len(KEYS[1]) - 4)
    if (currentProgressive == nil) then
        local currentSeed = tonumber(redis.call("hget", jpWalletKey, pool..":seed"))
        if (currentSeed == nil) then
            redis.call("hset", jp<PERSON>alle<PERSON><PERSON><PERSON>, pool..":seed", -walletAmount)
            redis.call("hincrby", jpWalletKey, "$$metaInf:seqId", 1)
            local result = {}
            table.insert(result, "Jackpot "..KEYS[1] .. " pool=" .. pool .. " was decremented." .. " Previous seed value=0 , current seed value=" .. -amount)
            table.insert(result, "INSERT INTO swjackpot.jp_audit(jackpot_id, type, history, ts, initiator_type, initiator_name) VALUES('" .. jackpotId .. "', 'decrease-pool', '{\"pool\":\"" .. pool .. "\",\"seed\":{\"previous\":0,\"current\":" .. -amount .. "}}', NOW(), 'user', 'SUPERADMIN');")
            return result
        end
        redis.call("hincrby", jpWalletKey, pool..":seed", -walletAmount)
        redis.call("hincrby", jpWalletKey, "$$metaInf:seqId", 1)
        local newAmount = redis.call("hget", jpWalletKey, pool..":seed")
        local result = {}
        table.insert(result, "Jackpot "..KEYS[1] .. " pool=" .. pool .. " was decremented." .. " Previous seed value=" .. currentSeed / precision .. ", current seed value=" ..newAmount / precision)
        table.insert(result, "INSERT INTO swjackpot.jp_audit(jackpot_id, type, history, ts, initiator_type, initiator_name) VALUES('" .. jackpotId .. "', 'decrease-pool', '{\"pool\":\"" .. pool .. "\",\"seed\":{\"previous\":" .. currentSeed / precision  .. ",\"current\":" .. newAmount / precision .. "}}', NOW(), 'user', 'SUPERADMIN');")
        return result
    end

    if (currentProgressive < walletAmount) then
        local seedDecrement = walletAmount - currentProgressive
        local currentSeed = tonumber(redis.call("hget", jpWalletKey, pool..":seed")) or 0
        redis.call("hdel", jpWalletKey, pool..":progressive")
        redis.call("hincrby", jpWalletKey, pool..":seed", -seedDecrement)
        redis.call("hincrby", jpWalletKey, "$$metaInf:seqId", 1)
        local newAmount = redis.call("hget", jpWalletKey, pool..":seed")
        local result = {}
        table.insert(result, "Jackpot "..KEYS[1] .. " pool=" .. pool .. " was decremented." .. " Previous progressive value=" .. currentProgressive / precision .. ", current progressive value=0, previous seed value" .. currentSeed / precision .. ", current seed value " .. newAmount / precision)
        table.insert(result, "INSERT INTO swjackpot.jp_audit(jackpot_id, type, history, ts, initiator_type, initiator_name) VALUES('" .. jackpotId .. "', 'decrease-pool', '{\"pool\":\"" .. pool .. "\",\"seed\":{\"previous\":" .. currentSeed / precision  .. ",\"current\":" .. newAmount / precision .. "},\"progressive\":{\"previous\":" .. currentProgressive / precision .. ",\"current\":0}}', NOW(), 'user', 'SUPERADMIN');")
        return result
    end

    redis.call("hincrby", jpWalletKey, pool..":progressive", -walletAmount)
    redis.call("hincrby", jpWalletKey, "$$metaInf:seqId", 1)
    local newAmount = redis.call("hget", jpWalletKey, pool..":progressive")
    local result = {}
    table.insert(result, "Jackpot "..KEYS[1] .. " pool=" .. pool .. " was decremented." .. " Previous progressive value=" .. currentProgressive / precision .. ", current progressive value=" ..newAmount / precision)
    table.insert(result, "INSERT INTO swjackpot.jp_audit(jackpot_id, type, history, ts, initiator_type, initiator_name) VALUES('" .. jackpotId .. "', 'decrease-pool', '{\"pool\":\"" .. pool .. "\",\"progressive\":{\"previous\":" .. currentProgressive / precision .. ",\"current\":".. newAmount / precision .. "}}', NOW(), 'user', 'SUPERADMIN');")
    return result
end

return main()