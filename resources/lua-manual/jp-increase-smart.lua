local precision = 1000000000
local function round(x)
    return x>=0 and math.floor(x+0.5) or math.ceil(x-0.5)
end

local function main()
    local argsCount = #KEYS
    if (argsCount < 3) then
        return "Wrong args count should be: dest_jackpot_id:currency dest_pool amount [initial_seed]"
    end
    local jpWalletKey = "jackpot:"..KEYS[1]
    local pool = KEYS[2]
    local amount = tonumber(KEYS[3])
    if (amount == nil or amount < 0) then
        return "Amount should be positive"
    end
    local initialSeed = tonumber(KEYS[4])
    local walletAmount = round(amount * precision)
    local currentProgressive = tonumber(redis.call("hget", jpWalletKey, pool..":progressive")) or 0
    local currentSeedMinor = tonumber(redis.call("hget", jpWalletKey, pool..":seed")) or 0
    local jackpotId = string.sub(KEYS[1], 1, string.len(KEYS[1]) - 4)
    if (initialSeed == nil) then
        redis.call("hincrby", jpWallet<PERSON>ey, pool..":progressive", walletAmount)
        redis.call("hincrby", jpWallet<PERSON>ey, "$$metaInf:seqId", 1)
        local newProgressive = tonumber(redis.call("hget", jpWalletKey, pool..":progressive"))
        local result = {}
        table.insert(result, "Jackpot "..KEYS[1] .. " pool=" .. pool.. " was incremented. Previous progressive value=" .. currentProgressive / precision .. ", current progressive value=" .. newProgressive/ precision)
        table.insert(result, "INSERT INTO swjackpot.jp_audit(jackpot_id, type, history, ts, initiator_type, initiator_name) VALUES('" .. jackpotId .. "', 'increase-pool', '{\"pool\":\"" .. pool .. "\",\"progressive\":{\"previous\":" .. currentProgressive / precision  .. ",\"current\":" .. newProgressive / precision .. "}}', NOW(), 'user', 'SUPERADMIN');")
        return result
    end

    local initialSeedMinor = round(initialSeed * precision)
    local fullSeed = 2 * initialSeedMinor
    if (currentSeedMinor < fullSeed) then
        local seedIncrement = 0
        if (walletAmount > fullSeed - currentSeedMinor) then
            seedIncrement = fullSeed - currentSeedMinor
            walletAmount = walletAmount - seedIncrement
        else
            seedIncrement = walletAmount
            walletAmount = 0
        end
        redis.call("hincrby", jpWalletKey, pool..":seed", seedIncrement)
        redis.call("hincrby", jpWalletKey, pool..":progressive", walletAmount)
        redis.call("hincrby", jpWalletKey, "$$metaInf:seqId", 1)
        local newProgressive = tonumber(redis.call("hget", jpWalletKey, pool..":progressive"))
        local newSeed = tonumber(redis.call("hget", jpWalletKey, pool..":seed"))
        local result = {}
        table.insert(result, "Jackpot "..KEYS[1] .. " pool=" .. pool.. " was incremented. Previous seed value=" .. currentSeedMinor / precision .. " currentSeed=" .. newSeed / precision .. " progressive value=" .. currentProgressive / precision .. ", current progressive value=" .. newProgressive / precision)
        table.insert(result, "INSERT INTO swjackpot.jp_audit(jackpot_id, type, history, ts, initiator_type, initiator_name) VALUES('" .. jackpotId .. "', 'increase-pool', '{\"pool\":\"" .. pool .. "\",\"seed\":{\"previous\":" .. currentSeedMinor / precision  .. ",\"current\":" .. newSeed / precision .. "},\"progressive\":{\"previous\":" .. currentProgressive / precision .. ",\"current\":" .. newProgressive/ precision .. "}}', NOW(), 'user', 'SUPERADMIN');")
        return result
    else
        redis.call("hincrby", jpWalletKey, pool..":progressive", walletAmount)
        redis.call("hincrby", jpWalletKey, "$$metaInf:seqId", 1)
        local newProgressive = tonumber(redis.call("hget", jpWalletKey, pool..":progressive"))
        local result = {}
        table.insert(result, "Jackpot "..KEYS[1] .. " pool=" .. pool.. " was incremented. Previous progressive value=" .. currentProgressive / precision .. ", current progressive value=" .. newProgressive/ precision)
        table.insert(result, "INSERT INTO swjackpot.jp_audit(jackpot_id, type, history, ts, initiator_type, initiator_name) VALUES('" .. jackpotId .. "', 'increase-pool', '{\"pool\":\"" .. pool .. "\",\"progressive\":{\"previous\":" .. currentProgressive / precision  .. ",\"current\":" .. newProgressive / precision .. "}}', NOW(), 'user', 'SUPERADMIN');")
        return result
    end
end


return main()