local key = KEYS[1]
local pool = ARGV[1]
local initialSeed = tonumber(ARGV[2])
local totalSeed = tonumber(ARGV[3])
local totalProgressive = tonumber(ARGV[4])
local seed = tonumber(ARGV[5])
local progressive = tonumber(ARGV[6])

redis.call('HSET', key, pool .. ":initialSeed", initialSeed)
redis.call('HINCRBY', key, pool .. ":count", 1)

redis.call('HINCRBYFLOAT', key, pool .. ":seed:sum", seed)
local prevMinSeed = tonumber(redis.call('HGET', key, pool .. ":seed:min"))
if not prevMinSeed or totalSeed < prevMinSeed then
    redis.call('HSET', key, pool .. ":seed:min", totalSeed)
end
local prevMaxSeed = tonumber(redis.call('HGET', key, pool .. ":seed:max"))
if not prevMaxSeed or totalSeed > prevMaxSeed then
    redis.call('HSET', key, pool .. ":seed:max", totalSeed)
end

redis.call('HINCRBYFLOAT', key, pool .. ":progressive:sum", progressive)
local prevMinProgressive = tonumber(redis.call('HGET', key, pool .. ":progressive:min"))
if not prevMinProgressive or progressive < prevMinProgressive then
    redis.call('HSET', key, pool .. ":progressive:min", progressive)
end
local prevMaxProgressive = tonumber(redis.call('HGET', key, pool .. ":progressive:max"))
if not prevMaxProgressive or progressive > prevMaxProgressive then
    redis.call('HSET', key, pool .. ":progressive:max", progressive)
end
