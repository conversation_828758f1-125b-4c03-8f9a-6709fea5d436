local walletKey = KEYS[1]
local jpWallet = cjson.decode(ARGV[1])
local merge = ARGV[2]

local currentWallet = redis.call('HGETALL', walletKey)
if currentWallet and merge ~= 'true' then
    for i = 1, #currentWallet, 2 do
        local key = currentWallet[i]
        if string.match(key, "seed") or string.match(key, "progressive") then
            return "ERROR_JACKPOT_NOT_EMPTY"
        end
    end
end

for key, value in pairs(jpWallet) do
    if string.match(key, "seed") or string.match(key, "progressive") then
        redis.call('HINCRBY', walletKey, key, value)
    end
end

local resultWallet = redis.call('HGETALL', walletKey)

local results = {
    remoteJP = jpWallet,
    localJP = currentWallet,
    resultJP = resultWallet
};

return cjson.encode(results)
