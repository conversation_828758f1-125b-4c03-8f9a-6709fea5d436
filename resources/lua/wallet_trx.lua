local key = KEYS[1]
local replies = {}
local replyCounter = 0

local valuesCache = {};
for i = 1, #ARGV, 4 do
    local op = ARGV[i]
    local prop = ARGV[i + 1]
    local value = tonumber(ARGV[i + 2])
    local minValue = tonumber(ARGV[i + 3])

    if (op == 'inc') then
        if value < 0 then
            local currentValue = valuesCache[key..prop]
            if not currentValue then
                currentValue = tonumber(redis.call('HGET', key, prop))
                if not currentValue then
                    currentValue = 0
                end
            end

            local finalValue = currentValue + value
            valuesCache[key..prop] = finalValue
            if finalValue < minValue then
                return "insufficient balance"
            end
        end
    end
end

for i = 1, #ARGV, 4 do
    local op = ARGV[i]
    local prop = ARGV[i + 1]
    local value = tonumber(ARGV[i + 2])
    local minValue = tonumber(ARGV[i + 3])
    if (op == 'inc') then
        local reply = redis.call('HINCRBYFLOAT', key, prop, value)
        if (tonumber(reply) == 0) then
            redis.call('HDEL', key, prop)
        end
        replyCounter = replyCounter + 1
        replies[replyCounter] = reply
    else
        if (value == 0) then
            redis.call('HDEL', key, prop)
        else
            redis.call('HSET', key, prop, value)
        end
    end
end

return replies
