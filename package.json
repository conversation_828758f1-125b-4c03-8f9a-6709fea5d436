{"name": "@skywind-group/sw-jpn-server", "version": "5.51.0", "main": "dist/index.js", "typings": "src/index.ts", "scripts": {"start": "node dist/skywind/app.js", "test": "MEASURES_BASE_INSTRUMENT=false nyc node_modules/.bin/_mocha dist/test/**/**/*.spec.js dist/test/**/*.spec.js dist/test/*.spec.js", "clean": "rm -rf ./dist", "compile": "tsc -b tsconfig.json", "version": "echo $npm_package_version $( git log --pretty=format:'%h' -n 1) $(date) > ./dist/skywind/version", "sonar": "node sonarqube.mjs", "lint": "eslint --ext .ts src", "lint:fix": "eslint --ext .ts src --fix", "preinstall": "node preinstall.cjs"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@skywind-group/sw-random-cs-cheating": "1.0.8", "@types/chai": "^4.3.20", "@types/chai-as-promised": "^7.1.8", "@types/chai-http": "3.0.5", "@types/express": "4.17.21", "@types/jsonwebtoken": "9.0.8", "@types/lodash": "4.17.15", "@types/mocha": "^10.0.10", "@types/node": "^20.17.19", "@types/request": "2.48.12", "@types/sinon": "^17.0.3", "@types/superagent": "^8.1.7", "@typescript-eslint/eslint-plugin": "^8.24.1", "@typescript-eslint/parser": "^8.24.1", "eslint": "^9.21.0", "chai": "4.5.0", "chai-as-promised": "^7.1.1", "chai-http": "3.0.0", "mocha": "10.8.2", "sinon": "^18.0.1", "sinon-chai": "3.7.0", "sonarqube-scanner": "3.5.0", "ts-node": "^10.7.0", "typescript": "4.9.5", "nyc": "17.1.0"}, "dependencies": {"@skywind-group/sw-currency-exchange": "2.3.12", "@skywind-group/sw-gameprovider-adapter-core": "1.3.2", "@skywind-group/sw-jpn-core": "2.0.0", "@skywind-group/sw-jpn-games": "2.0.0", "@skywind-group/sw-random-cs": "1.0.8", "@skywind-group/sw-utils": "2.3.8", "@skywind-group/sw-wallet": "1.0.2", "@skywind-group/gelf-stream": "1.2.6", "agentkeepalive": "^4.5.0", "js-big-integer": "1.0.2", "body-parser": "1.20.3", "bole": "5.0.17", "bole-console": "0.1.10", "cls-hooked": "4.2.2", "compression": "1.8.0", "cookie-parser": "1.4.7", "express": "4.21.2", "express-prom-bundle": "^7.0.0", "emitter-listener": "^1.1.2", "generic-pool": "3.9.0", "ioredis": "5.5.0", "jsonwebtoken": "9.0.2", "kafka-node": "5.0.0", "lodash": "4.17.21", "method-override": "3.0.0", "node-schedule": "2.1.1", "pg": "8.13.3", "prom-client": "~15.0.0", "reflect-metadata": "0.2.2", "request": "2.88.2", "superagent": "^9.0.2", "sequelize": "6.37.5", "swagger-tools": "0.10.4", "swagger-ui-dist": "5.19.0", "uuid": "9.0.1"}}