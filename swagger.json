{"swagger": "2.0", "info": {"description": "Skywind - Galaxy Pro - JPN API", "version": "4.103", "title": "Skywind - Galaxy Pro - JPN API"}, "basePath": "/api/v2/jpn", "schemes": ["http", "https"], "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Basic JWT authorization", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-Access-Token", "in": "header"}, "Permissions": {"description": "Dummy OAuth2 authorization for permissions through scopes", "type": "oauth2", "authorizationUrl": "http://localhost:3000/oauth/dialog", "tokenUrl": "http://localhost:3000/oauth/token", "flow": "accessCode", "scopes": {"jackpot": "Manage jackpot types and instances", "jackpot:type": "Manage jackpot types", "jackpot:type:view": "View jackpot types", "jackpot:type:edit": "Edit jackpot types", "jackpot:type:create": "Create jackpot types", "jackpot:type:delete": "Delete jackpot types", "jackpot:instance": "Manage jackpot instances", "jackpot:instance:view": "View jackpot instances", "jackpot:instance:edit": "Edit jackpot instances", "jackpot:instance:edit:is-test": "Edit isTest jackpot instance parameter", "jackpot:instance:create": "Create jackpot instances", "jackpot:instance:delete": "Delete jackpot instances", "jackpot:pool:update": "Update jackpot pool"}}}, "tags": [{"name": "Transfers", "description": "Group of endpoints used by internal services for jackpot pools unsafe financial operations. Under the hood it uses short life-time tokens. Currently we have not any expossed auth endpoints for this operations. For develop envs. we have non-expiring tokens See https://jira.skywindgroup.com/browse/SWS-2982 . Or if you have access token secret from jackpot (ACCESS_TOKEN_SECRET from env.vars) you can generate it manualy based on example token from previos link.", "externalDocs": {"url": "https://jira.skywindgroup.com/browse/SWS-2982"}}, {"name": "Internal", "description": "No access token required."}, {"name": "Model", "description": "Access token from MAPI with jackpot permissions is required."}, {"name": "JPN", "description": "/auth endpoint invocation is required to get access token for this group."}], "paths": {"/auth": {"post": {"tags": ["JPN"], "summary": "Authorize player to jackpots", "parameters": [{"in": "body", "name": "auth", "description": "Auth data.", "schema": {"$ref": "#/definitions/AuthRequest"}}], "responses": {"200": {"description": "Return auth token", "schema": {"$ref": "#/definitions/AuthResponse"}}, "400": {"description": "- 3: Validation error\n- 28: <PERSON><PERSON><PERSON><PERSON> not found\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 14: <PERSON><PERSON> not found\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/ticker": {"get": {"security": [{"apiKey": []}], "tags": ["JPN"], "summary": "Get tickers of all jackpots player is authorized", "parameters": [{"in": "query", "name": "exchangeRates", "type": "string", "description": "JSON string with custom rates from player currency, e.g. {\"USD\": 1.25, \"CNY\": 0.13}"}], "responses": {"200": {"description": "200 Return jackpot ticker", "schema": {"type": "array", "items": {"$ref": "#/definitions/TickerInformationResponse"}}}, "400": {"description": "- 3: Validation error\n- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/contribute/transactionId": {"get": {"security": [{"apiKey": []}], "tags": ["JPN"], "summary": "Generate contribution transaction Id", "responses": {"200": {"description": "Return generated transaction Id", "schema": {"$ref": "#/definitions/TransactionIdResponse"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 17: <PERSON><PERSON> not initialized\n"}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/contribute": {"post": {"security": [{"apiKey": []}], "tags": ["JPN"], "summary": "Contribute specified amount to all jackpots that player is authorized", "parameters": [{"in": "body", "name": "contribution", "description": "Contribution data", "schema": {"$ref": "#/definitions/ContributionRequest"}}], "responses": {"200": {"description": "Return updated events", "schema": {"$ref": "#/definitions/ContributionResponse"}}, "400": {"description": "- 3: Validation error\n- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n- 32: Required field is missing\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 18: Jackpot game not found\n- 24: Jackpot instance not found\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 17: Jackpot not initialized\n- 25: Invalid jackpot result\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n- 5: Too many jackpot requests\n- 20: Concurrent jackpot win\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/contribute/deferred": {"post": {"security": [{"apiKey": []}], "tags": ["JPN"], "summary": "Prepare contribution data for deferred contribution", "parameters": [{"in": "body", "name": "contribution", "description": "Contribution data", "schema": {"$ref": "#/definitions/DeferredContributionRequest"}}], "responses": {"200": {"description": "Return contribution data", "schema": {"$ref": "#/definitions/DeferredContributionResponse"}}, "400": {"description": "- 3: Validation error\n- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n- 32: Required field is missing\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 18: Jackpot game not found\n- 24: Jackpot instance not found\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 17: Jackpot not initialized\n- 25: Invalid jackpot result\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error", "schema": {"$ref": "#/definitions/Error"}}}}}, "/checkWin": {"post": {"security": [{"apiKey": []}], "tags": ["JPN"], "summary": "Check if jackpot is triggered", "parameters": [{"in": "body", "name": "checkWin", "description": "Check win data", "schema": {"$ref": "#/definitions/CheckWinRequest"}}], "responses": {"200": {"description": "Return updated events", "schema": {"$ref": "#/definitions/CheckWinResponse"}}, "400": {"description": "- 3: Validation error\n- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n- 32: Required field is missing\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 18: Jackpot game not found\n- 24: Jackpot instance not found\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 17: Jackpot not initialized\n- 25: Invalid jackpot result\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n- 5: Too many jackpot requests\n- 20: Concurrent jackpot win\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/minigame": {"post": {"security": [{"apiKey": []}], "tags": ["JPN"], "summary": "Update minigame status", "parameters": [{"in": "body", "name": "mini_game", "description": "Update minigame status", "schema": {"$ref": "#/definitions/MiniGameRequest"}}], "responses": {"200": {"description": "Return updated events", "schema": {"$ref": "#/definitions/MiniGameResponse"}}, "400": {"description": "- 3: Validation error\n- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n- 15: Mini-game not found\n- 29: Jack<PERSON> is not authorized\n- 31: Win mini-game is not supported\n- 32: Required field is missing\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 18: Jackpot game not found\n- 24: Jackpot instance not found\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 4: Bad jackpot context state\n- 17: Jackpot not initialized\n- 25: Invalid jackpot result\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n- 5: Too many jackpot requests\n- 20: Concurrent jackpot win\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/win/confirm": {"post": {"security": [{"apiKey": []}], "tags": ["JPN"], "summary": "Confirms that jackpot win amount was paid to player", "parameters": [{"in": "body", "name": "confirm_win", "description": "Confirms that jackpot win amount was paid to player", "schema": {"$ref": "#/definitions/WinConfirmRequest"}}], "responses": {"200": {"description": "Return updated events", "schema": {"$ref": "#/definitions/WinConfirmResponse"}}, "400": {"description": "- 3: Validation error\n- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 24: <PERSON><PERSON> instance not found\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 17: <PERSON><PERSON> not initialized\n"}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/win": {"post": {"security": [{"apiKey": []}], "tags": ["JPN"], "summary": "Trigger to win jackpot, This win shoud be confirmed by POST /win/confirm", "parameters": [{"in": "body", "name": "win", "description": "Trigger jackpot win", "schema": {"$ref": "#/definitions/WinJackpotRequest"}}], "responses": {"200": {"description": "Return updated events", "schema": {"$ref": "#/definitions/WinJackpotResponse"}}, "400": {"description": "- 3: Validation error\n- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n- 29: Jackpot is not authorized\n- 30: Win Jackpot is not supported\n- 32: Required field is missing\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 18: Jackpot game not found\n- 24: Jackpot instance not found\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 4: Bad jackpot context state\n- 17: Jackpot not initialized\n- 25: Invalid jackpot result\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n- 5: Too many jackpot requests\n- 20: Concurrent jackpot win\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/pools/transfer/transactionId": {"get": {"security": [{"apiKey": []}], "tags": ["Transfers"], "summary": "Generate transaction Id for pool transfer operations", "responses": {"200": {"description": "Return generated transaction Id", "schema": {"$ref": "#/definitions/TransactionIdResponse"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 17: <PERSON><PERSON> not initialized\n"}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/pools/transfer/progressive": {"post": {"security": [{"apiKey": []}], "tags": ["Transfers"], "summary": "Move fixed or all amout of progressive from one pool to another within one jackpot [feature transfer]", "parameters": [{"in": "body", "name": "transfer_progressive", "description": "Transfer amount of progressive from one pool to another", "schema": {"$ref": "#/definitions/ProgressiveTransferRequest"}}], "responses": {"201": {"description": "Transfer is committed successfully for the first time"}, "200": {"description": "Transfer has been ALREADY successfully committed"}, "400": {"description": "- 3: Validation error\n- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n- 19: Insufficient jackpot balance\n- 34: This jackpot doesn't support this feature\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 18: Jackpot game not found\n- 24: Jackpot instance not found\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 17: <PERSON><PERSON> not initialized\n"}, "500": {"description": "- 1: JPN internal server error\n- 5: Too many jackpot requests\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/pools/{pool_id}": {"parameters": [{"$ref": "#/parameters/pool_id"}], "get": {"security": [{"apiKey": []}], "tags": ["Transfers"], "summary": "Return pool state, ie seed, progressive, initial seed", "responses": {"200": {"description": "Returns pool state, ie seed, progressive, initial seed", "schema": {"$ref": "#/definitions/PoolStateResponse"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 17: <PERSON><PERSON> not initialized\n"}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["jackpot:pool:update"]}], "tags": ["Transfers"], "summary": "Updates amount of progressive and seed in the pool for test jackpot", "parameters": [{"in": "body", "name": "update", "description": "Update payload", "schema": {"$ref": "#/definitions/UpdatePoolRequest"}}], "responses": {"201": {"description": "Successfully updated", "schema": {"$ref": "#/definitions/UpdatePoolResponse"}}, "200": {"description": "Success transferred", "schema": {"$ref": "#/definitions/UpdatePoolResponse"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 17: <PERSON><PERSON> not initialized\n"}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/pools": {"get": {"security": [{"apiKey": []}], "tags": ["Transfers"], "summary": "Return pool state, ie seed, progressive, initial seed", "responses": {"200": {"description": "Returns pool state, ie seed, progressive, initial seed", "schema": {"$ref": "#/definitions/AllPoolsStateResponse"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 17: <PERSON><PERSON> not initialized\n"}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/pools/{pool_id}/deposit": {"parameters": [{"$ref": "#/parameters/pool_id"}], "post": {"security": [{"apiKey": []}], "tags": ["Transfers"], "summary": "Transfer in fixed amount of progressive and seed to the pool. Note, values can be negative for withdraw [feature deposit]", "parameters": [{"in": "body", "name": "deposit", "description": "Deposit payload", "schema": {"$ref": "#/definitions/PoolDepositRequest"}}], "responses": {"201": {"description": "Success transfered", "schema": {"$ref": "#/definitions/PoolDepositResponse"}}, "200": {"description": "Success transferred", "schema": {"$ref": "#/definitions/PoolDepositResponse"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> has expired\n- 34: This jackpot doesn't support this feature\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 17: <PERSON><PERSON> not initialized\n"}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/types": {"get": {"parameters": [{"$ref": "#/parameters/names"}], "security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:type", "jackpot:type:view"]}], "tags": ["Model"], "summary": "Return all jackpot types", "responses": {"200": {"description": "Return all jackpot types", "schema": {"type": "array", "items": {"$ref": "#/definitions/JackpotType"}}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:type", "jackpot:type:create"]}], "tags": ["Model"], "summary": "Create and store jackpot type", "parameters": [{"in": "body", "name": "jackpot_body", "description": "Jackpot type to save", "schema": {"$ref": "#/definitions/JackpotType"}}], "responses": {"201": {"description": "Return created jackpot type", "schema": {"$ref": "#/definitions/JackpotType"}}, "400": {"description": "- 3: Validation error\n- 26: Jackpot type already exists\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/types/{type}": {"parameters": [{"$ref": "#/parameters/type"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:type", "jackpot:type:view"]}], "tags": ["Model"], "summary": "Return jackpot type by type name", "responses": {"200": {"description": "Return jackpot types by type name", "schema": {"$ref": "#/definitions/JackpotType"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 23: Jackpot type not found\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:type", "jackpot:type:edit"]}], "tags": ["Model"], "summary": "Update jackpot type information by type name", "parameters": [{"in": "body", "name": "jackpot body", "description": "Jackpot type to update", "schema": {"$ref": "#/definitions/JackpotType"}}], "responses": {"200": {"description": "Return modified jackpot type", "schema": {"$ref": "#/definitions/JackpotType"}}, "400": {"description": "- 3: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 23: Jackpot type not found\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:type", "jackpot:type:delete"]}], "tags": ["Model"], "summary": "Delete jackpot type by type name", "responses": {"204": {"description": "Successfuly deleted"}, "400": {"description": "- 3: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 23: Jackpot type not found\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/initType/{type}": {"parameters": [{"$ref": "#/parameters/type"}, {"name": "configurable", "in": "query", "type": "boolean", "description": "Indicates if jackpot type can be reconfigured via API", "required": false}, {"name": "overridable", "in": "query", "type": "boolean", "description": "Indicates if jackpot type definition can be overwritten on instance level", "required": false}, {"name": "canBeDisabled", "in": "query", "type": "boolean", "description": "Indicates if jackpot instance of this type can be disabled", "required": false}], "post": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:type", "jackpot:type:view"]}], "tags": ["Model"], "summary": "Create new jackpot type from jp-games module, Will throw error if jp type already exists", "responses": {"200": {"description": "Return created jackpot types", "schema": {"$ref": "#/definitions/JackpotType"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 26: Jackpot type already exist\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/updateType/{type}": {"parameters": [{"$ref": "#/parameters/type"}, {"$ref": "#/parameters/initiator"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:type", "jackpot:type:view"]}], "tags": ["Model"], "summary": "Update jackpot type from jp-games module", "responses": {"200": {"description": "Return updated jackpot type", "schema": {"$ref": "#/definitions/JackpotType"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/validateTypes": {"get": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:type", "jackpot:type:view"]}], "tags": ["Model"], "summary": "Validates jackpot types configuration with type in jp-games module", "responses": {"200": {"description": "Return Ok or error list"}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 36: JP type distinguish error\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/jackpots/{id}/enable": {"parameters": [{"$ref": "#/parameters/id"}, {"$ref": "#/parameters/initiator"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:instance", "jackpot:instance:edit"]}], "tags": ["Model"], "summary": "Enable disabled jackpot instance", "responses": {"200": {"description": "Enabled jackpot instance", "schema": {"$ref": "#/definitions/JackpotInstance"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/jackpots/{id}/disable": {"parameters": [{"$ref": "#/parameters/id"}, {"in": "query", "name": "disableMode", "type": "number", "required": false, "description": "Jackpot disable mode: 0 - immediately (default), 1 - on next win"}, {"$ref": "#/parameters/initiator"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:instance", "jackpot:instance:edit"]}], "tags": ["Model"], "summary": "Disable jackpot instance", "responses": {"200": {"description": "Disable jackpot instance", "schema": {"$ref": "#/definitions/JackpotInstance"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/regions": {"get": {"security": [{"apiKey": []}, {"Permissions": ["jackpot"]}], "tags": ["Model"], "summary": "Return all jackpot regions", "responses": {"200": {"description": "Return all jackpot regions", "schema": {"type": "array", "items": {"$ref": "#/definitions/JackpotRegion"}}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["jackpot"]}], "tags": ["Model"], "summary": "Create jackpot region", "parameters": [{"in": "body", "name": "region_body", "description": "Jackpot region to save", "schema": {"$ref": "#/definitions/JackpotRegion"}}], "responses": {"201": {"description": "Return created jackpot region", "schema": {"$ref": "#/definitions/JackpotRegion"}}, "400": {"description": "- 3: Validation error\n- 26: Jackpot region already exists\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/regions/{code}": {"parameters": [{"$ref": "#/parameters/code"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["jackpot"]}], "tags": ["Model"], "summary": "Return jackpot region by code", "responses": {"200": {"description": "Return jackpot region by code", "schema": {"$ref": "#/definitions/JackpotRegion"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 23: Jackpot region not found\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["jackpot"]}], "tags": ["Model"], "summary": "Update jackpot region by code", "parameters": [{"in": "body", "name": "region_body", "description": "Jackpot region to update", "schema": {"$ref": "#/definitions/JackpotRegion"}}], "responses": {"200": {"description": "Return modified jackpot region", "schema": {"$ref": "#/definitions/JackpotRegion"}}, "400": {"description": "- 3: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 23: Jackpot region not found\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["jackpot"]}], "tags": ["Model"], "summary": "Delete jackpot region by code", "responses": {"204": {"description": "Successfuly deleted"}, "400": {"description": "- 3: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 23: Jackpot region not found\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/jackpots": {"get": {"parameters": [{"in": "query", "name": "ids", "type": "string", "required": false, "description": "Comma separated jackpot ids to return. (All by default)"}], "security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:instance", "jackpot:instance:view"]}], "tags": ["Model"], "summary": "Return all jackpot instances", "responses": {"200": {"description": "Return all jackpot instances", "schema": {"type": "array", "items": {"$ref": "#/definitions/JackpotInstance"}}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:instance", "jackpot:instance:create"]}], "tags": ["Model"], "summary": "Create and store new jackpot instance", "parameters": [{"in": "body", "name": "jackpot_body", "description": "Jackpot instance to save", "schema": {"$ref": "#/definitions/JackpotInstanceRequest"}}, {"$ref": "#/parameters/initiator"}, {"name": "autoCreateRemote", "in": "query", "type": "boolean", "description": "Indicates if global jackpot should be automatically created on remote side (only if regionCode is provided)", "required": false}], "responses": {"201": {"description": "Return created jackpot instance", "schema": {"$ref": "#/definitions/JackpotInstance"}}, "400": {"description": "- 3: Validation error\n- 27: Jack<PERSON> instance already exists\n- 28: <PERSON><PERSON><PERSON><PERSON> not found\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/jackpots/{id}": {"parameters": [{"$ref": "#/parameters/id"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:instance", "jackpot:instance:view"]}], "tags": ["Model"], "summary": "Return jackpot instance by jackpot id", "responses": {"200": {"description": "Return jackpot instance by jackpot id", "schema": {"$ref": "#/definitions/JackpotInstance"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 24: <PERSON><PERSON> instance not found\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:instance", "jackpot:instance:edit"]}], "tags": ["Model"], "summary": "Update jackpot instance by jackpot id", "parameters": [{"in": "body", "name": "jackpot body", "description": "Jackpot body to update", "schema": {"$ref": "#/definitions/JackpotInstanceRequest"}}, {"$ref": "#/parameters/initiator"}], "responses": {"200": {"description": "Return modified jackpot instance", "schema": {"$ref": "#/definitions/JackpotInstance"}}, "400": {"description": "- 3: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 24: <PERSON><PERSON> instance not found\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:instance", "jackpot:instance:delete"]}], "parameters": [{"$ref": "#/parameters/initiator"}], "tags": ["Model"], "summary": "Mark jackpot instance as deleted by jackpot id.", "responses": {"204": {"description": "Successfuly marked as deleted"}, "400": {"description": "- 3: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 24: <PERSON><PERSON> instance not found\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/jackpots/{id}/isTest": {"parameters": [{"$ref": "#/parameters/id"}], "patch": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:instance", "jackpot:instance:edit", "jackpot:instance:edit:is-test"]}], "tags": ["Model"], "summary": "Update jackpot instance isTest parameter by jackpot id", "parameters": [{"in": "query", "name": "isTest", "description": "Jackpot isTest parameter", "type": "boolean", "required": true}, {"$ref": "#/parameters/initiator"}], "responses": {"200": {"description": "Return modified jackpot instance", "schema": {"$ref": "#/definitions/JackpotInstance"}}, "400": {"description": "- 3: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 24: <PERSON><PERSON> instance not found\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/jackpots/{id}/ticker": {"parameters": [{"$ref": "#/parameters/id"}, {"$ref": "#/parameters/currency"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:instance", "jackpot:instance:view"]}], "tags": ["Model"], "summary": "Get Jackpot ticker by jackpot Id", "responses": {"200": {"description": "Return jackpot ticker", "schema": {"type": "array", "items": {"$ref": "#/definitions/JackpotInformation"}}}, "400": {"description": "28 - <PERSON><PERSON><PERSON><PERSON> not fount", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/audits": {"get": {"security": [{"apiKey": []}, {"Permissions": ["jackpot"]}], "parameters": [{"in": "query", "name": "jackpotId", "type": "string", "required": false, "description": "Jackpot id"}, {"in": "query", "name": "type", "type": "string", "required": false, "description": "Audit type"}, {"in": "query", "name": "initiatorType", "type": "string", "required": false, "description": "Initiator type"}, {"in": "query", "name": "initiatorName", "type": "string", "required": false, "description": "Initiator name"}, {"in": "query", "name": "ip", "type": "string", "required": false, "description": "IP address"}, {"in": "query", "name": "ts__gte", "type": "number", "required": false, "description": "Timestamp from (in milliseconds since Unix Epoch, e.g. 1572614697688)"}, {"in": "query", "name": "ts__lte", "type": "number", "required": false, "description": "Timestamp to (in milliseconds since Unix Epoch, e.g. 1572614697688)"}, {"in": "query", "name": "limit", "type": "number", "required": false, "description": "Max number of requested items"}, {"in": "query", "name": "offset", "type": "number", "required": false, "description": "Offset of requested items"}], "tags": ["Model"], "summary": "Search jackpot audits", "responses": {"200": {"description": "Jackpot audits", "schema": {"type": "array", "items": {"$ref": "#/definitions/JackpotAudit"}}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/exchange": {"get": {"security": [{"apiKey": []}], "tags": ["Internal"], "summary": "Get exchange rates table", "responses": {"200": {"description": "Return exchange rates table", "schema": {"$ref": "#/definitions/ExchangeRates"}}, "500": {"description": "- 1: JPN internal server error\n"}}}}, "/exchange/{base_currency}/{target_currency}": {"parameters": [{"$ref": "#/parameters/base_currency"}, {"$ref": "#/parameters/target_currency"}], "get": {"security": [{"apiKey": []}], "tags": ["Internal"], "summary": "Return exchange rate between base_currency and target_currency", "responses": {"200": {"description": "Return exchange rate between base_currency and target_currency", "schema": {"$ref": "#/definitions/ExchangeResponse"}}, "400": {"description": "- 28: <PERSON><PERSON><PERSON><PERSON> not found\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/jackpots/{id}/game-action": {"post": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:instance", "jackpot:instance:edit"]}], "parameters": [{"$ref": "#/parameters/id"}, {"$ref": "#/parameters/initiator"}, {"name": "game action", "in": "body", "description": "Game specific action", "required": true, "schema": {"$ref": "#/definitions/GameAction"}}], "tags": ["Model"], "summary": "Perform game specific action", "responses": {"200": {"description": "Changed properties", "schema": {"$ref": "#/definitions/ChangedProperties"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}}, "parameters": {"name": {"name": "name", "in": "path", "type": "string", "description": "Measure name", "required": true}, "code": {"name": "code", "in": "path", "type": "string", "description": "region code", "required": true}, "type": {"name": "type", "in": "path", "type": "string", "description": "type name", "required": true}, "id": {"name": "id", "in": "path", "type": "string", "description": "Jackpot Id", "required": true}, "base_currency": {"name": "base_currency", "in": "path", "type": "string", "description": "Base currency code", "required": true}, "target_currency": {"name": "target_currency", "in": "path", "type": "string", "description": "Target currency code", "required": true}, "currency": {"name": "currency", "in": "query", "type": "string", "description": "currency code to represent result in.", "required": false}, "pool_id": {"name": "pool_id", "in": "path", "type": "string", "description": "Pool Id", "required": true}, "names": {"name": "names", "in": "query", "type": "string", "description": "Comma separated jackpot type names to return. (All by default)", "required": false}, "initiator": {"name": "initiator", "in": "query", "type": "string", "description": "Initiator of jackpot changes", "required": false}}, "definitions": {"PlayerInformation": {"type": "object", "properties": {"playerCode": {"type": "string", "description": "Player Code (Id)", "example": "Player_01"}, "brandId": {"type": "integer", "description": "Brand Entity Id", "example": 42}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "gameCode": {"type": "string", "description": "Game code", "example": "game1"}}}, "AuthRequest": {"allOf": [{"$ref": "#/definitions/PlayerInformation"}, {"type": "object", "required": ["jackpotIds"], "properties": {"jackpotIds": {"type": "array", "description": "List of jackpot instance ids", "items": {"type": "string"}}}}]}, "AuthResponse": {"type": "object", "required": ["token", "jackpots"], "properties": {"token": {"type": "string", "description": "Access token", "example": "cmQzgyMiwiaXNzIjoic2t5d2luZGdyb3VwIn0.fR_..."}, "jackpots": {"type": "array", "items": [{"$ref": "#/definitions/JackpotShortInfo"}], "description": "Jackpot short info list", "example": [{"id": "mega", "currency": "EUR", "jpGameId": "mega_jp_id", "isTest": false}]}}}, "JackpotShortInfo": {"type": "object", "required": ["id", "currency", "jpGameId"], "properties": {"id": {"type": "string", "description": "Jackpot Id", "example": "mega_jp_id"}, "currency": {"type": "string", "description": "Jackpoi main currency", "example": "EUR"}, "jpGameId": {"type": "string", "description": "Jackpot Game Id", "example": "mega"}}}, "TickerInformationResponse": {"type": "object", "required": ["jackpotId", "pools"], "properties": {"jackpotId": {"type": "string", "description": "Jackpot Instance Id", "example": "123"}, "pools": {"$ref": "#/definitions/JackpotPoolsInformation"}}}, "JackpotPoolInformation": {"type": "object", "required": ["amount"], "properties": {"amount": {"type": "number", "description": "Current pool amount", "example": 10.52}, "info": {"type": "object", "description": "Additional information", "example": {"bet": {"$gt": 10}}}}}, "JackpotPoolsInformation": {"type": "object", "additionalProperties": {"$ref": "#/definitions/JackpotPoolInformation"}}, "PoolStateResponse": {"type": "object", "required": ["seed", "progressive", "initialSeed"], "properties": {"seed": {"type": "number", "description": "Jackpot seed", "example": 12345}, "progressive": {"type": "number", "description": "Jackpot progressive", "example": 12345}, "initialSeed": {"type": "number", "description": "Jackpot initial seed", "example": 12345}}}, "AllPoolsStateResponse": {"type": "array", "items": {"$ref": "#/definitions/PoolStateResponse"}}, "UpdatePoolResponse": {"type": "object"}, "PoolDepositResponse": {"type": "object"}, "TransactionIdResponse": {"type": "object", "required": ["transactionId"], "properties": {"transactionId": {"type": "string", "description": "Transaction Id", "example": "cmQzgyMiwiaXNzIjoic2t5d2luZGdyb3VwIn0..."}}}, "TransactionIdBatch": {"type": "array", "items": {"type": "string", "description": "Transaction Id", "example": "cmQzgyMiwiaXNzIjoic2t5d2luZGdyb3VwIn0..."}}, "CheckWinRequest": {"type": "object", "required": ["transactionId", "roundId"], "properties": {"transactionId": {"type": "string", "description": "Transaction Id generated by GET /contribute/transactionId", "example": "cmQzgyMiwiaXNzIjoic2t5d2luZGdyb3VwIn"}, "externalId": {"type": "string", "description": "External Id", "example": "1234..."}, "cheats": {"type": "array", "items": {"type": "integer"}, "description": "Reel position for cheating", "example": [1, 2, 3, 4]}, "exchangeRates": {"type": "object", "description": "Custom exchange rates from player currency.", "example": {"USD": 1.25, "CNY": 0.13}}, "roundId": {"type": "string", "description": "Round id", "example": 120089}}, "additionalProperties": {"type": "object", "description": "Jackpot data payload"}}, "CheckWinResponse": {"type": "object", "required": ["events"], "properties": {"events": {"type": "array", "items": {"$ref": "#/definitions/JackpotEvent"}}}}, "BaseRequest": {"type": "object", "required": ["transactionId", "roundId"], "properties": {"transactionId": {"type": "string", "description": "Transaction Id generated by GET /contribute/transactionId", "example": "cmQzgyMiwiaXNzIjoic2t5d2luZGdyb3VwIn"}, "externalId": {"type": "string", "description": "External Id", "example": "1234..."}, "exchangeRates": {"type": "object", "description": "Custom exchange rates from player currency.", "example": {"USD": 1.25, "CNY": 0.13}}, "roundId": {"type": "string", "description": "Round id", "example": 120089}}}, "ContributionRequest": {"allOf": [{"$ref": "#/definitions/BaseRequest"}, {"type": "object", "required": ["amount"], "properties": {"amount": {"type": "number", "description": "Amount to comtribute", "example": 10.52}, "cheats": {"type": "array", "items": {"type": "integer"}, "description": "Reel position for cheating", "example": [1, 2, 3, 4]}}, "additionalProperties": {"type": "object", "description": "Jackpot data payload"}}]}, "DeferredContributionRequest": {"type": "object", "required": ["amount", "roundId"], "properties": {"amount": {"type": "number", "description": "Amount to comtribute", "example": 10.52}, "cheats": {"type": "array", "items": {"type": "integer"}, "description": "Reel position for cheating", "example": [1, 2, 3, 4]}, "roundId": {"type": "string", "description": "Round id", "example": 120089}, "externalId": {"type": "string", "description": "External Id", "example": "1234..."}, "exchangeRates": {"type": "object", "description": "Custom exchange rates from player currency.", "example": {"USD": 1.25, "CNY": 0.13}}}, "additionalProperties": {"type": "object", "description": "Jackpot data payload"}}, "DeferredContributionResponse": {"type": "object", "required": ["transactionId", "result"], "properties": {"transactionId": {"type": "string", "example": "cmQzgyMiwiaXNzIjoic2t5d2luZGdyb3VwIn"}, "result": {"type": "string", "description": "JWT encoded contribution data", "example": "eyJhbGciOiJIUzI1..."}}}, "ContributionResponse": {"type": "object", "required": ["events"], "properties": {"events": {"type": "array", "items": {"$ref": "#/definitions/JackpotEvent"}}}}, "JackpotEvent": {"type": "object", "required": ["type", "jackpotId"], "properties": {"type": {"type": "string", "description": "Event type", "enum": ["contribution", "start-mini-game", "end-mini-game", "win", "win-confirm"], "example": "win"}, "jackpotId": {"type": "string", "description": "Jackpot Instance Id", "example": "small_jackpot"}}}, "MiniGameRequest": {"allOf": [{"$ref": "#/definitions/BaseRequest"}]}, "MiniGameResponse": {"type": "object", "required": ["events"], "properties": {"events": {"type": "array", "items": {"$ref": "#/definitions/JackpotEvent"}}}}, "WinConfirmRequest": {"type": "object", "required": ["transactionId", "jackpotId"], "properties": {"transactionId": {"type": "string", "description": "TransactionId generated by GET /contribute/transactionId", "example": "cmQzgyMiwiaXNzIjoic2t5d2luZGdyb3VwIn"}, "jackpotId": {"type": "string", "description": "Jackpot Id", "example": "small_jackpot"}, "exchangeRates": {"type": "object", "description": "Custom exchange rates from player currency.", "example": {"USD": 1.25, "CNY": 0.13}}}}, "WinConfirmResponse": {"type": "object", "required": ["events"], "properties": {"events": {"type": "array", "items": {"$ref": "#/definitions/JackpotEvent"}}}}, "WinJackpotRequest": {"allOf": [{"$ref": "#/definitions/BaseRequest"}, {"type": "object", "required": ["jackpotId"], "properties": {"jackpotId": {"type": "string", "description": "JackpotId", "example": "small_jackpot"}, "totalBet": {"type": "number", "description": "Game specific setting. Required in Water Reel, Metal Reel, Fire Reel.", "example": 10}}, "additionalProperties": {"type": "object"}}]}, "WinJackpotResponse": {"type": "object", "required": ["events"], "properties": {"events": {"type": "array", "items": {"$ref": "#/definitions/JackpotEvent"}}}}, "JackpotType": {"type": "object", "required": ["name", "jpGameId", "definition"], "properties": {"name": {"type": "string", "description": "Type name", "example": "Type1"}, "jpGameId": {"type": "string", "description": "Jackpot game Id (defined in sw-jpn-games module)", "example": "Jackpot_game_1"}, "definition": {"$ref": "#/definitions/JackpotDefinition"}, "configurable": {"type": "boolean", "description": "Indicates if jackpot type can be reconfigured via API", "example": false}, "overridable": {"type": "boolean", "description": "Indicates if jackpot type definition can be overwritten on instance level", "example": false}, "canBeDisabled": {"type": "boolean", "description": "Indicates if jackpot instance of this type can be disabled", "example": false}, "supportsWinCap": {"type": "boolean", "description": "Indicates if jackpot instance can supports jackpot win cap (Greece jurisdiction)", "example": false}}}, "JackpotDefinition": {"type": "object", "required": ["currency", "list"], "properties": {"winCapping": {"type": "number", "description": "Jackpot win cap", "example": 500}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "list": {"type": "array", "items": {"$ref": "#/definitions/JackpotPoolDefinition"}}, "pendingList": {"type": "array", "items": {"$ref": "#/definitions/JackpotPoolDefinition"}}}}, "JackpotPoolDefinition": {"type": "object", "required": ["id", "seed", "contribution"], "properties": {"id": {"type": "string", "description": "Pool Id", "example": 42}, "seed": {"$ref": "#/definitions/JackpotSeedDefinition"}, "contribution": {"type": "array", "items": {"$ref": "#/definitions/JackpotContributionDefinition"}}, "info": {"type": "object", "description": "Additional pool information", "example": {}}}}, "JackpotSeedDefinition": {"type": "object", "required": ["amount"], "properties": {"amount": {"type": "number", "description": "Initial seed amount", "example": 10.53}}}, "JackpotContributionDefinition": {"type": "object", "required": ["seed", "progressive"], "properties": {"seed": {"type": "number", "description": "Percent of contribution to seed pot", "example": 0.102}, "progressive": {"type": "number", "description": "Percent of contribution to progressive pot", "example": 0.123}, "condition": {"type": "object", "description": "Condition of contribution to the pool with defined percents", "example": {"seed": {"$lt": 200}, "bet": {"$gt": 10}}}}}, "JackpotInstanceInfo": {"type": "object", "properties": {"externalId": {"type": "string", "description": "Jackpot instance external id", "example": "jackpotId_hash"}, "externalStartDate": {"type": "string", "description": "Jackpot instance external start date in ISO 8601 timestamp (e.g. 2023-09-7T16:47:38.887Z)", "example": "2023-09-07T16:47:38.887Z"}}}, "JackpotInstanceRequest": {"type": "object", "required": ["id", "type"], "properties": {"id": {"type": "string", "description": "Jackpot Id", "example": "jackpot_1"}, "type": {"type": "string", "description": "Jackpot type", "example": "small"}, "regionCode": {"type": "string", "description": "Jackpot region", "example": "eu"}, "isTest": {"type": "boolean", "description": "Flag that determines whether the jackpot is test", "example": true}, "isGlobal": {"type": "boolean", "description": "Flag that indicates whether the jackpot is global (cross-site)", "example": true}, "isOwned": {"type": "boolean", "description": "Flag that indicates whether the jackpot is owned by some operator", "example": true}, "isLocal": {"type": "boolean", "description": "Flag that indicates whether the jackpot is owned by just one operator", "example": true}, "jackpotConfigurationLevel": {"type": "integer", "description": "Jackpot configuration level", "enum": [1, 2, 3, 4, 5, 6], "example": 1}, "entityId": {"type": "integer", "description": "The entity id for which the jackpot configuration level applies", "example": 1119}, "jurisdictionCode": {"type": "string", "description": "The jurisdiction code where the jackpot configuration level applies", "example": "UK"}}}, "JackpotInstance": {"type": "object", "required": ["id", "type"], "properties": {"id": {"type": "string", "description": "Jackpot Id", "example": "jackpot_1"}, "type": {"type": "string", "description": "Jackpot type", "example": "small"}, "jpGameId": {"type": "string", "readOnly": true, "description": "Jackpot game Id", "example": "jp_game_1"}, "definition": {"description": "Definition from jackpot type", "$ref": "#/definitions/JackpotDefinition"}, "regionCode": {"type": "string", "description": "Jackpot region", "example": "eu"}, "isTest": {"type": "boolean", "description": "Flag that determines whether the jackpot is test", "example": true}, "isLocal": {"type": "boolean", "description": "Flag that indicates whether the jackpot is owned by just one operator", "example": true}, "isGlobal": {"type": "boolean", "description": "Flag that indicates whether the jackpot is global", "example": true}, "jackpotConfigurationLevel": {"type": "integer", "description": "Jackpot configuration level", "enum": [1, 2, 3, 4, 5, 6], "example": 1}, "entityId": {"type": "integer", "description": "The entity id for which the jackpot configuration level applies", "example": 1119}, "jurisdictionCode": {"type": "string", "description": "The jurisdiction code where the jackpot configuration level applies", "example": "UK"}, "info": {"description": "Jackpot instance info", "$ref": "#/definitions/JackpotInstanceInfo"}}}, "JackpotInformation": {"type": "object", "required": ["id", "currency", "pools"], "properties": {"id": {"type": "string", "description": "Jackpot Id", "example": "small_jackpot"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "pools": {"$ref": "#/definitions/JackpotPoolsInformation"}}}, "JackpotRegion": {"type": "object", "required": ["code", "url"], "properties": {"code": {"type": "string", "description": "Region code", "example": "eu"}, "url": {"type": "string", "description": "Region http(s) url", "example": "http://jpn.server.api:5000"}, "secureOptions": {"type": "object", "description": "SSL certificates or other secure options", "example": {"key": "-----BEGIN DSA PRIVATE KEY-----\n...", "cert": "-----BEGIN CERTIFICATE-----\n...", "ca": "-----BEGIN CERTIFICATE-----\n...", "passphrase": "pass123"}}}}, "JackpotAudit": {"type": "object", "properties": {"jackpotId": {"type": "string", "description": "Jackpot id", "example": "OMQ-JP-IPM"}, "type": {"enum": ["create", "update", "archive", "disable", "enable", "type-update"], "description": "Audit type"}, "ts": {"type": "string", "description": "Timestamp"}, "history": {"type": "object", "description": "Jackpot change history", "example": {"id": "ID", "type": "type", "definition": {}}}, "initiatorType": {"enum": ["user", "system"], "description": "Initiator type"}, "initiatorName": {"type": "string", "description": "Initiator name", "example": "Somebody"}, "ip": {"type": "string", "description": "IP address", "example": "127.0.0.1"}, "userAgent": {"type": "string", "description": "User agent", "example": "Chrome"}}}, "ExchangeRates": {"type": "object", "required": ["provider", "ts", "startTime", "endTime", "rates"], "properties": {"provider": {"description": "Provider type", "enum": ["oxr", "oanda", "default"]}, "ts": {"type": "integer", "description": "Last update timestamp", "example": 1375432612423}, "startTime": {"type": "integer", "description": "Time of currency rates creation", "example": 1375432612423}, "endTime": {"type": "integer", "description": "Exchange rates is expiring at this time", "example": 1375432612423}, "rates": {"type": "object", "example": {"Base rate": {"EUR": 1.2343, "GBP": 3.23212}}, "description": "Rates table"}}}, "ExchangeResponse": {"type": "object", "required": ["rate"], "properties": {"rate": {"type": "number", "description": "Exchange rate", "example": 1.23242654}}}, "ProgressiveTransferRequest": {"type": "object", "required": ["transactionId", "fromPoolId", "toPoolId"], "properties": {"transactionId": {"type": "string", "description": "TransactionId generated by GET /pools/transfer/transactionId", "example": "cmQzgyMiwiaXNzIjoic2t5d2luZGdyb3VwIn"}, "fromPoolId": {"type": "string", "description": "Donor pool Id", "example": "challenge_pool"}, "toPoolId": {"type": "string", "description": "Recipient pool Id", "example": "prize_pool"}, "amount": {"type": "number", "description": "Amount of progressive to transfer, if undefined all amount from donor pool will be charged", "example": 18}}}, "PoolDepositRequest": {"type": "object", "required": ["transactionId"], "properties": {"transactionId": {"type": "string", "description": "TransactionId generated by GET /pools/transfer/transactionId", "example": "cmQzgyMiwiaXNzIjoic2t5d2luZGdyb3VwIn"}, "seed": {"type": "number", "description": "seed amount to deposit", "example": 1234}, "progressive": {"type": "number", "description": "progressive amount to deposite", "example": 987}}}, "UpdatePoolRequest": {"type": "object", "properties": {"seed": {"type": "number", "description": "seed amount to update", "example": 1001}, "progressive": {"type": "number", "description": "progressive amount to update", "example": 0}}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "number", "description": "Error code", "example": 42}, "message": {"type": "string", "description": "Error message", "example": "Something went wrong"}}}, "GameAction": {"type": "object", "required": ["action", "payload"], "properties": {"action": {"type": "string", "description": "action name", "example": "stop-pools"}, "payload": {"type": "object", "description": "Specific data JSON", "example": "{'pools': ['hourly-1', 'daily-1']}"}}}, "ChangedProperties": {"type": "array", "items": {"type": "string"}, "example": ["hourly-1.state", "grand-1.state"], "description": "Array of changed pool.property values"}}}