######################### SONAR STUFF #########################
FROM node:22.14.0-bullseye as sonar

# Install dependencies
RUN apt-get -yqq --no-install-recommends install curl unzip

WORKDIR /app
COPY . /app/

RUN npm install --unsafe-perm \
    && npm run clean \
    && npm run compile \
    && npm run version

CMD ["node", "dist/skywind/app"]

######################### MAIN IMAGE #########################
FROM node:22.14.0-alpine as main
EXPOSE 5000

WORKDIR /app
COPY --chown=node:node --from=sonar /app .

RUN rm -rf coverage .nyc_output && npm cache clean --force && rm .npmrc

USER node
CMD ["node", "--max-http-header-size=81000", "dist/skywind/app"]
