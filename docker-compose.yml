version: '3.4'
services:
  jpn:
    image: ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED}
    build:
      target: main
      context: .
      dockerfile: Dockerfile
    depends_on:
      - sonar
  db:
    image: postgres:10
    ports:
      - 5432
    environment:
      POSTGRES_HOST_AUTH_METHOD: trust
  redis:
    image: redis:alpine
    ports:
      - 6379
  sonar:
    image: ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED}-sonar
    build:
      target: sonar
      context: .
      dockerfile: Dockerfile
    links:
      - db
      - redis
    environment:
      PGUSER: postgres
      GS_PGUSER: postgres
      PGDATABASE: postgres
      GS_PGDATABASE: postgres
      SONAR_HOST_URL: "${SONAR_HOST_URL}"
    command: sh -c "npm run test && npm run sonar"
