{"swagger": "2.0", "info": {"description": "Skywind - Galaxy Pro - Ticker API", "version": "4.103", "title": "Skywind - Galaxy Pro - Ticker API"}, "basePath": "/v1/", "schemes": ["http", "https"], "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Basic JWT authorization", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-Access-Token", "in": "header"}, "Permissions": {"description": "Dummy OAuth2 authorization for permissions through scopes", "type": "oauth2", "authorizationUrl": "http://localhost:3000/oauth/dialog", "tokenUrl": "http://localhost:3000/oauth/token", "flow": "accessCode", "scopes": {}}}, "paths": {"/ticker": {"get": {"tags": ["Ticker"], "summary": "Get tickers of all jackpots player is authorized", "parameters": [{"in": "query", "name": "currency", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "required": true}, {"in": "query", "name": "jackpotIds", "type": "array", "description": "List of jackpot instance ids", "items": {"type": "string"}, "required": true}, {"in": "query", "name": "skip<PERSON><PERSON><PERSON>", "type": "boolean", "description": "Skip errors if jackpot not found and etc.", "required": false}], "responses": {"200": {"description": "200 Return jackpot ticker", "schema": {"type": "array", "items": {"$ref": "#/definitions/TickerInformationResponse"}}}, "400": {"description": "- 3: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/version": {"get": {"tags": ["Version"], "summary": "Checks service version", "responses": {"200": {"description": "Returns version, revision and time of build", "schema": {"type": "string", "example": "1.1.1 6ca78adf 01.01.1970 00:00:00"}}}}}, "/health": {"get": {"tags": ["Health"], "summary": "Checks server health", "responses": {"200": {"description": "Health check OK"}}}}}, "definitions": {"TickerInformationResponse": {"type": "object", "required": ["jackpotId", "pools"], "properties": {"jackpotId": {"type": "string", "description": "Jackpot Instance Id", "example": "123"}, "pools": {"$ref": "#/definitions/JackpotPoolsInformation"}}}, "JackpotPoolInformation": {"type": "object", "required": ["amount"], "properties": {"amount": {"type": "number", "description": "Current pool amount", "example": 10.52}, "info": {"type": "object", "description": "Additional information", "example": {"bet": {"$gt": 10}}}}}, "JackpotPoolsInformation": {"type": "object", "additionalProperties": {"$ref": "#/definitions/JackpotPoolInformation"}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "number", "description": "Error code", "example": 42}, "message": {"type": "string", "description": "Error message", "example": "Something went wrong"}}}}}